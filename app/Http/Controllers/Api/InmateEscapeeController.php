<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\InmateEscapeeUpdateService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Gate;

class InmateEscapeeController extends Controller
{
    protected InmateEscapeeUpdateService $inmateUpdateService;

    public function __construct(InmateEscapeeUpdateService $inmateUpdateService)
    {
        $this->middleware('auth:sanctum');
        $this->inmateUpdateService = $inmateUpdateService;
    }

    /**
     * Get summary of inmate escapee statuses
     */
    public function statusSummary(): JsonResponse
    {
        if (!Gate::allows('view-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $summary = $this->inmateUpdateService->getStatusSummary();

        return response()->json([
            'success' => true,
            'data' => $summary,
        ]);
    }

    /**
     * Get inmates with mismatched escapee status
     */
    public function statusMismatches(): JsonResponse
    {
        if (!Gate::allows('view-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $mismatches = $this->inmateUpdateService->getStatusMismatches();

        return response()->json([
            'success' => true,
            'data' => $mismatches,
        ]);
    }

    /**
     * Fix status mismatches
     */
    public function fixMismatches(): JsonResponse
    {
        if (!Gate::allows('manage-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $result = $this->inmateUpdateService->fixStatusMismatches();

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fix status mismatches',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Sync all inmate escapee statuses
     */
    public function syncAll(): JsonResponse
    {
        if (!Gate::allows('manage-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $result = $this->inmateUpdateService->syncAllInmateEscapeeStatuses();

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync inmate statuses',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update specific inmate escapee status
     */
    public function updateStatus(Request $request): JsonResponse
    {
        if (!Gate::allows('manage-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'prisoner_no' => 'required|exists:inmates_all_class_register,prisoner_no',
            'is_escapee' => 'required|boolean',
            'is_recaptured' => 'required|boolean',
        ]);

        try {
            $success = $this->inmateUpdateService->updateInmateEscapeeStatus(
                $validated['prisoner_no'],
                $validated['is_escapee'],
                $validated['is_recaptured']
            );

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Inmate status updated successfully',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update inmate status',
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update inmate status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
