<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\StaffManagement\Http\Controllers\Api\V1\StaffController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('/staffmanagement', function (Request $request) {
//     return $request->user();
// });

Route::middleware(['auth:sanctum'])->group(function () {
    Route::prefix('v1')->group(function () {

        // Staff Management Routes with RBAC
        Route::middleware('authorize:any:view-staff,manage-staff,manage-all')->group(function () {
            Route::get('staff', [StaffController::class, 'index']);
            Route::get('staff/{staff}', [StaffController::class, 'show']);
        });

        Route::middleware('authorize:any:create-staff,manage-staff,manage-all')->group(function () {
            Route::post('staff', [StaffController::class, 'store']);
        });

        Route::middleware('authorize:any:update-staff,manage-staff,manage-all')->group(function () {
            Route::put('staff/{staff}', [StaffController::class, 'update']);
            Route::patch('staff/{staff}', [StaffController::class, 'update']);
            Route::patch('staff/{id}/deactivate', [StaffController::class, 'deactivate'])->name('staff.deactivate');
            Route::patch('staff/{id}/reactivate', [StaffController::class, 'reactivate'])->name('staff.reactivate');
        });

        Route::middleware('authorize:any:delete-staff,manage-staff,manage-all')->group(function () {
            Route::delete('staff/{staff}', [StaffController::class, 'destroy']);
        });
    });
});

