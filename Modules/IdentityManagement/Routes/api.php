<?php

use Illuminate\Support\Facades\Route;
use Modules\IdentityManagement\Http\Controllers\Api\v1\ATPController;
use Modules\IdentityManagement\Http\Controllers\Api\v1\ConvictController;
use Modules\IdentityManagement\Http\Controllers\Api\v1\InmateController;
use Modules\IdentityManagement\Http\Controllers\Api\v1\InmateDashboardController;
use Modules\IdentityManagement\Http\Controllers\Api\v1\WarrantController;
use Modules\IdentityManagement\Http\Controllers\Api\v1\ScannerConnectionController;
use Modules\IdentityManagement\Http\Controllers\Api\v1\PrisonerBiometricController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('/identitymanagement', function (Request $request) {
//     return $request->user();
// });

Route::prefix('v1')->group(function () {

    Route::middleware(['auth:sanctum', 'universal.auth'])->group(function () {

        // Inmate Management Routes with new RBAC
        Route::middleware('authorize:any:view-inmates,manage-inmates,manage-all')->group(function () {
            Route::get('inmates', [InmateController::class, 'index']);
            Route::get('inmates/{inmate}', [InmateController::class, 'show']);
            Route::get('inmates_search', [InmateController::class, 'search'])->name('inmates_search');
            Route::get('inmates_on_remand', [InmateController::class, 'onRemand'])->name('inmates_on_remand');
            Route::get('transferred_inmates', [InmateController::class, 'transferred'])->name('transferred_inmates');
            Route::get('lifers', [InmateController::class, 'lifers'])->name('lifers');
            Route::get('death_roll_inmates', [InmateController::class, 'deathRoll'])->name('death_roll');
            Route::get('released_or_discharged', [InmateController::class, 'releasedOrDischarged'])->name('released_or_discharged');
            Route::get('most_recent_admission', [InmateController::class, 'mostRecentAdmission'])->name('most_recent_admission');
            Route::get('most_recent_release_discharge_transfer', [InmateController::class, 'mostRecentReleaseOrDischargeOrTransfer'])->name('most_recent_release_discharge_transfer');
            Route::get('duration_classification', [InmateController::class, 'durationClassification'])->name('duration_classification');
            Route::get('acja', [InmateController::class, 'acja']);
        });

        Route::middleware('authorize:any:create-inmates,manage-inmates,manage-all')->group(function () {
            Route::post('inmates', [InmateController::class, 'store']);
            Route::post('verify_selected', [InmateController::class, 'verifySelectedInmates'])->name('verify_selected');
        });

        Route::middleware('authorize:any:update-inmates,manage-inmates,manage-all')->group(function () {
            Route::put('inmates/{inmate}', [InmateController::class, 'update']);
            Route::patch('inmates/{inmate}', [InmateController::class, 'update']);
            Route::post('batch_release', [InmateController::class, 'doBatchRelease'])->name('batch_release');
            Route::post('batch_transfer', [InmateController::class, 'doBatchTransfer'])->name('batch_transfer');
        });

        Route::middleware('authorize:any:delete-inmates,manage-inmates,manage-all')->group(function () {
            Route::delete('inmates/{inmate}', [InmateController::class, 'destroy']);
        });

        // ATP (Awaiting Trial Prisoners) Routes
        Route::middleware('authorize:any:view-inmates,manage-inmates,manage-all')->group(function () {
            Route::get('awaiting', [ATPController::class, 'index']);
            Route::get('awaiting/{awaiting}', [ATPController::class, 'show']);
        });

        Route::middleware('authorize:any:create-inmates,manage-inmates,manage-all')->group(function () {
            Route::post('awaiting', [ATPController::class, 'store']);
        });

        Route::middleware('authorize:any:update-inmates,manage-inmates,manage-all')->group(function () {
            Route::put('awaiting/{awaiting}', [ATPController::class, 'update']);
            Route::patch('awaiting/{awaiting}', [ATPController::class, 'update']);
        });

        Route::middleware('authorize:any:delete-inmates,manage-inmates,manage-all')->group(function () {
            Route::delete('awaiting/{awaiting}', [ATPController::class, 'destroy']);
        });

        // Convicted Prisoners Routes
        Route::middleware('authorize:any:view-inmates,manage-inmates,manage-all')->group(function () {
            Route::get('convicted', [ConvictController::class, 'index']);
            Route::get('convicted/{convicted}', [ConvictController::class, 'show']);
        });

        Route::middleware('authorize:any:create-inmates,manage-inmates,manage-all')->group(function () {
            Route::post('convicted', [ConvictController::class, 'store']);
        });

        Route::middleware('authorize:any:update-inmates,manage-inmates,manage-all')->group(function () {
            Route::put('convicted/{convicted}', [ConvictController::class, 'update']);
            Route::patch('convicted/{convicted}', [ConvictController::class, 'update']);
        });

        Route::middleware('authorize:any:delete-inmates,manage-inmates,manage-all')->group(function () {
            Route::delete('convicted/{convicted}', [ConvictController::class, 'destroy']);
        });

        // Warrant Routes
        Route::middleware('authorize:any:view-warrants,manage-warrants,manage-inmates,manage-all')->group(function () {
            Route::get('warrant', [WarrantController::class, 'index']);
            Route::get('warrant/{warrant}', [WarrantController::class, 'show']);
        });

        Route::middleware('authorize:any:create-warrants,manage-warrants,manage-inmates,manage-all')->group(function () {
            Route::post('warrant', [WarrantController::class, 'store']);
        });

        Route::middleware('authorize:any:update-warrants,manage-warrants,manage-inmates,manage-all')->group(function () {
            Route::put('warrant/{warrant}', [WarrantController::class, 'update']);
            Route::patch('warrant/{warrant}', [WarrantController::class, 'update']);
        });

        Route::middleware('authorize:any:delete-warrants,manage-warrants,manage-inmates,manage-all')->group(function () {
            Route::delete('warrant/{warrant}', [WarrantController::class, 'destroy']);
        });

        Route::get('get_timeless_stats', [InmateDashboardController::class, 'index'])->name('get_timeless_stats.index');

        Route::get('get_periodic_stats', [InmateDashboardController::class, 'periodic_stats'])->name('periodic_stats');

        Route::apiResource('scanner-connections', ScannerConnectionController::class);

        Route::get('scanner-connections/last', [ScannerConnectionController::class, 'last'])->name('scanner-connections.last');

        Route::get('get_biometrics', [PrisonerBiometricController::class, 'fetchBiometrics'])->name('fetch_biometrics');

        Route::post('transfer_biometrics', [PrisonerBiometricController::class, 'transferBiometrics'])->name('transfer_biometrics');

        Route::patch('inmates/{id}/update-biometrics', [InmateController::class, 'updateBiometrics']);
    });
});
