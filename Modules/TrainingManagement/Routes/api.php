<?php

use Illuminate\Support\Facades\Route;
use Modules\TrainingManagement\Http\Controllers\Api\V1\TrainingProgramController;
use Modules\TrainingManagement\Http\Controllers\Api\V1\EnrollmentController;
use Modules\TrainingManagement\Http\Controllers\Api\V1\ProductivityLogController;
use Modules\TrainingManagement\Http\Controllers\Api\V1\ProductionProductController;
use Modules\TrainingManagement\Http\Controllers\Api\V1\ReportController;
use Modules\TrainingManagement\Http\Controllers\Api\V1\SkillsTrackingController;
use Modules\TrainingManagement\Http\Controllers\Api\V1\JobPlacementController;
use Modules\TrainingManagement\Http\Controllers\Api\V1\RecidivismController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('v1')->group(function () {

    Route::middleware(['auth:sanctum'])->group(function () {

        // Training Programs Routes with new RBAC
        Route::middleware('authorize:any:view-training-programs,manage-training-programs,manage-all-training')->group(function () {
            Route::get('training-programs', [TrainingProgramController::class, 'index']);
            Route::get('training-programs/{trainingProgram}', [TrainingProgramController::class, 'show']);
            Route::get('training-programs/{id}/enrollments', [TrainingProgramController::class, 'enrollments']);
        });

        Route::middleware('authorize:any:create-training-programs,manage-training-programs,manage-all-training')->group(function () {
            Route::post('training-programs', [TrainingProgramController::class, 'store']);
        });

        Route::middleware('authorize:any:update-training-programs,manage-training-programs,manage-all-training')->group(function () {
            Route::put('training-programs/{trainingProgram}', [TrainingProgramController::class, 'update']);
            Route::patch('training-programs/{trainingProgram}', [TrainingProgramController::class, 'update']);
        });

        Route::middleware('authorize:any:delete-training-programs,manage-training-programs,manage-all-training')->group(function () {
            Route::delete('training-programs/{trainingProgram}', [TrainingProgramController::class, 'destroy']);
        });

        // Training Program Statistics and Management
        Route::middleware('authorize:any:view-training-reports,manage-training-programs,manage-all-training')->group(function () {
            Route::get('training-programs/statistics', [TrainingProgramController::class, 'statistics']);
            Route::get('training-programs-statistics', [TrainingProgramController::class, 'statistics']); // Legacy support
        });

        Route::middleware('authorize:any:manage-training-enrollments,manage-training-programs,manage-all-training')->group(function () {
            Route::post('training-programs/{trainingProgram}/enrollments', [TrainingProgramController::class, 'manageEnrollments']);
        });

        // Enrollments with RBAC
        Route::middleware('authorize:any:view-training-enrollments,manage-training-enrollments,manage-all-training')->group(function () {
            Route::get('enrollments', [EnrollmentController::class, 'index']);
            Route::get('enrollments/{enrollment}', [EnrollmentController::class, 'show']);
        });

        Route::middleware('authorize:any:manage-training-enrollments,manage-training-programs,manage-all-training')->group(function () {
            Route::post('enrollments', [EnrollmentController::class, 'store']);
            Route::put('enrollments/{enrollment}', [EnrollmentController::class, 'update']);
            Route::patch('enrollments/{enrollment}', [EnrollmentController::class, 'update']);
            Route::delete('enrollments/{enrollment}', [EnrollmentController::class, 'destroy']);
            Route::patch('enrollments/{id}/complete', [EnrollmentController::class, 'complete']);
            Route::post('enrollments/bulk', [EnrollmentController::class, 'bulkEnroll']);
        });

        // Productivity Logs with RBAC
        Route::middleware('authorize:any:view-training-productivity,manage-training-productivity,manage-all-training')->group(function () {
            Route::get('productivity-logs', [ProductivityLogController::class, 'index']);
            Route::get('productivity-logs/{productivityLog}', [ProductivityLogController::class, 'show']);
        });

        Route::middleware('authorize:any:manage-training-productivity,manage-training-programs,manage-all-training')->group(function () {
            Route::post('productivity-logs', [ProductivityLogController::class, 'store']);
            Route::put('productivity-logs/{productivityLog}', [ProductivityLogController::class, 'update']);
            Route::patch('productivity-logs/{productivityLog}', [ProductivityLogController::class, 'update']);
            Route::delete('productivity-logs/{productivityLog}', [ProductivityLogController::class, 'destroy']);
            Route::post('productivity-logs/bulk', [ProductivityLogController::class, 'bulkStore']);
        });

        // Production Products with RBAC
        Route::middleware('authorize:any:view-training-production,manage-training-production,manage-all-training')->group(function () {
            Route::get('production-products', [ProductionProductController::class, 'index']);
            Route::get('production-products/{productionProduct}', [ProductionProductController::class, 'show']);
            Route::get('production-products/program/{programId}', [ProductionProductController::class, 'byProgram']);
            Route::get('production-products/{id}/statistics', [ProductionProductController::class, 'statistics']);
        });

        Route::middleware('authorize:any:manage-training-production,manage-training-programs,manage-all-training')->group(function () {
            Route::post('production-products', [ProductionProductController::class, 'store']);
            Route::put('production-products/{productionProduct}', [ProductionProductController::class, 'update']);
            Route::patch('production-products/{productionProduct}', [ProductionProductController::class, 'update']);
            Route::delete('production-products/{productionProduct}', [ProductionProductController::class, 'destroy']);
            Route::post('production-products/generate-sku', [ProductionProductController::class, 'generateSku']);
        });

        // Reports with RBAC
        Route::middleware('authorize:any:view-training-reports,manage-training-programs,manage-all-training')->group(function () {
            Route::get('reports/dashboard', [ReportController::class, 'dashboard']);
            Route::get('reports/productivity', [ReportController::class, 'productivity']);
            Route::get('reports/certifications', [ReportController::class, 'certifications']);
            Route::get('reports/program-effectiveness', [ReportController::class, 'programEffectiveness']);
            Route::get('reports/export-productivity', [ReportController::class, 'exportProductivity']);
        });

        // Skills Tracking with RBAC
        Route::middleware('authorize:any:view-training-skills,manage-training-skills,manage-all-training')->group(function () {
            Route::get('skills/catalog', [SkillsTrackingController::class, 'getSkillsCatalog']);
            Route::get('skills/inmate/{prisoner_no}', [SkillsTrackingController::class, 'getInmateSkills']);
            Route::get('skills/statistics', [SkillsTrackingController::class, 'getSkillsStatistics']);
            Route::get('skills/progression/{prisoner_no}', [SkillsTrackingController::class, 'getSkillProgression']);
        });

        Route::middleware('authorize:any:manage-training-skills,manage-training-programs,manage-all-training')->group(function () {
            Route::post('skills/record', [SkillsTrackingController::class, 'recordSkillAcquisition']);
            Route::put('skills/{skill_tracking_id}/proficiency', [SkillsTrackingController::class, 'updateSkillProficiency']);
            Route::post('skills/{skill_tracking_id}/certify', [SkillsTrackingController::class, 'awardCertification']);
        });

        // Job Placement Services with RBAC
        Route::middleware('authorize:any:view-training-jobs,manage-training-jobs,manage-all-training')->group(function () {
            Route::get('jobs/opportunities', [JobPlacementController::class, 'getJobOpportunities']);
            Route::get('jobs/placements', [JobPlacementController::class, 'getJobPlacements']);
            Route::get('jobs/statistics', [JobPlacementController::class, 'getPlacementStatistics']);
            Route::get('jobs/employers', [JobPlacementController::class, 'getEmployerPartners']);
        });

        Route::middleware('authorize:any:manage-training-jobs,manage-training-programs,manage-all-training')->group(function () {
            Route::post('jobs/applications', [JobPlacementController::class, 'createJobApplication']);
            Route::put('jobs/applications/{placement_id}/status', [JobPlacementController::class, 'updateApplicationStatus']);
            Route::post('jobs/match', [JobPlacementController::class, 'matchInmatesToJobs']);
        });

        // Recidivism Tracking with RBAC
        Route::middleware('authorize:any:view-training-recidivism,manage-training-recidivism,manage-all-training')->group(function () {
            Route::get('recidivism/analysis', [RecidivismController::class, 'getRecidivismAnalysis']);
            Route::get('recidivism/statistics', [RecidivismController::class, 'getRecidivismStatistics']);
            Route::get('recidivism/program-effectiveness', [RecidivismController::class, 'getProgramEffectivenessAnalysis']);
            Route::get('recidivism/comparative', [RecidivismController::class, 'getComparativeAnalysis']);
        });

        Route::middleware('authorize:any:manage-training-recidivism,manage-training-programs,manage-all-training')->group(function () {
            Route::post('recidivism/releases', [RecidivismController::class, 'recordRelease']);
            Route::post('recidivism/analysis', [RecidivismController::class, 'createRecidivismAnalysis']);
        });

    });
});
