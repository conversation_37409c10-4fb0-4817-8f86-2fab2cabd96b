<?php

namespace Modules\AdminManagement\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Database\Eloquent\Model;
use Modules\IdentityManagement\Entities\ATP;
use Modules\IdentityManagement\Entities\Inmate;
use Modules\IdentityManagement\Entities\Convict;
use Modules\AdminManagement\Entities\StateZoneAdmin;

class AdminManagementDatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();
        StateZoneAdmin::factory(10)->create();
        Inmate::factory(100)->create();
        ATP::factory(50)->create();
        Convict::factory(50)->create();
        
        // $this->call("OthersTableSeeder");
    }
}
