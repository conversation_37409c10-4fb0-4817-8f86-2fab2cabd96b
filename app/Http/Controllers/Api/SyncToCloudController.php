<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

class SyncToCloudController extends Controller
{
    protected $syncService;

    public function __construct(SyncToCloudService $syncService)
    {
        $this->syncService = $syncService;
    }

    /**
     * Upload vetted data to cloud
     */
    public function syncData(Request $request)
    {
        if (!Gate::allows('upload-to-cloud')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'tables'   => 'sometimes|array',
            'tables.*' => 'string',
        ]);

        $result = $this->syncService->uploadVettedData($validated);
        return response()->json($result);
    }

    /**
     * Get upload statistics for tables
     */
    public function getStatistics(Request $request)
    {
        if (!Gate::allows('view-cloud-statistics')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'tables'   => 'sometimes|array',
            'tables.*' => 'string',
        ]);

        $tables = $validated['tables'] ?? [
            'inmates_all_class_register',
            'inmates_atp_register',
            'inmates_convict_register',
            'inmates_sittings',
            'escort_duties',
            'legal_aid_firms',
            'inmate_legal_representative',
            'legal_representatives',
            'legal_rep_defaulters',
        ];

        $results = [];
        foreach ($tables as $table) {
            $results[$table] = $this->syncService->getTableStatistics($table);
        }

        return response()->json([
            'success' => true,
            'data'    => $results,
        ]);
    }

    /**
     * Retry failed uploads
     */
    public function retryFailedSync(Request $request)
    {
        if (!Gate::allows('retry-cloud-uploads')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'tables'   => 'sometimes|array',
            'tables.*' => 'string',
        ]);

        $result = $this->syncService->retryFailedUploads($validated);

        return response()->json($result);
    }

    /**
     * Verify table structure
     */
    public function verifyTableStructure(Request $request)
    {
        if (!Gate::allows('verify-cloud-structure')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'table' => 'required|string',
        ]);

        $result = $this->syncService->verifyTableStructure($validated['table']);

        return response()->json([
            'success' => $result['exists'] && $result['is_valid'],
            'data'    => $result,
        ]);
    }
}
