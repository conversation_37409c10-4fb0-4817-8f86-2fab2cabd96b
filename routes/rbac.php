<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\RbacController;

/*
|--------------------------------------------------------------------------
| RBAC API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for Role-Based Access Control (RBAC) management.
| These routes are protected by authentication and authorization middleware.
|
*/

Route::middleware(['auth:sanctum', 'rbac'])->prefix('rbac')->group(function () {
    
    // System Statistics
    Route::get('/stats', [RbacController::class, 'getStats'])
        ->middleware('rbac:,system-admin')
        ->name('rbac.stats');
    
    // Roles Management
    Route::get('/roles', [RbacController::class, 'getRoles'])
        ->middleware('rbac:,view-roles')
        ->name('rbac.roles.index');
    
    // Permissions Management
    Route::get('/permissions', [RbacController::class, 'getPermissions'])
        ->middleware('rbac:,view-permissions')
        ->name('rbac.permissions.index');
    
    Route::get('/permissions/module/{module}', [RbacController::class, 'getModulePermissions'])
        ->middleware('rbac:,view-permissions')
        ->name('rbac.permissions.module');
    
    // Account Types Management
    Route::get('/account-types', [RbacController::class, 'getAccountTypes'])
        ->middleware('rbac:,view-account-types')
        ->name('rbac.account-types.index');
    
    // Staff RBAC Management
    Route::prefix('staff/{staff}')->group(function () {
        
        // View staff permissions and roles
        Route::get('/permissions', [RbacController::class, 'getStaffPermissions'])
            ->name('rbac.staff.permissions');
        
        // Role assignment
        Route::post('/roles/assign', [RbacController::class, 'assignRole'])
            ->name('rbac.staff.roles.assign');
        
        Route::delete('/roles/remove', [RbacController::class, 'removeRole'])
            ->name('rbac.staff.roles.remove');
        
        Route::put('/roles/sync', [RbacController::class, 'syncStaffRoles'])
            ->name('rbac.staff.roles.sync');
        
        // Available roles for staff
        Route::get('/roles/available', [RbacController::class, 'getAvailableRoles'])
            ->name('rbac.staff.roles.available');
        
        // Permission checking
        Route::post('/check-permission', [RbacController::class, 'checkPermission'])
            ->name('rbac.staff.check-permission');
        
        // Cache management
        Route::delete('/cache', [RbacController::class, 'clearCache'])
            ->name('rbac.staff.cache.clear');
        
        // Account type management
        Route::put('/account-type', [RbacController::class, 'updateAccountType'])
            ->name('rbac.staff.account-type.update');
    });
    
    // Bulk operations
    Route::prefix('bulk')->group(function () {
        
        // Bulk role assignment
        Route::post('/assign-roles', function (Request $request) {
            // Implementation for bulk role assignment
            $request->validate([
                'staff_ids' => 'required|array',
                'staff_ids.*' => 'integer|exists:staff,id',
                'role_assignments' => 'required|array'
            ]);
            
            // This would be implemented in the controller
            return response()->json([
                'success' => true,
                'message' => 'Bulk role assignment completed'
            ]);
        })->middleware('rbac:,system-admin')
          ->name('rbac.bulk.assign-roles');
        
        // Bulk permission cache clearing
        Route::delete('/clear-cache', function (Request $request) {
            $request->validate([
                'staff_ids' => 'array',
                'staff_ids.*' => 'integer|exists:staff,id'
            ]);
            
            $staffIds = $request->staff_ids ?? [];
            
            if (empty($staffIds)) {
                // Clear all caches
                \Illuminate\Support\Facades\Cache::flush();
            } else {
                // Clear specific staff caches
                foreach ($staffIds as $staffId) {
                    \Illuminate\Support\Facades\Cache::forget("staff_permissions_{$staffId}");
                    \Illuminate\Support\Facades\Cache::forget("staff_roles_{$staffId}");
                }
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Cache cleared successfully'
            ]);
        })->middleware('rbac:,system-admin')
          ->name('rbac.bulk.clear-cache');
    });
    
    // System health and diagnostics
    Route::prefix('system')->group(function () {
        
        // Check system integrity
        Route::get('/health', function () {
            $health = [
                'database' => \Illuminate\Support\Facades\DB::connection()->getPdo() ? 'connected' : 'disconnected',
                'cache' => \Illuminate\Support\Facades\Cache::store()->getStore() ? 'available' : 'unavailable',
                'tables' => [
                    'roles' => \Illuminate\Support\Facades\Schema::hasTable('roles'),
                    'permissions' => \Illuminate\Support\Facades\Schema::hasTable('permissions'),
                    'account_types' => \Illuminate\Support\Facades\Schema::hasTable('account_types'),
                    'staff_roles' => \Illuminate\Support\Facades\Schema::hasTable('staff_roles'),
                    'role_permissions' => \Illuminate\Support\Facades\Schema::hasTable('role_permissions')
                ],
                'models' => [
                    'Role' => class_exists('App\\Models\\Role'),
                    'Permission' => class_exists('App\\Models\\Permission'),
                    'AccountType' => class_exists('App\\Models\\AccountType'),
                    'Staff' => class_exists('App\\Models\\Staff')
                ]
            ];
            
            return response()->json([
                'success' => true,
                'data' => $health
            ]);
        })->middleware('rbac:,system-admin')
          ->name('rbac.system.health');
        
        // Validate RBAC configuration
        Route::get('/validate', function () {
            $validation = [
                'orphaned_roles' => \App\Models\Role::doesntHave('permissions')->count(),
                'inactive_roles' => \App\Models\Role::where('is_active', false)->count(),
                'inactive_permissions' => \App\Models\Permission::where('is_active', false)->count(),
                'staff_without_roles' => \App\Models\Staff::doesntHave('roles')->count(),
                'expired_role_assignments' => \App\Models\Staff::whereHas('roles', function ($query) {
                    $query->where('expires_at', '<', now());
                })->count()
            ];
            
            return response()->json([
                'success' => true,
                'data' => $validation
            ]);
        })->middleware('rbac:,system-admin')
          ->name('rbac.system.validate');
    });
});

// Public routes (no authentication required)
Route::prefix('rbac/public')->group(function () {
    
    // Get available account types for registration
    Route::get('/account-types', function () {
        $accountTypes = \App\Models\AccountType::where('status', 'active')
            ->orderBy('hierarchy_level', 'desc')
            ->get(['account_type', 'display_name', 'description']);
        
        return response()->json([
            'success' => true,
            'data' => $accountTypes
        ]);
    })->name('rbac.public.account-types');
    
    // Get system information
    Route::get('/info', function () {
        return response()->json([
            'success' => true,
            'data' => [
                'rbac_version' => '1.0.0',
                'features' => [
                    'role_based_access',
                    'account_type_permissions',
                    'hierarchical_permissions',
                    'state_prison_scoping',
                    'permission_caching'
                ],
                'scopes' => ['system', 'state', 'prison']
            ]
        ]);
    })->name('rbac.public.info');
});