<?php
namespace App\Models;

use App\Models\Escapee;
use App\Models\Prison;
use App\Models\States;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EscapeeBatch extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'old_batch_id',
        'title',
        'description',
        'batch_status',
        'state_id',
        'prison_id',
        'uploaded',
        'status',
    ];

    protected $casts = [
        'uploaded' => 'boolean',
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $dates = ['deleted_at'];

    /**
     * Get all escapees in this batch
     */
    public function escapees()
    {
        return $this->hasMany(Escapee::class, 'batch_id', 'id');
    }

    /**
     * Get the state this batch belongs to
     */
    public function state()
    {
        return $this->belongsTo(States::class);
    }

    /**
     * Get the prison this batch belongs to
     */
    public function prison()
    {
        return $this->belongsTo(Prison::class);
    }

    /**
     * Scope to filter by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('batch_status', $status);
    }

    /**
     * Scope to filter by state
     */
    public function scopeByState($query, $stateId)
    {
        return $query->where('state_id', $stateId);
    }

    /**
     * Scope to filter by prison
     */
    public function scopeByPrison($query, $prisonId)
    {
        return $query->where('prison_id', $prisonId);
    }

    /**
     * Get batch completion percentage
     */
    public function getCompletionPercentageAttribute()
    {
        $totalEscapees = $this->escapees()->count();
        if ($totalEscapees === 0) {
            return 0;
        }

        $resolvedEscapees = $this->escapees()
            ->whereIn('current_status', ['recaptured', 'deceased'])
            ->count();

        return round(($resolvedEscapees / $totalEscapees) * 100, 2);
    }

    /**
     * Get active escapees count
     */
    public function getActiveEscapeesCountAttribute()
    {
        return $this->escapees()
            ->whereIn('current_status', ['escaped', 'unknown'])
            ->count();
    }
}
