<?php

namespace App\Policies;

use App\Models\Staff;
use App\Policies\BasePolicy;
use Modules\AdminManagement\Entities\HqAdmin;
use Modules\IdentityManagement\Entities\Warrant;

class WarrantPolicy extends BasePolicy
{
    /**
     * Get the base permission name for warrants
     */
    protected function getBasePermission(): string
    {
        return 'warrants';
    }

    /**
     * Override view permissions to include manage permissions
     */
    protected function getViewPermissions(): array
    {
        return [
            'view-warrants',
            'manage-warrants',
            'manage-inmates',
            'manage-all'
        ];
    }
}
