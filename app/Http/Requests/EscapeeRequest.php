<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;

class EscapeeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Gate::allows('manage-escapees');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'batch_id' => 'required|exists:escapee_batches,id',
            'prisoner_id' => 'required|exists:inmates_all_class_register,prisoner_no',
            'escape_date' => 'required|date|before_or_equal:today',
            'escape_location' => 'nullable|string|max:255',
            'escape_circumstances' => 'nullable|string|max:1000',
            'recapture_date' => 'nullable|date|after_or_equal:escape_date|before_or_equal:today',
            'recapture_location' => 'nullable|string|max:255',
            'current_status' => 'required|in:escaped,recaptured,deceased,unknown',
        ];

        // Additional rules for update requests
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            $rules['batch_id'] = 'sometimes|exists:escapee_batches,id';
            $rules['prisoner_id'] = 'sometimes|exists:inmates_all_class_register,prisoner_no';
            $rules['escape_date'] = 'sometimes|date|before_or_equal:today';
            $rules['current_status'] = 'sometimes|in:escaped,recaptured,deceased,unknown';
        }

        // Conditional validation for recapture fields
        if ($this->filled('current_status') && $this->current_status === 'recaptured') {
            $rules['recapture_date'] = 'required|date|after_or_equal:escape_date|before_or_equal:today';
            $rules['recapture_location'] = 'required|string|max:255';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'batch_id.required' => 'Batch ID is required.',
            'batch_id.exists' => 'The selected batch does not exist.',
            'prisoner_id.required' => 'Prisoner ID is required.',
            'prisoner_id.exists' => 'The selected prisoner does not exist.',
            'escape_date.required' => 'Escape date is required.',
            'escape_date.date' => 'Escape date must be a valid date.',
            'escape_date.before_or_equal' => 'Escape date cannot be in the future.',
            'recapture_date.after_or_equal' => 'Recapture date must be on or after the escape date.',
            'recapture_date.before_or_equal' => 'Recapture date cannot be in the future.',
            'recapture_date.required' => 'Recapture date is required for recaptured escapees.',
            'recapture_location.required' => 'Recapture location is required for recaptured escapees.',
            'current_status.required' => 'Current status is required.',
            'current_status.in' => 'Invalid status. Must be one of: escaped, recaptured, deceased, unknown.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'batch_id' => 'batch',
            'prisoner_id' => 'prisoner',
            'escape_date' => 'escape date',
            'escape_location' => 'escape location',
            'escape_circumstances' => 'escape circumstances',
            'recapture_date' => 'recapture date',
            'recapture_location' => 'recapture location',
            'current_status' => 'current status',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check if prisoner is already registered as an escapee in another active batch
            if ($this->isMethod('POST') && $this->filled('prisoner_id')) {
                $existingEscapee = \App\Models\Escapee::where('prisoner_id', $this->prisoner_id)
                    ->whereIn('current_status', ['escaped', 'unknown'])
                    ->where(function ($query) {
                        // Exclude the current escapee if updating
                        if ($this->route('escapee')) {
                            $query->where('id', '!=', $this->route('escapee'));
                        }
                    })
                    ->first();

                if ($existingEscapee) {
                    $validator->errors()->add(
                        'prisoner_id',
                        'This prisoner is already registered as an active escapee in another batch.'
                    );
                }
            }
        });
    }
}
