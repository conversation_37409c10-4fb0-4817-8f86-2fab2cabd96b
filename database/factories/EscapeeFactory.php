<?php

namespace Database\Factories;

use App\Models\Escapee;
use App\Models\EscapeeBatch;
use Modules\IdentityManagement\Entities\Inmate;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Escapee>
 */
class EscapeeFactory extends Factory
{
    protected $model = Escapee::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $batches = EscapeeBatch::pluck('id')->toArray();
        $inmates = Inmate::pluck('prisoner_no')->toArray();
        
        $escapeDate = $this->faker->dateTimeBetween('-2 years', '-1 day');
        $currentStatus = $this->faker->randomElement(['escaped', 'recaptured', 'deceased', 'unknown']);
        
        // If recaptured, set recapture date and location
        $recaptureDate = null;
        $recaptureLocation = null;
        
        if ($currentStatus === 'recaptured') {
            $recaptureDate = $this->faker->dateTimeBetween($escapeDate, 'now');
            $recaptureLocation = $this->faker->randomElement([
                'Police Station',
                'Highway Checkpoint',
                'Residential Area',
                'Market Place',
                'Border Crossing',
                'Hospital',
                'Another Prison',
                'Family Home',
                'Workplace',
                'Public Transport',
            ]);
        }
        
        return [
            'batch_id' => $this->faker->randomElement($batches),
            'old_batch_id' => $this->faker->optional()->numberBetween(1, 1000),
            'prisoner_id' => $this->faker->randomElement($inmates),
            'escape_date' => $escapeDate,
            'escape_location' => $this->faker->randomElement([
                'Prison Yard',
                'Workshop Area',
                'Kitchen',
                'Medical Center',
                'Visiting Room',
                'Exercise Area',
                'Dormitory',
                'Laundry Room',
                'Library',
                'Chapel',
                'Transport Vehicle',
                'Court Premises',
                'Hospital',
                'Work Detail',
                'Perimeter Wall',
            ]),
            'escape_circumstances' => $this->faker->randomElement([
                'Escaped during outdoor exercise',
                'Broke through cell window',
                'Overpowered guard during transport',
                'Escaped during medical visit',
                'Climbed over perimeter wall',
                'Escaped during work detail',
                'Broke out during visiting hours',
                'Escaped from court premises',
                'Tunneled under prison wall',
                'Escaped during fire drill',
                'Overpowered guards in workshop',
                'Escaped during meal time',
                'Broke through dormitory ceiling',
                'Escaped during religious service',
                'Cut through fence during night',
            ]),
            'recapture_date' => $recaptureDate,
            'recapture_location' => $recaptureLocation,
            'current_status' => $currentStatus,
            'uploaded' => $this->faker->boolean(25), // 25% chance of being uploaded
            'status' => $this->faker->boolean(98), // 98% chance of being active
            'created_at' => $this->faker->dateTimeBetween($escapeDate, 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the escapee is still escaped.
     */
    public function escaped(): static
    {
        return $this->state(fn (array $attributes) => [
            'current_status' => 'escaped',
            'recapture_date' => null,
            'recapture_location' => null,
        ]);
    }

    /**
     * Indicate that the escapee has been recaptured.
     */
    public function recaptured(): static
    {
        return $this->state(function (array $attributes) {
            $escapeDate = Carbon::parse($attributes['escape_date'] ?? $this->faker->dateTimeBetween('-1 year', '-1 day'));
            $recaptureDate = $this->faker->dateTimeBetween($escapeDate, 'now');
            
            return [
                'current_status' => 'recaptured',
                'recapture_date' => $recaptureDate,
                'recapture_location' => $this->faker->randomElement([
                    'Police Station',
                    'Highway Checkpoint',
                    'Residential Area',
                    'Market Place',
                    'Border Crossing',
                    'Hospital',
                    'Another Prison',
                    'Family Home',
                ]),
            ];
        });
    }

    /**
     * Indicate that the escapee is deceased.
     */
    public function deceased(): static
    {
        return $this->state(fn (array $attributes) => [
            'current_status' => 'deceased',
            'recapture_date' => null,
            'recapture_location' => null,
        ]);
    }

    /**
     * Indicate that the escapee status is unknown.
     */
    public function unknown(): static
    {
        return $this->state(fn (array $attributes) => [
            'current_status' => 'unknown',
            'recapture_date' => null,
            'recapture_location' => null,
        ]);
    }

    /**
     * Indicate that the escapee belongs to a specific batch.
     */
    public function forBatch(int $batchId): static
    {
        return $this->state(fn (array $attributes) => [
            'batch_id' => $batchId,
        ]);
    }

    /**
     * Indicate that the escapee is for a specific prisoner.
     */
    public function forPrisoner(string $prisonerId): static
    {
        return $this->state(fn (array $attributes) => [
            'prisoner_id' => $prisonerId,
        ]);
    }

    /**
     * Indicate that the escape happened recently (within last 30 days).
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'escape_date' => $this->faker->dateTimeBetween('-30 days', 'now'),
        ]);
    }

    /**
     * Indicate that the escapee has been uploaded.
     */
    public function uploaded(): static
    {
        return $this->state(fn (array $attributes) => [
            'uploaded' => true,
        ]);
    }
}
