<?php

namespace Modules\BelongingManagement\Http\Controllers;

use Inertia\Inertia;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Gate;

class BelongingManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('authorize:view-belongings')->only(['index', 'show']);
        $this->middleware('authorize:create-belongings')->only(['create', 'store']);
        $this->middleware('authorize:update-belongings')->only(['edit', 'update']);
        $this->middleware('authorize:delete-belongings')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index()
    {
        // Authorization handled by middleware

        // return view('belongingmanagement::index');
        return Inertia::render('../../Modules/BelongingManagement/Resources/assets/js/views/index');
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        // Authorization handled by middleware

        return view('belongingmanagement::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        // Authorization handled by middleware

        //
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function show($id)
    {
        // Authorization handled by middleware

        return view('belongingmanagement::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        // Authorization handled by middleware

        return view('belongingmanagement::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        // Authorization handled by middleware

        //
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        // Authorization handled by middleware

        //
    }
}
