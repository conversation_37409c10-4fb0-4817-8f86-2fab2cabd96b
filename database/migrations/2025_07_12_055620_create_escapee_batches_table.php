<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('escapee_batches', function (Blueprint $table) {
            $table->id();
            $table->string('old_batch_id')->nullable()->comment('from old cims v1 database');
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('batch_status', ['created', 'under_review', 'investigated', 'closed'])->default('created');
            $table->unsignedBigInteger('state_id')->nullable();
            $table->unsignedBigInteger('prison_id')->nullable();
            $table->boolean('uploaded')->default(false);
            $table->boolean('status')->default(true);
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('state_id')->references('id')->on('states')->onDelete('set null');
            $table->foreign('prison_id')->references('id')->on('prisons')->onDelete('set null');

            // Indexes for performance
            $table->index(['state_id', 'prison_id']);
            $table->index('batch_status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('escapee_batches');
    }
};
