<?php

namespace App\Models;

use App\Models\EscapeeBatch;
use Modules\IdentityManagement\Entities\Inmate;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;


class Escapee extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'batch_id',
        'old_batch_id',
        'prisoner_id',
        'escape_date',
        'escape_location',
        'escape_circumstances',
        'recapture_date',
        'recapture_location',
        'current_status',
        'uploaded',
        'status',
    ];

    protected $casts = [
        'escape_date' => 'datetime',
        'recapture_date' => 'datetime',
        'uploaded' => 'boolean',
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $dates = ['deleted_at'];

    /**
     * Get the batch this escapee belongs to
     */
    public function batch()
    {
        return $this->belongsTo(EscapeeBatch::class, 'batch_id', 'id');
    }

    /**
     * Get the prisoner/inmate record
     */
    public function prisoner()
    {
        return $this->belongsTo(Inmate::class, 'prisoner_id', 'prisoner_no');
    }

    /**
     * Scope to filter by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('current_status', $status);
    }

    /**
     * Scope to filter by escape date range
     */
    public function scopeByEscapeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('escape_date', [$startDate, $endDate]);
    }

    /**
     * Scope to filter active escapees (not recaptured or deceased)
     */
    public function scopeActive($query)
    {
        return $query->whereIn('current_status', ['escaped', 'unknown']);
    }

    /**
     * Scope to filter resolved cases (recaptured or deceased)
     */
    public function scopeResolved($query)
    {
        return $query->whereIn('current_status', ['recaptured', 'deceased']);
    }

    /**
     * Get days since escape
     */
    public function getDaysSinceEscapeAttribute()
    {
        return $this->escape_date ? now()->diffInDays($this->escape_date) : null;
    }

    /**
     * Get days to recapture (if recaptured)
     */
    public function getDaysToRecaptureAttribute()
    {
        if ($this->escape_date && $this->recapture_date) {
            return $this->recapture_date->diffInDays($this->escape_date);
        }
        return null;
    }

    /**
     * Check if escapee is still at large
     */
    public function getIsAtLargeAttribute()
    {
        return in_array($this->current_status, ['escaped', 'unknown']);
    }
}
