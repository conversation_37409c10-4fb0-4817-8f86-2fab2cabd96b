<?php

namespace Database\Seeders;

use App\Models\Escapee;
use App\Models\EscapeeBatch;
use App\Models\States;
use App\Models\Prison;
use Modules\IdentityManagement\Entities\Inmate;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EscapeeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Get some existing states and prisons for realistic data
        $states = States::take(5)->get();
        $prisons = Prison::take(10)->get();

        if ($states->isEmpty() || $prisons->isEmpty()) {
            $this->command->warn('No states or prisons found. Please seed states and prisons first.');
            return;
        }

        // Create batches with different statuses
        $this->command->info('Creating escapee batches...');

        // Create 5 batches in different states
        $batches = collect();

        // 2 active batches (created/under_review)
        $batches->push(
            EscapeeBatch::factory()
                ->created()
                ->forLocation($states->random()->id, $prisons->random()->id)
                ->create([
                    'title' => 'Mass Escape Incident - Kirikiri Maximum Security',
                    'description' => 'Multiple inmates escaped during morning exercise period. Investigation ongoing.',
                ])
        );

        $batches->push(
            EscapeeBatch::factory()
                ->underReview()
                ->forLocation($states->random()->id, $prisons->random()->id)
                ->create([
                    'title' => 'Escape During Court Transport',
                    'description' => 'Two inmates escaped during transport to court hearing.',
                ])
        );

        // 2 investigated batches
        $batches->push(
            EscapeeBatch::factory()
                ->investigated()
                ->forLocation($states->random()->id, $prisons->random()->id)
                ->create([
                    'title' => 'Tunnel Escape Attempt',
                    'description' => 'Inmates attempted escape through underground tunnel. Most recaptured.',
                ])
        );

        // 1 closed batch
        $batches->push(
            EscapeeBatch::factory()
                ->closed()
                ->forLocation($states->random()->id, $prisons->random()->id)
                ->create([
                    'title' => 'Historical Escape Case - 2023',
                    'description' => 'All escapees from this incident have been accounted for.',
                ])
        );

        $this->command->info('Creating individual escapee records...');

        // Create escapees for each batch
        foreach ($batches as $index => $batch) {
            $escapeeCount = rand(2, 8); // Random number of escapees per batch

            for ($i = 0; $i < $escapeeCount; $i++) {
                // Generate different scenarios based on batch status
                $escapee = match ($batch->batch_status) {
                    'created' => Escapee::factory()
                        ->atLarge()
                        ->recent()
                        ->forBatch($batch->id),

                    'under_review' => Escapee::factory()
                        ->when($i < $escapeeCount / 2, fn($factory) => $factory->atLarge())
                        ->when($i >= $escapeeCount / 2, fn($factory) => $factory->recaptured())
                        ->forBatch($batch->id),

                    'investigated' => Escapee::factory()
                        ->when($i < $escapeeCount / 3, fn($factory) => $factory->atLarge())
                        ->when($i >= $escapeeCount / 3 && $i < 2 * $escapeeCount / 3, fn($factory) => $factory->recaptured())
                        ->when($i >= 2 * $escapeeCount / 3, fn($factory) => $factory->unknown())
                        ->forBatch($batch->id),

                    'closed' => Escapee::factory()
                        ->when($i < $escapeeCount / 2, fn($factory) => $factory->recaptured())
                        ->when($i >= $escapeeCount / 2, fn($factory) => $factory->deceased())
                        ->old()
                        ->forBatch($batch->id),

                    default => Escapee::factory()->forBatch($batch->id)
                };

                $escapeeRecord = $escapee->create();

                // Update inmate register if prisoner exists
                $this->updateInmateRegister($escapeeRecord);
            }
        }

        // Create some additional standalone batches for variety
        $this->command->info('Creating additional sample batches...');

        EscapeeBatch::factory(3)
            ->create()
            ->each(function ($batch) {
                // Create 1-5 escapees per batch
                $escapees = Escapee::factory(rand(1, 5))
                    ->forBatch($batch->id)
                    ->create();

                // Update inmate registers
                foreach ($escapees as $escapee) {
                    $this->updateInmateRegister($escapee);
                }
            });

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $totalBatches = EscapeeBatch::count();
        $totalEscapees = Escapee::count();
        $atLarge = Escapee::atLarge()->count();
        $recaptured = Escapee::recaptured()->count();

        $this->command->info("Escapee seeding completed!");
        $this->command->info("Created {$totalBatches} batches with {$totalEscapees} escapee records");
        $this->command->info("Status breakdown: {$atLarge} at large, {$recaptured} recaptured");
    }

    /**
     * Update inmate register with escapee status
     */
    private function updateInmateRegister(Escapee $escapee): void
    {
        // Try to find existing inmate record
        $inmate = Inmate::where('prisoner_no', $escapee->prisoner_no)->first();

        if ($inmate) {
            $inmate->update([
                'escapee' => $escapee->current_status === 'escaped' ? 1 : 0,
                'recaptured' => $escapee->current_status === 'recaptured' ? 1 : 0,
            ]);
        } else {
            // Create a basic inmate record if it doesn't exist (for testing purposes)
            $this->createBasicInmateRecord($escapee->prisoner_no);
        }
    }

    /**
     * Create a basic inmate record for testing using direct DB insert to avoid model issues
     */
    private function createBasicInmateRecord(string $prisonerNo): void
    {
        $states = States::take(5)->get();
        $prisons = Prison::take(10)->get();

        if ($states->isEmpty() || $prisons->isEmpty()) {
            return;
        }

        $faker = \Faker\Factory::create();

        try {
            // Use direct DB insert to avoid model trait issues during seeding
            DB::table('inmates_all_class_register')->insert([
                'prisoner_no' => $prisonerNo,
                'state_id' => $states->random()->id,
                'prison_id' => $prisons->random()->id,
                'surname' => $faker->lastName,
                'first_name' => $faker->firstName,
                'other_names' => $faker->optional()->firstName,
                'date_of_birth' => $faker->date('Y-m-d', '-18 years'),
                'age_on_admission' => $faker->numberBetween(18, 65),
                'gender' => $faker->randomElement(['Male', 'Female']),
                'marital_status' => $faker->randomElement(['Single', 'Married', 'Divorced', 'Widowed']),
                'address' => $faker->address,
                'description' => $faker->sentence,
                'health_status' => 'Good',
                'inmate_status' => 'Active',
                'register_stage' => 1,
                'escapee' => 1,
                'recaptured' => 0,
                'inmate_lodger_ip' => $faker->ipv4,
                'inmate_lodger' => 'SYSTEM_SEEDER',
                'vetted' => false,
                'status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } catch (\Exception $e) {
            // Ignore duplicate key errors during seeding
            if (!str_contains($e->getMessage(), 'Duplicate entry')) {
                $this->command->warn("Could not create inmate record for {$prisonerNo}: " . $e->getMessage());
            }
        }
    }
}
