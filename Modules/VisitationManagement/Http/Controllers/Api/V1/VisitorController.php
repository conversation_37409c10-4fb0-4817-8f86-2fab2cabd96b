<?php
namespace Modules\VisitationManagement\Http\Controllers\Api\V1;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\VisitationManagement\Http\Requests\BlacklistVisitorRequest;
use Modules\VisitationManagement\Http\Requests\StoreVisitorRequest;
use Modules\VisitationManagement\Http\Requests\UpdateVisitorRequest;
use Modules\VisitationManagement\Services\VisitorService;
use Modules\VisitationManagement\Transformers\VisitorResource;
use Illuminate\Support\Facades\Gate;

class VisitorController extends Controller
{
    protected VisitorService $visitorService;

    public function __construct(VisitorService $visitorService)
    {
        $this->middleware('auth:sanctum');
        $this->middleware('authorize:view-visitors')->only(['index', 'show']);
        $this->middleware('authorize:create-visitors')->only(['store']);
        $this->middleware('authorize:update-visitors')->only(['update']);
        $this->middleware('authorize:delete-visitors')->only(['destroy']);
        $this->visitorService = $visitorService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        // Authorization handled by middleware
        $visitors = $this->visitorService->getAllVisitors($request->all(), $request->per_page);
        return response()->json([
            'data'  => VisitorResource::collection($visitors),
            'meta'  => [
                'current_page' => $visitors->currentPage(),
                'last_page'    => $visitors->lastPage(),
                'total'        => $visitors->total(),
                'per_page'     => $visitors->perPage(),
                'from'         => $visitors->firstItem(),
                'to'           => $visitors->lastItem(),
            ],
            'links' => [
                'first' => $visitors->url(1),
                'last'  => $visitors->url($visitors->lastPage()),
                'prev'  => $visitors->previousPageUrl(),
                'next'  => $visitors->nextPageUrl(),
            ],
        ]);
        // return response()->json(VisitorResource::collection($visitors));
        // return response()->json($visitors);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreVisitorRequest $request): JsonResponse
    {
        // Authorization handled by middleware
        $visitor = $this->visitorService->createVisitor($request->validated());
        return response()->json(new VisitorResource($visitor), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        // Authorization handled by middleware
        $visitor = $this->visitorService->getVisitorById($id);
        return $visitor
        ? response()->json(new VisitorResource($visitor))
        : response()->json(['message' => 'Visitor not found'], 404);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateVisitorRequest $request, string $id): JsonResponse
    {
        if (!Gate::allows('update-visitors')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }
        $visitor = $this->visitorService->getVisitorById($id);

        if (! $visitor) {
            return response()->json(['message' => 'Visitor not found'], 404);
        }

        $updatedVisitor = $this->visitorService->updateVisitor($visitor, $request->validated());
        return response()->json(new VisitorResource($updatedVisitor));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        if (!Gate::allows('delete-visitors')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }
        $visitor = $this->visitorService->getVisitorById($id);

        if (! $visitor) {
            return response()->json(['message' => 'Visitor not found'], 404);
        }

        $this->visitorService->deleteVisitor($visitor);
        return response()->json(null, 204);
    }

    public function blacklist(BlacklistVisitorRequest $request, string $id): JsonResponse
    {
        $visitor = $this->visitorService->getVisitorById($id);

        if (! $visitor) {
            return response()->json(['message' => 'Visitor not found'], 404);
        }

        $visitor = $this->visitorService->blacklistVisitor($visitor, $request->validated('reason'));
        return response()->json(new VisitorResource($visitor));
    }

    public function removeFromBlacklist(string $id): JsonResponse
    {
        $visitor = $this->visitorService->getVisitorById($id);

        if (! $visitor) {
            return response()->json(['message' => 'Visitor not found'], 404);
        }

        $visitor = $this->visitorService->removeFromBlacklist($visitor);
        return response()->json(new VisitorResource($visitor));
    }

    public function findByIDNumber(string $idNumber): JsonResponse
    {
        $visitor = $this->visitorService->getVisitorByIdNumber($idNumber);
        return $visitor
        ? response()->json(new VisitorResource($visitor))
        : response()->json(['message' => 'Visitor not found'], 404);
    }
}
