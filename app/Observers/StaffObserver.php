<?php

namespace App\Observers;

use App\Models\Staff;
use App\Services\PermissionCacheService;
use Illuminate\Support\Facades\Log;

class StaffObserver
{
    protected PermissionCacheService $cacheService;

    public function __construct(PermissionCacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Handle the Staff "created" event.
     */
    public function created(Staff $staff): void
    {
        // No cache to clear for new staff, but we can warm it
        $this->cacheService->warmCache($staff);
        
        Log::info("Staff created and cache warmed", ['staff_id' => $staff->id]);
    }

    /**
     * Handle the Staff "updated" event.
     */
    public function updated(Staff $staff): void
    {
        // Check if permission-related fields were changed
        $permissionFields = [
            'account_type_id',
            'assigned_state',
            'prison',
            'status',
            'is_active',
        ];

        $hasPermissionChanges = false;
        foreach ($permissionFields as $field) {
            if ($staff->wasChanged($field)) {
                $hasPermissionChanges = true;
                break;
            }
        }

        if ($hasPermissionChanges) {
            $this->cacheService->clearStaffCache($staff);
            
            Log::info("Staff updated - permission cache cleared", [
                'staff_id' => $staff->id,
                'changed_fields' => array_keys($staff->getChanges()),
            ]);
        }
    }

    /**
     * Handle the Staff "deleted" event.
     */
    public function deleted(Staff $staff): void
    {
        $this->cacheService->clearStaffCache($staff);
        
        Log::info("Staff deleted - cache cleared", ['staff_id' => $staff->id]);
    }

    /**
     * Handle the Staff "restored" event.
     */
    public function restored(Staff $staff): void
    {
        // Clear cache and warm it for restored staff
        $this->cacheService->clearStaffCache($staff);
        $this->cacheService->warmCache($staff);
        
        Log::info("Staff restored - cache cleared and warmed", ['staff_id' => $staff->id]);
    }

    /**
     * Handle the Staff "force deleted" event.
     */
    public function forceDeleted(Staff $staff): void
    {
        $this->cacheService->clearStaffCache($staff);
        
        Log::info("Staff force deleted - cache cleared", ['staff_id' => $staff->id]);
    }
}
