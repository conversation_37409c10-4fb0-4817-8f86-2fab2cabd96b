<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\EscapeeBatchRequest;
use App\Http\Requests\EscapeeCsvUploadRequest;
use App\Services\EscapeeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Gate;

class EscapeeBatchController extends Controller
{
    protected EscapeeService $escapeeService;

    public function __construct(EscapeeService $escapeeService)
    {
        $this->middleware('auth:sanctum');
        $this->escapeeService = $escapeeService;
    }

    /**
     * Display a listing of escapee batches
     */
    public function index(Request $request): JsonResponse
    {
        if (!Gate::allows('view-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $filters = $request->only([
            'state_id', 'prison_id', 'batch_status', 'search',
            'date_from', 'date_to', 'sort_by', 'sort_direction', 'per_page'
        ]);

        $batches = $this->escapeeService->listBatches($filters);

        return response()->json([
            'success' => true,
            'data' => $batches,
            'filters_applied' => $filters,
        ]);
    }

    /**
     * Store a newly created escapee batch
     */
    public function store(EscapeeBatchRequest $request): JsonResponse
    {
        try {
            $batch = $this->escapeeService->createBatch($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Escapee batch created successfully',
                'data' => $batch,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create escapee batch',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified escapee batch
     */
    public function show($id): JsonResponse
    {
        if (!Gate::allows('view-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $batch = $this->escapeeService->getBatch($id);

            return response()->json([
                'success' => true,
                'data' => $batch,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Escapee batch not found',
                'error' => $e->getMessage(),
            ], 404);
        }
    }

    /**
     * Update the specified escapee batch
     */
    public function update(EscapeeBatchRequest $request, $id): JsonResponse
    {
        try {
            $batch = $this->escapeeService->updateBatch($id, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Escapee batch updated successfully',
                'data' => $batch,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update escapee batch',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified escapee batch
     */
    public function destroy($id): JsonResponse
    {
        if (!Gate::allows('delete-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $this->escapeeService->deleteBatch($id);

            return response()->json([
                'success' => true,
                'message' => 'Escapee batch deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete escapee batch',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload CSV file to create escapee batch
     */
    public function uploadCsv(EscapeeCsvUploadRequest $request): JsonResponse
    {
        try {
            $processedData = $request->getProcessedData();
            
            $result = $this->escapeeService->processCsvUpload(
                $processedData['csv_file'],
                $processedData['batch_data'],
                $processedData['options']
            );

            $statusCode = $result['success'] ? 200 : 422;

            return response()->json($result, $statusCode);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process CSV upload',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Download CSV template for escapee upload
     */
    public function downloadTemplate()
    {
        if (!Gate::allows('manage-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        return $this->escapeeService->generateCsvTemplate();
    }

    /**
     * Get statistics for escapee batches
     */
    public function statistics(Request $request): JsonResponse
    {
        if (!Gate::allows('view-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $filters = $request->only([
            'state_id', 'prison_id', 'date_from', 'date_to'
        ]);

        $statistics = $this->escapeeService->generateStatistics($filters);

        return response()->json([
            'success' => true,
            'data' => $statistics,
        ]);
    }
}
