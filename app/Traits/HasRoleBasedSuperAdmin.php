<?php

namespace App\Traits;

use App\Models\Role;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait HasRoleBasedSuperAdmin
{
    /**
     * The roles that belong to the user through universal role assignments.
     */
    public function universalRoles(): MorphToMany
    {
        return $this->morphToMany(Role::class, 'user', 'universal_role_assignments')
                    ->withPivot(['state_id', 'prison_id', 'assigned_at', 'expires_at', 'is_active'])
                    ->withTimestamps();
    }

    /**
     * Get active universal roles for the user.
     */
    public function activeUniversalRoles()
    {
        return $this->universalRoles()->wherePivot('is_active', true)
                           ->where(function ($query) {
                               $query->whereNull('universal_role_assignments.expires_at')
                                     ->orWhere('universal_role_assignments.expires_at', '>', now());
                           });
    }

    /**
     * Check if user has a specific role through universal role assignments.
     */
    public function hasUniversalRole(string $roleName, ?int $stateId = null, ?int $prisonId = null): bool
    {
        $query = $this->activeUniversalRoles()->where('roles.name', $roleName);

        if ($stateId) {
            $query->where(function ($q) use ($stateId) {
                $q->where('universal_role_assignments.state_id', $stateId)
                  ->orWhere('roles.scope', 'system');
            });
        }

        if ($prisonId) {
            $query->where(function ($q) use ($prisonId) {
                $q->where('universal_role_assignments.prison_id', $prisonId)
                  ->orWhere('roles.scope', 'state')
                  ->orWhere('roles.scope', 'system');
            });
        }

        return $query->exists();
    }

    /**
     * Check if user has any of the given roles through universal role assignments.
     */
    public function hasAnyUniversalRole(array $roles, ?int $stateId = null, ?int $prisonId = null): bool
    {
        foreach ($roles as $role) {
            if ($this->hasUniversalRole($role, $stateId, $prisonId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if user is a role-based super admin.
     * Checks for roles named 'superadmin', 'super-admin', or 'super_admin'
     */
    public function isRoleBasedSuperAdmin(): bool
    {
        $superAdminRoles = ['superadmin', 'super-admin', 'super_admin'];

        return $this->activeUniversalRoles()
                    ->whereIn('roles.name', $superAdminRoles)
                    ->exists();
    }

    /**
     * Assign a role to the user through universal role assignments.
     */
    public function assignUniversalRole(string $roleName, ?int $stateId = null, ?int $prisonId = null, ?\DateTime $expiresAt = null): bool
    {
        $role = Role::where('name', $roleName)->first();

        if (!$role) {
            return false;
        }

        $this->universalRoles()->attach($role->id, [
            'state_id' => $stateId,
            'prison_id' => $prisonId,
            'assigned_at' => now(),
            'expires_at' => $expiresAt,
            'is_active' => true,
        ]);

        return true;
    }

    /**
     * Remove a role from the user through universal role assignments.
     */
    public function removeUniversalRole(string $roleName, ?int $stateId = null, ?int $prisonId = null): bool
    {
        $role = Role::where('name', $roleName)->first();

        if (!$role) {
            return false;
        }

        $query = $this->universalRoles()->where('role_id', $role->id);

        if ($stateId !== null) {
            $query->where('state_id', $stateId);
        }

        if ($prisonId !== null) {
            $query->where('prison_id', $prisonId);
        }

        $query->detach();
        return true;
    }
}
