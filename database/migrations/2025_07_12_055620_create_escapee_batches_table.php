<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('escapee_batches', function (Blueprint $table) {
            $table->id();
            $table->string('old_batch_id')->comment('from old cims v1 database');
            $table->string('title');
            $table->string('message');
            $table->unsignedBigInteger('state_id');
            $table->unsignedBigInteger('prison_id');
            $table->boolean('uploaded')->default(false);
            $table->boolean('status')->default(true);
            $table->foreign('state_id')->references('id')->on('states')->onDelete('cascade');
            $table->foreign('prison_id')->references('id')->on('prisons')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('escapee_batches');
    }
};
