<?php

namespace App\Services;

use App\Models\Staff;
use App\Models\Role;
use App\Models\Permission;
use App\Models\AccountType;
use Modules\AdminManagement\Entities\HqAdmin;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class RbacService
{
    /**
     * Assign role to staff member.
     */
    public function assignRole($user, string $roleName, ?int $stateId = null, ?int $prisonId = null, ?string $expiresAt = null): bool
    {
        // Super admin cannot have roles assigned (they have full access)
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            throw new \InvalidArgumentException('Super admins cannot have roles assigned.');
        }

        // Only Staff can have roles assigned
        if (!$user instanceof Staff) {
            throw new \InvalidArgumentException('Only Staff users can have roles assigned.');
        }

        $role = Role::where('name', $roleName)->first();

        if (!$role) {
            throw new \InvalidArgumentException("Role '{$roleName}' not found.");
        }

        // Validate scope requirements
        if ($role->scope === 'state' && !$stateId) {
            throw new \InvalidArgumentException('State ID required for state-scoped role.');
        }

        if ($role->scope === 'prison' && (!$stateId || !$prisonId)) {
            throw new \InvalidArgumentException('State ID and Prison ID required for prison-scoped role.');
        }

        // Check if assignment already exists
        $existingAssignment = $user->roles()
            ->where('role_id', $role->id)
            ->where('state_id', $stateId)
            ->where('prison_id', $prisonId)
            ->first();

        if ($existingAssignment) {
            // Update existing assignment
            $user->roles()->updateExistingPivot($role->id, [
                'expires_at' => $expiresAt,
                'is_active' => true,
                'assigned_at' => now()
            ]);
        } else {
            // Create new assignment
            $user->roles()->attach($role->id, [
                'state_id' => $stateId,
                'prison_id' => $prisonId,
                'assigned_at' => now(),
                'expires_at' => $expiresAt,
                'is_active' => true
            ]);
        }



        return true;
    }

    /**
     * Remove role from staff member.
     */
    public function removeRole($user, string $roleName, ?int $stateId = null, ?int $prisonId = null): bool
    {
        // Super admin cannot have roles removed (they don't have roles)
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return false;
        }

        // Only Staff can have roles removed
        if (!$user instanceof Staff) {
            return false;
        }

        $role = Role::where('name', $roleName)->first();

        if (!$role) {
            return false;
        }

        $query = $user->roles()->where('role_id', $role->id);

        if ($stateId) {
            $query->where('state_id', $stateId);
        }

        if ($prisonId) {
            $query->where('prison_id', $prisonId);
        }

        $query->detach();



        return true;
    }

    /**
     * Get all permissions for a user (combined from account type and roles).
     */
    public function getStaffPermissions($user): Collection
    {
        // Super admin has all permissions
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return Permission::where('is_active', true)->pluck('name');
        }

        // Only Staff have role-based permissions
        if (!$user instanceof Staff) {
            return collect();
        }

        $permissions = collect();

        // Get account type permissions
        if ($user->accountType) {
            $accountTypePermissions = $user->accountType->getAllPermissions();
            $permissions = $permissions->merge($accountTypePermissions);
        }

        // Get role-based permissions
        $rolePermissions = $user->roles()
            ->with('permissions')
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->get()
            ->pluck('permissions')
            ->flatten()
            ->pluck('name');

        $permissions = $permissions->merge($rolePermissions);

        return $permissions->unique()->values();
    }

    /**
     * Check if user has specific permission.
     */
    public function staffHasPermission($user, string $permission): bool
    {
        return $this->getStaffPermissions($user)->contains($permission);
    }

    /**
     * Get user roles with context.
     */
    public function getStaffRoles($user): Collection
    {
        // Super admin doesn't have roles (they have full access)
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return collect();
        }

        // Only Staff have roles
        if (!$user instanceof Staff) {
            return collect();
        }

        return $user->roles()
            ->with(['state', 'prison'])
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->get();
    }

    /**
     * Get available roles for assignment based on user's current context.
     */
    public function getAvailableRoles($user): Collection
    {
        // Super admin doesn't need roles (they have full access)
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return collect();
        }

        // Only Staff can have roles assigned
        if (!$user instanceof Staff) {
            return collect();
        }

        $query = Role::where('is_active', true);

        // Filter based on staff's assigned state/prison
        if ($user->assigned_state) {
            $query->where(function ($q) use ($user) {
                $q->where('scope', 'system')
                  ->orWhere(function ($sq) use ($user) {
                      $sq->where('scope', 'state')
                         ->where(function ($ssq) use ($user) {
                             $ssq->whereNull('state_id')
                                ->orWhere('state_id', $user->assigned_state);
                         });
                  })
                  ->orWhere(function ($pq) use ($user) {
                      $pq->where('scope', 'prison')
                         ->where(function ($ppq) use ($user) {
                             $ppq->whereNull('prison_id')
                                ->orWhere('prison_id', $user->prison);
                         });
                  });
            });
        }

        return $query->get();
    }

    /**
     * Sync user roles (remove all and assign new ones).
     */
    public function syncStaffRoles($user, array $roleAssignments): bool
    {
        // Super admin cannot have roles synced
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            throw new \InvalidArgumentException('Super admins cannot have roles synced.');
        }

        // Only Staff can have roles synced
        if (!$user instanceof Staff) {
            throw new \InvalidArgumentException('Only Staff users can have roles synced.');
        }

        DB::transaction(function () use ($user, $roleAssignments) {
            // Remove all existing roles
            $user->roles()->detach();

            // Assign new roles
            foreach ($roleAssignments as $assignment) {
                $this->assignRole(
                    $user,
                    $assignment['role_name'],
                    $assignment['state_id'] ?? null,
                    $assignment['prison_id'] ?? null,
                    $assignment['expires_at'] ?? null
                );
            }
        });

        return true;
    }

    /**
     * Get permission hierarchy for a module.
     */
    public function getModulePermissions(string $module): Collection
    {
        return Permission::where('module', $module)
            ->where('is_active', true)
            ->orderBy('scope')
            ->orderBy('name')
            ->get();
    }

    /**
     * Check if user can access specific state/prison.
     */
    public function canAccessLocation($user, ?int $stateId = null, ?int $prisonId = null): bool
    {
        // Super admin has access to all locations
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }

        // Only Staff have location-based access
        if (!$user instanceof Staff) {
            return false;
        }

        // System-wide access
        if ($user->hasPermission('system-admin')) {
            return true;
        }

        // State-level access
        if ($stateId && $user->hasPermission('state-admin')) {
            return $user->assigned_state == $stateId;
        }

        // Prison-level access
        if ($prisonId && $user->hasPermission('prison-admin')) {
            return $user->prison == $prisonId;
        }

        // Check role-based access
        $hasAccess = $user->roles()
            ->where('is_active', true)
            ->where(function ($query) use ($stateId, $prisonId) {
                if ($stateId) {
                    $query->where('state_id', $stateId);
                }
                if ($prisonId) {
                    $query->where('prison_id', $prisonId);
                }
            })
            ->exists();

        return $hasAccess;
    }



    /**
     * Get system statistics.
     */
    public function getSystemStats(): array
    {
        return [
            'total_staff' => Staff::count(),
            'total_roles' => Role::count(),
            'total_permissions' => Permission::count(),
            'total_account_types' => AccountType::count(),
            'active_roles' => Role::where('is_active', true)->count(),
            'active_permissions' => Permission::where('is_active', true)->count(),
            'staff_with_roles' => Staff::has('roles')->count(),
            'roles_by_scope' => Role::groupBy('scope')
                ->selectRaw('scope, count(*) as count')
                ->pluck('count', 'scope')
                ->toArray(),
            'permissions_by_module' => Permission::groupBy('module')
                ->selectRaw('module, count(*) as count')
                ->pluck('count', 'module')
                ->toArray()
        ];
    }
}
