<?php

namespace App\Policies;

use App\Models\Staff;
use App\Policies\BasePolicy;
use Modules\AdminManagement\Entities\HqAdmin;
use Modules\CaseManagement\Entities\EscortDuty;

class EscortDutyPolicy extends BasePolicy
{
    /**
     * Get the base permission name for escort duties
     */
    protected function getBasePermission(): string
    {
        return 'escort-duties';
    }

    /**
     * Override view permissions to include manage permissions
     */
    protected function getViewPermissions(): array
    {
        return [
            'view-escort-duties',
            'manage-escort-duties',
            'manage-cases',
            'manage-all'
        ];
    }
}
