<?php

namespace App\Policies;

use App\Models\Staff;
use Modules\AdminManagement\Entities\HqAdmin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Database\Eloquent\Model;

abstract class BasePolicy
{
    use HandlesAuthorization;

    /**
     * Perform pre-authorization checks.
     * Super admins bypass all authorization checks.
     */
    public function before($user, $ability)
    {
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }
    }

    /**
     * Check if user has specific permission with optional scope
     */
    protected function hasPermission($user, string $permission, ?int $stateId = null, ?int $prisonId = null): bool
    {
        // Super admin check (redundant but explicit)
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }

        // Only Staff can have role-based permissions
        if (!$user instanceof Staff) {
            return false;
        }

        return $user->hasPermission($permission, $stateId, $prisonId);
    }

    /**
     * Check if user has any of the given permissions
     */
    protected function hasAnyPermission($user, array $permissions, ?int $stateId = null, ?int $prisonId = null): bool
    {
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }

        if (!$user instanceof Staff) {
            return false;
        }

        return $user->hasAnyPermission($permissions, $stateId, $prisonId);
    }

    /**
     * Check if user has all of the given permissions
     */
    protected function hasAllPermissions($user, array $permissions, ?int $stateId = null, ?int $prisonId = null): bool
    {
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }

        if (!$user instanceof Staff) {
            return false;
        }

        return $user->hasAllPermissions($permissions, $stateId, $prisonId);
    }

    /**
     * Check if user can access specific state
     */
    protected function canAccessState($user, ?int $stateId): bool
    {
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }

        if (!$user instanceof Staff) {
            return false;
        }

        // System admin can access all states
        if ($user->hasPermission('system-admin')) {
            return true;
        }

        // State admin can access their assigned state
        if ($user->hasPermission('state-admin') && $user->assigned_state == $stateId) {
            return true;
        }

        // Check role-based state access
        return $user->hasStateAccess($stateId);
    }

    /**
     * Check if user can access specific prison
     */
    protected function canAccessPrison($user, ?int $prisonId): bool
    {
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }

        if (!$user instanceof Staff) {
            return false;
        }

        // System admin can access all prisons
        if ($user->hasPermission('system-admin')) {
            return true;
        }

        // Prison admin can access their assigned prison
        if ($user->hasPermission('prison-admin') && $user->prison == $prisonId) {
            return true;
        }

        // Check role-based prison access
        return $user->hasPrisonAccess($prisonId);
    }

    /**
     * Check if user owns or can manage the resource
     */
    protected function canManageResource($user, Model $resource, string $permission, ?string $ownerField = null): bool
    {
        // Super admin can manage everything
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }

        if (!$user instanceof Staff) {
            return false;
        }

        // Check if user has the required permission
        if (!$user->hasPermission($permission)) {
            return false;
        }

        // If owner field is specified, check ownership
        if ($ownerField && isset($resource->$ownerField)) {
            return $resource->$ownerField == $user->id;
        }

        // Check scope-based access
        $stateId = $resource->state_id ?? null;
        $prisonId = $resource->prison_id ?? null;

        if ($prisonId && !$this->canAccessPrison($user, $prisonId)) {
            return false;
        }

        if ($stateId && !$this->canAccessState($user, $stateId)) {
            return false;
        }

        return true;
    }

    /**
     * Standard view authorization
     */
    public function viewAny($user): bool
    {
        return $this->hasAnyPermission($user, $this->getViewPermissions());
    }

    /**
     * Standard view single resource authorization
     */
    public function view($user, Model $model): bool
    {
        return $this->canManageResource($user, $model, $this->getViewPermission());
    }

    /**
     * Standard create authorization
     */
    public function create($user): bool
    {
        return $this->hasPermission($user, $this->getCreatePermission());
    }

    /**
     * Standard update authorization
     */
    public function update($user, Model $model): bool
    {
        return $this->canManageResource($user, $model, $this->getUpdatePermission());
    }

    /**
     * Standard delete authorization
     */
    public function delete($user, Model $model): bool
    {
        return $this->canManageResource($user, $model, $this->getDeletePermission());
    }

    /**
     * Standard restore authorization
     */
    public function restore($user, Model $model): bool
    {
        return $this->canManageResource($user, $model, $this->getRestorePermission());
    }

    /**
     * Standard force delete authorization
     */
    public function forceDelete($user, Model $model): bool
    {
        return $this->canManageResource($user, $model, $this->getForceDeletePermission());
    }

    /**
     * Get view permissions - override in child classes
     */
    protected function getViewPermissions(): array
    {
        return [$this->getViewPermission()];
    }

    /**
     * Get base permission name - override in child classes
     */
    abstract protected function getBasePermission(): string;

    /**
     * Get view permission
     */
    protected function getViewPermission(): string
    {
        return 'view-' . $this->getBasePermission();
    }

    /**
     * Get create permission
     */
    protected function getCreatePermission(): string
    {
        return 'create-' . $this->getBasePermission();
    }

    /**
     * Get update permission
     */
    protected function getUpdatePermission(): string
    {
        return 'update-' . $this->getBasePermission();
    }

    /**
     * Get delete permission
     */
    protected function getDeletePermission(): string
    {
        return 'delete-' . $this->getBasePermission();
    }

    /**
     * Get restore permission
     */
    protected function getRestorePermission(): string
    {
        return 'restore-' . $this->getBasePermission();
    }

    /**
     * Get force delete permission
     */
    protected function getForceDeletePermission(): string
    {
        return 'force-delete-' . $this->getBasePermission();
    }

    /**
     * Get manage permission (covers create, update, delete)
     */
    protected function getManagePermission(): string
    {
        return 'manage-' . $this->getBasePermission();
    }
}
