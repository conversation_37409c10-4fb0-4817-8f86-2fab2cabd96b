<?php
namespace Modules\IdentityManagement\Entities;

use App\Models\Prison;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\CaseManagement\Entities\Sittings;
use Modules\HealthManagement\Entities\MedicalHistory;
use Modules\IdentityManagement\Entities\ATP;
use Modules\IdentityManagement\Entities\Convict;
use Modules\LegalAidManagement\Entities\LegalRepresentative;
use Modules\TrainingManagement\Entities\InmateTrainingEnrollment;
use Modules\TrainingManagement\Entities\ProductivityLog;
use Modules\VisitationManagement\Entities\Visit;
use Modules\VisitationManagement\Entities\Visitor;

class Inmate extends Model
{
    use HasFactory;
    protected $table      = "inmates_all_class_register";
    protected $primaryKey = 'id';
    //protected $fillable = ['prisoner_no'];
    protected $guarded = [];

    /**
     * The function "convicted" returns a HasMany relationship for a prisoner's convictions.
     *
     * @return HasMany a HasMany relationship.
     */
    public function convicted(): HasMany
    {
        return $this->hasMany(Convict::class, 'prisoner_no', 'prisoner_no');
    }

    /**
     * The function "awaiting" returns a HasMany relationship for a specific prisoner number in the ATP
     * class.
     *
     * @return HasMany a HasMany relationship.
     */
    public function awaiting(): HasMany
    {
        return $this->hasMany(ATP::class, 'prisoner_no', 'prisoner_no');
    }

    /**
     * The function returns a HasMany relationship for the "sittings" table, using the "prisoner_no"
     * column as the foreign key.
     *
     * @return HasMany a HasMany relationship.
     */
    public function sittings(): HasMany
    {
        return $this->hasMany(Sittings::class, 'prisoner_no', 'prisoner_no');
    }

    public function legal_rep(): BelongsToMany
    {
        return $this->belongsToMany(
            LegalRepresentative::class,
            'inmate_legal_representative',
            'prisoner_no',
            'rep_id',
            'prisoner_no',
            'id'
        )->withPivot(['date_assigned', 'offence', 'status']);
    }

    public function medical_history(): HasMany
    {
        return $this->hasMany(MedicalHistory::class, 'prisoner_no', 'prisoner_no');
    }

    public function warrants(): HasMany
    {
        return $this->hasMany(Warrant::class, 'prisoner_no', 'prisoner_no');
    }

    public function center(): BelongsTo
    {
        return $this->belongsTo(Prison::class, 'prison_id', 'id');
    }
    /**
     * The function returns a new instance of the InmateFactory class.
     *
     * @return The method is returning an instance of the `InmateFactory` class.
     */

    public function visitors(): BelongsToMany
    {
        return $this->belongsToMany(Visitor::class, 'inmate_visitor', 'prisoner_no', 'visitor_id')
            ->withPivot('relationship')
            ->withTimestamps();
    }

    public function visits(): HasMany
    {
        return $this->hasMany(Visit::class, 'prisoner_no', 'prisoner_no');
    }

    public function enrollments(): HasMany
    {
        return $this->hasMany(InmateTrainingEnrollment::class, 'prisoner_no', 'prisoner_no');
    }

    public function productivityLogs(): HasMany
    {
        return $this->hasMany(ProductivityLog::class, 'prisoner_no', 'prisoner_no');
    }

    protected static function newFactory()
    {
        return \Modules\IdentityManagement\Database\factories\InmateFactory::new ();
    }
}