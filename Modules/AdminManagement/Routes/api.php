<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\AdminManagement\Http\Controllers\Api\V1\HqAdminController;
use Modules\AdminManagement\Http\Controllers\Api\V1\AdminTypeController;
use Modules\AdminManagement\Http\Controllers\Api\V1\HqAccountTypeController;
use Modules\AdminManagement\Http\Controllers\Api\V1\StateAdminZoneController;
use Modules\AdminManagement\Http\Controllers\Api\V1\StateZoneAdminController;


/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('/adminmanagement', function (Request $request) {
//     return $request->user();
// });
Route::prefix('v1')->group(function () {
    // Unprotected route for GET admin_type
    Route::get('admin_type', [AdminTypeController::class, 'index']);

    Route::middleware(['auth:sanctum'])->group(function () {

        // Admin User Management Routes with RBAC
        Route::middleware('authorize:any:view-admin-users,manage-admin-users,manage-all')->group(function () {
            Route::get('admin_users', [StateAdminZoneController::class, 'index']);
            Route::get('admin_users/{admin_user}', [StateAdminZoneController::class, 'show']);
        });

        Route::middleware('authorize:any:create-admin-users,manage-admin-users,manage-all')->group(function () {
            Route::post('admin_users', [StateAdminZoneController::class, 'store']);
        });

        Route::middleware('authorize:any:update-admin-users,manage-admin-users,manage-all')->group(function () {
            Route::put('admin_users/{admin_user}', [StateAdminZoneController::class, 'update']);
            Route::patch('admin_users/{admin_user}', [StateAdminZoneController::class, 'update']);
        });

        Route::middleware('authorize:any:delete-admin-users,manage-admin-users,manage-all')->group(function () {
            Route::delete('admin_users/{admin_user}', [StateAdminZoneController::class, 'destroy']);
        });

        // Admin Type Management Routes
        Route::middleware('authorize:any:manage-admin-types,manage-all')->group(function () {
            Route::get('admin_type/{admin_type}', [AdminTypeController::class, 'show']);
            Route::post('admin_type', [AdminTypeController::class, 'store']);
            Route::put('admin_type/{admin_type}', [AdminTypeController::class, 'update']);
            Route::patch('admin_type/{admin_type}', [AdminTypeController::class, 'update']);
            Route::delete('admin_type/{admin_type}', [AdminTypeController::class, 'destroy']);
        });

        // Account Type Management Routes
        Route::middleware('authorize:any:manage-account-types,manage-all')->group(function () {
            Route::get('account_type', [HqAccountTypeController::class, 'index']);
            Route::get('account_type/{account_type}', [HqAccountTypeController::class, 'show']);
            Route::post('account_type', [HqAccountTypeController::class, 'store']);
            Route::put('account_type/{account_type}', [HqAccountTypeController::class, 'update']);
            Route::patch('account_type/{account_type}', [HqAccountTypeController::class, 'update']);
            Route::delete('account_type/{account_type}', [HqAccountTypeController::class, 'destroy']);
        });

        // State Zone Admin Management Routes
        Route::middleware('authorize:any:view-state-zone-admins,manage-state-zone-admins,manage-all')->group(function () {
            Route::get('state_zone_admins', [StateZoneAdminController::class, 'index']);
            Route::get('state_zone_admins/{state_zone_admin}', [StateZoneAdminController::class, 'show']);
        });

        Route::middleware('authorize:any:create-state-zone-admins,manage-state-zone-admins,manage-all')->group(function () {
            Route::post('state_zone_admins', [StateZoneAdminController::class, 'store']);
        });

        Route::middleware('authorize:any:update-state-zone-admins,manage-state-zone-admins,manage-all')->group(function () {
            Route::put('state_zone_admins/{state_zone_admin}', [StateZoneAdminController::class, 'update']);
            Route::patch('state_zone_admins/{id}', [StateZoneAdminController::class, 'update'])->name('state_zone_admins.update');
            Route::patch('state_zone_admins/{id}/activate', [StateZoneAdminController::class, 'activate'])->name('state_zone_admins.activate');
            Route::patch('state_zone_admins/{id}/deactivate', [StateZoneAdminController::class, 'deactivate'])->name('state_zone_admins.deactivate');
        });

        Route::middleware('authorize:any:delete-state-zone-admins,manage-state-zone-admins,manage-all')->group(function () {
            Route::delete('state_zone_admins/{state_zone_admin}', [StateZoneAdminController::class, 'destroy']);
        });

        // HQ Admin Management Routes
        Route::middleware('authorize:any:view-hq-admins,manage-hq-admins,manage-all')->group(function () {
            Route::get('hq_admins', [HqAdminController::class, 'index']);
            Route::get('hq_admins/{hq_admin}', [HqAdminController::class, 'show']);
        });

        Route::middleware('authorize:any:create-hq-admins,manage-hq-admins,manage-all')->group(function () {
            Route::post('hq_admins', [HqAdminController::class, 'store']);
        });

        Route::middleware('authorize:any:update-hq-admins,manage-hq-admins,manage-all')->group(function () {
            Route::put('hq_admins/{hq_admin}', [HqAdminController::class, 'update']);
            Route::patch('hq_admins/{id}', [HqAdminController::class, 'update'])->name('hq_admins.update');
            Route::patch('hq_admins/{id}/activate', [HqAdminController::class, 'activate'])->name('hq_admins.activate');
            Route::patch('hq_admins/{id}/deactivate', [HqAdminController::class, 'deactivate'])->name('hq_admins.deactivate');
        });

        Route::middleware('authorize:any:delete-hq-admins,manage-hq-admins,manage-all')->group(function () {
            Route::delete('hq_admins/{hq_admin}', [HqAdminController::class, 'destroy']);
        });
    });
});
