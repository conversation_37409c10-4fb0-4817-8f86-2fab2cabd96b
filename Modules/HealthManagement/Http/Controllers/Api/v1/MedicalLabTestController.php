<?php

namespace Modules\HealthManagement\Http\Controllers\Api\v1;

use App\Traits\Common;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Contracts\Support\Renderable;
use Modules\HealthManagement\Entities\MedicalLabTest;

class MedicalLabTestController extends Controller
{
    use Common;

    public function __construct()
    {
        $this->middleware('authorize:view-medical-lab-tests')->only(['index', 'show', 'getLabTestsByPrisoner']);
        $this->middleware('authorize:create-medical-lab-tests')->only(['store']);
        $this->middleware('authorize:update-medical-lab-tests')->only(['update']);
        $this->middleware('authorize:delete-medical-lab-tests')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(Request $request)
    {
        // Authorization handled by middleware

        $noIncludeArray = array('count', 'page', 'include', 'paginate', 'start_date', 'end_date', 'test_status', 'search');
        $relations = array('appointment');
        $includeString = explode(',', $request->include);
        $query = $request->query();
        $paginate = $request->paginate ? $request->paginate : 20;

        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

        $labTests = MedicalLabTest::orderBy('test_date', 'desc');

        $labTests->where($filteredQuery);

        if ($request->has('start_date') && $request->has('end_date')) {
            $labTests->whereBetween('test_date', [$request->start_date, $request->end_date]);
        } else if ($request->has('start_date')) {
            $labTests->where('test_date', '>=', $request->start_date);
        } else if ($request->has('end_date')) {
            $labTests->where('test_date', '<=', $request->end_date);
        }

        if ($request->has('test_status')) {
            $labTests->where('test_status', $request->test_status);
        }

        if ($request->has('search')) {
            $searchTerm = $request->search;
            $labTests->where(function($query) use ($searchTerm) {
                $query->where('prisoner_no', 'like', "%{$searchTerm}%")
                      ->orWhere('test_type', 'like', "%{$searchTerm}%")
                      ->orWhere('results', 'like', "%{$searchTerm}%");
            });
        }

        $results = $labTests->paginate($paginate);

        if($request->include){
            foreach($includeString as $string){
                $checkRelationshipArray = $this->shouldIncludeRelation($relations, $string);
                if($checkRelationshipArray) $results->load($string);
            }
        }

         // Append the full name to each model
        $results->getCollection()->each(function ($data) {
            if ($data->inmate) {
                $data->prisoner_fullname = trim("{$data->inmate->surname} {$data->inmate->first_name} {$data->inmate->other_names}");
                unset($data->inmate);
            }
        });

        if($request->count){
            $results = $results->total();
        }

        return response()->json(['data' => $results]);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        return view('healthmanagement::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        // Authorization handled by middleware

        $request->validate([
            'prisoner_no' => 'required|exists:inmates_all_class_register,prisoner_no',
            'appointment_id' => 'required|exists:medical_appointments,id',
            'test_type' => 'required|string',
            'test_date' => 'required|date',
            'results' => 'nullable|string',
            'test_status' => 'required|in:pending,completed,canceled',
        ]);

        $labTest = new MedicalLabTest($request->all());
        $labTest->save();

        return response()->json([
            'message' => 'Medical lab test created successfully',
            'data' => $labTest
        ], 201);
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function show($id)
    {
        // Authorization handled by middleware

        $labTest = MedicalLabTest::findOrFail($id);
        return response()->json(['data' => $labTest]);
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        return view('healthmanagement::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        // Authorization handled by middleware

        $request->validate([
            'prisoner_no' => 'required|exists:inmates_all_class_register,prisoner_no',
            'appointment_id' => 'required|exists:medical_appointments,id',
            'test_type' => 'required|string',
            'test_date' => 'required|date',
            'results' => 'nullable|string',
            'test_status' => 'required|in:pending,completed,canceled',
            'status' => 'nullable|integer|in:0,1',
        ]);

        $labTest = MedicalLabTest::findOrFail($id);
        $labTest->update($request->all());

        return response()->json([
            'message' => 'Medical lab test updated successfully',
            'data' => $labTest
        ]);
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        // Authorization handled by middleware

        $labTest = MedicalLabTest::findOrFail($id);
        $labTest->delete();

        return response()->json([
            'message' => 'Medical lab test deleted successfully'
        ]);
    }


    /**
     * Get all lab tests for a specific prisoner
     * @param string $prisoner_no
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLabTestsByPrisoner($prisoner_no)
    {
        // Authorization handled by middleware

        $labTests = MedicalLabTest::where('prisoner_no', $prisoner_no)
            ->orderBy('test_date', 'desc')
            ->get();

        if ($labTests->isEmpty()) {
            return response()->json([
                'message' => 'No lab tests found for this prisoner',
                'data' => []
            ]);
        }

        return response()->json([
            'data' => $labTests
        ]);
    }
}
