<?php

namespace Modules\VisitationManagement\Http\Controllers\Api\V1;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\VisitationManagement\Http\Requests\StoreVisitorWithVisitRequest;
use Modules\VisitationManagement\Http\Requests\StoreVisitRequest;
use Modules\VisitationManagement\Http\Requests\UpdateVisitRequest;
use Modules\VisitationManagement\Services\VisitService;
use Modules\VisitationManagement\Transformers\VisitorResource;
use Modules\VisitationManagement\Transformers\VisitResource;
use Illuminate\Support\Facades\Gate;

class VisitController extends Controller
{
    protected $visitService;

    public function __construct(VisitService $visitService)
    {
        $this->middleware('auth:sanctum');
        $this->middleware('authorize:view-visits')->only(['index', 'show']);
        $this->middleware('authorize:create-visits')->only(['store']);
        $this->middleware('authorize:update-visits')->only(['update']);
        $this->middleware('authorize:delete-visits')->only(['destroy']);
        $this->visitService = $visitService;
    }

    public function index(Request $request): JsonResponse
    {
        // Authorization handled by middleware
        $visits = $this->visitService->getAllVisits($request->all(), $request->per_page ?? 15);
        // return response()->json(VisitResource::collection($visits));
        return response()->json([
            'data'  => VisitResource::collection($visits),
            'meta'  => [
                'current_page' => $visits->currentPage(),
                'last_page'    => $visits->lastPage(),
                'total'        => $visits->total(),
                'per_page'     => $visits->perPage(),
                'from'         => $visits->firstItem(),
                'to'           => $visits->lastItem(),
            ],
            'links' => [
                'first' => $visits->url(1),
                'last'  => $visits->url($visits->lastPage()),
                'prev'  => $visits->previousPageUrl(),
                'next'  => $visits->nextPageUrl(),
            ],
        ]);
    }

    public function store(StoreVisitRequest $request): JsonResponse
    {
        // Authorization handled by middleware
        $visit = $this->visitService->createVisit($request->validated());
        return response()->json(new VisitResource($visit), 201);
    }

    public function show(string $id): JsonResponse
    {
        // Authorization handled by middleware
        $visit = $this->visitService->getVisitById($id);
        return $visit
        ? response()->json(new VisitResource($visit))
        : response()->json(['message' => 'Visit not found'], 404);
    }

    public function update(UpdateVisitRequest $request, string $id): JsonResponse
    {
        if (!Gate::allows('update-visits')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }
        $visit = $this->visitService->getVisitById($id);

        if (! $visit) {
            return response()->json(['message' => 'Visit not found'], 404);
        }

        $updatedVisit = $this->visitService->updateVisit($visit, $request->validated());
        return response()->json(new VisitResource($updatedVisit));
    }

    public function destroy(string $id): JsonResponse
    {
        if (!Gate::allows('delete-visits')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }
        $visit = $this->visitService->getVisitById($id);

        if (! $visit) {
            return response()->json(['message' => 'Visit not found'], 404);
        }

        $this->visitService->deleteVisit($visit);
        return response()->json(null, 204);
    }

    public function checkIn(string $id): JsonResponse
    {
        $visit = $this->visitService->getVisitById($id);

        if (! $visit) {
            return response()->json(['message' => 'Visit not found'], 404);
        }

        $visit = $this->visitService->checkInVisit($visit);
        return response()->json(new VisitResource($visit));
    }

    public function complete(string $id): JsonResponse
    {
        $visit = $this->visitService->getVisitById($id);

        if (! $visit) {
            return response()->json(['message' => 'Visit not found'], 404);
        }

        $visit = $this->visitService->completeVisit($visit);
        return response()->json(new VisitResource($visit));
    }

    public function cancel(Request $request, string $id): JsonResponse
    {
        $visit = $this->visitService->getVisitById($id);

        if (! $visit) {
            return response()->json(['message' => 'Visit not found'], 404);
        }

        $visit = $this->visitService->cancelVisit($visit, $request->reason);
        return response()->json(new VisitResource($visit));
    }

    public function statistics(Request $request): JsonResponse
    {
        $stats = $this->visitService->getVisitStatistics($request->all());
        return response()->json($stats);
    }

    public function upcoming(Request $request): JsonResponse
    {
        $days   = $request->input('days', 7);
        $visits = $this->visitService->getUpcomingVisits($days);
        return response()->json($visits);
    }

    public function storeWithVisitor(StoreVisitorWithVisitRequest $request): JsonResponse
    {
        $result = $this->visitService->createVisitorWithVisit($request->validated());

        return response()->json([
            'visitor' => new VisitorResource($result['visitor']),
            'visit'   => new VisitResource($result['visit']),
        ], 201);
    }

    public function getAllInmate(Request $request): JsonResponse
    {
        $result = $this->visitService->getAllInmate($request->all(), $request->per_page);
        return response()->json(['success' => true, 'data' => $result]);
    }
}
