<?php

namespace App\Policies;

use App\Models\Staff;
use App\Policies\BasePolicy;
use Modules\AdminManagement\Entities\HqAdmin;
use Modules\BelongingManagement\Entities\Belonging;

class BelongingPolicy extends BasePolicy
{
    /**
     * Get the base permission name for belongings
     */
    protected function getBasePermission(): string
    {
        return 'belongings';
    }

    /**
     * Override view permissions to include manage permissions
     */
    protected function getViewPermissions(): array
    {
        return [
            'view-belongings',
            'manage-belongings',
            'manage-all-belongings',
            'manage-all'
        ];
    }

    /**
     * Custom authorization for retrieving belongings
     */
    public function retrieve($user, Belonging $belonging): bool
    {
        return $this->canManageResource($user, $belonging, 'retrieve-belongings');
    }

    /**
     * Custom authorization for disposing belongings
     */
    public function dispose($user, Belonging $belonging): bool
    {
        return $this->canManageResource($user, $belonging, 'dispose-belongings');
    }

    /**
     * Custom authorization for cash transactions
     */
    public function manageCashTransactions($user, Belonging $belonging): bool
    {
        return $this->canManageResource($user, $belonging, 'manage-cash-transactions');
    }

    /**
     * Custom authorization for viewing cash statements
     */
    public function viewCashStatements($user): bool
    {
        return $this->hasAnyPermission($user, [
            'view-cash-statements',
            'manage-cash-transactions',
            'manage-all-belongings',
            'manage-all'
        ]);
    }

    /**
     * Custom authorization for viewing cash balances
     */
    public function viewCashBalances($user): bool
    {
        return $this->hasAnyPermission($user, [
            'view-cash-balances',
            'manage-cash-transactions',
            'manage-all-belongings',
            'manage-all'
        ]);
    }

    /**
     * Custom authorization for viewing dashboard
     */
    public function viewDashboard($user): bool
    {
        return $this->hasAnyPermission($user, [
            'view-belongings-dashboard',
            'view-belongings',
            'manage-all-belongings',
            'manage-all'
        ]);
    }

    /**
     * Custom authorization for viewing reports
     */
    public function viewReports($user): bool
    {
        return $this->hasAnyPermission($user, [
            'view-belonging-reports',
            'view-belongings',
            'manage-all-belongings',
            'manage-all'
        ]);
    }
}
