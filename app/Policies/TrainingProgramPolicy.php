<?php

namespace App\Policies;

use App\Models\Staff;
use App\Policies\BasePolicy;
use Modules\AdminManagement\Entities\HqAdmin;
use Modules\TrainingManagement\Entities\TrainingProgram;

class TrainingProgramPolicy extends BasePolicy
{

    /**
     * Get the base permission name for training programs
     */
    protected function getBasePermission(): string
    {
        return 'training-programs';
    }

    /**
     * Override view permissions to include manage permissions
     */
    protected function getViewPermissions(): array
    {
        return [
            'view-training-programs',
            'manage-training-programs',
            'manage-all-training'
        ];
    }

    /**
     * Custom authorization for training program enrollment
     */
    public function enroll($user, TrainingProgram $trainingProgram): bool
    {
        return $this->canManageResource($user, $trainingProgram, 'manage-training-enrollments');
    }

    /**
     * Custom authorization for viewing training reports
     */
    public function viewReports($user): bool
    {
        return $this->hasAnyPermission($user, [
            'view-training-reports',
            'manage-training-programs',
            'manage-all-training'
        ]);
    }
}

    /**
     * Determine whether the user can create models.
     */
    public function create($user): bool
    {
        // Super admin access
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }

        // Staff access
        if ($user instanceof Staff) {
            return $user->hasAnyPermission([
                'create-training-programs',
                'manage-training-programs',
                'manage-all-training'
            ]);
        }

        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update($user, TrainingProgram $trainingProgram): bool
    {
        // Super admin access
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }

        // Staff access
        if ($user instanceof Staff) {
            // Check basic permission
            if (!$user->hasAnyPermission([
                'update-training-programs',
                'manage-training-programs',
                'manage-all-training'
            ])) {
                return false;
            }

            // Super admin can update anything
            if ($user->hasPermission('manage-all-training')) {
                return true;
            }

            // Check state/prison access
            if ($trainingProgram->state_id && !$user->hasStateAccess($trainingProgram->state_id)) {
                return false;
            }

            if ($trainingProgram->prison_id && !$user->hasPrisonAccess($trainingProgram->prison_id)) {
                return false;
            }

            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete($user, TrainingProgram $trainingProgram): bool
    {
        // Super admin access
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }

        // Staff access
        if ($user instanceof Staff) {
            // Check basic permission
            if (!$user->hasAnyPermission([
                'delete-training-programs',
                'manage-training-programs',
                'manage-all-training'
            ])) {
                return false;
            }

            // Super admin can delete anything
            if ($user->hasPermission('manage-all-training')) {
                return true;
            }

            // Check state/prison access
            if ($trainingProgram->state_id && !$user->hasStateAccess($trainingProgram->state_id)) {
                return false;
            }

            if ($trainingProgram->prison_id && !$user->hasPrisonAccess($trainingProgram->prison_id)) {
                return false;
            }

            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore($user, TrainingProgram $trainingProgram): bool
    {
        return $this->delete($user, $trainingProgram);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete($user, TrainingProgram $trainingProgram): bool
    {
        // Super admin access
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }

        // Staff access
        if ($user instanceof Staff) {
            return $user->hasPermission('manage-all-training');
        }

        return false;
    }

    /**
     * Determine whether the user can manage enrollments for the training program.
     */
    public function manageEnrollments($user, TrainingProgram $trainingProgram): bool
    {
        // Super admin access
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }

        // Staff access
        if ($user instanceof Staff) {
            // Check basic permission
            if (!$user->hasAnyPermission([
                'manage-training-enrollments',
                'manage-training-programs',
                'manage-all-training'
            ])) {
                return false;
            }

            // Super admin can manage anything
            if ($user->hasPermission('manage-all-training')) {
                return true;
            }

            // Check state/prison access
            if ($trainingProgram->state_id && !$user->hasStateAccess($trainingProgram->state_id)) {
                return false;
            }

            if ($trainingProgram->prison_id && !$user->hasPrisonAccess($trainingProgram->prison_id)) {
                return false;
            }

            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view reports for the training program.
     */
    public function viewReports($user, TrainingProgram $trainingProgram): bool
    {
        // Super admin access
        if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
            return true;
        }

        // Staff access
        if ($user instanceof Staff) {
            // Check basic permission
            if (!$user->hasAnyPermission([
                'view-training-reports',
                'manage-training-programs',
                'manage-all-training'
            ])) {
                return false;
            }

            // Super admin can view anything
            if ($user->hasPermission('manage-all-training')) {
                return true;
            }

            // Check state/prison access
            if ($trainingProgram->state_id && !$user->hasStateAccess($trainingProgram->state_id)) {
                return false;
            }

            if ($trainingProgram->prison_id && !$user->hasPrisonAccess($trainingProgram->prison_id)) {
                return false;
            }

            return true;
        }

        return false;
    }
}
