<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Auth\Access\AuthorizationException;
use Modules\AdminManagement\Entities\HqAdmin;

class SuperAdminMiddleware
{
    /**
     * Handle an incoming request.
     * This middleware ensures that only super admin users can access the route.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            throw new AuthorizationException('Authentication required.');
        }

        $user = auth()->user();

        // Check if user is a super admin
        if (!($user instanceof HqAdmin && $user->isSuperAdmin())) {
            throw new AuthorizationException('Super admin access required.');
        }

        return $next($request);
    }
}
