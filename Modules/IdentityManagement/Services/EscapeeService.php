<?php
namespace App\Services;

use App\Models\Escapee;
use App\Models\EscapeeBatch;

class EscapeeService
{
    public function listBatches(array $filters = [])
    {
        return EscapeeBatch::with(['state', 'prison', 'escapees'])
            ->when(! empty($filters['state_id']), fn($q) => $q->where('state_id', $filters['state_id']))
            ->when(! empty($filters['prison_id']), fn($q) => $q->where('prison_id', $filters['prison_id']))
            ->paginate($filters['per_page'] ?? 15);
    }

    public function getBatch($id)
    {
        return EscapeeBatch::with('escapees')->findOrFail($id);
    }

    public function createBatch(array $data)
    {
        return EscapeeBatch::create($data);
    }

    public function updateBatch($id, array $data)
    {
        $batch = EscapeeBatch::findOrFail($id);
        $batch->update($data);
        return $batch;
    }

    public function deleteBatch($id)
    {
        $batch = EscapeeBatch::findOrFail($id);
        $batch->escapees()->delete(); // Optional: delete escapees first
        return $batch->delete();
    }

    public function addEscapeeToBatch($batchId, array $escapeeData)
    {
        $escapeeData['batch_id'] = $batchId;
        return Escapee::create($escapeeData);
    }

    public function updateEscapee($id, array $data)
    {
        $escapee = Escapee::findOrFail($id);
        $escapee->update($data);
        return $escapee;
    }

    public function deleteEscapee($id)
    {
        return Escapee::findOrFail($id)->delete();
    }
}
