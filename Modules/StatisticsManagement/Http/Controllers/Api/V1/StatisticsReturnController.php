<?php
namespace Modules\StatisticsManagement\Http\Controllers\Api\V1;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Gate;
use Modules\StatisticsManagement\Services\StatsOptimizedService;

class StatisticsReturnController extends Controller
{
    protected $statsService;

    public function __construct(StatsOptimizedService $statsService)
    {
        $this->middleware('auth:sanctum');
        $this->middleware('authorize:view-statistics-health')->only(['getHealth']);
        $this->middleware('authorize:view-statistics-summary')->only(['getSummary']);
        $this->middleware('authorize:view-statistics-demographic')->only(['getDemographic']);
        $this->middleware('authorize:view-statistics-security')->only(['getSecurity']);
        $this->statsService = $statsService;
    }
    public function getSummary(Request $request): JsonResponse
    {
        if (!Gate::allows('view-statistics-summary')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            // 'year' => 'nullable|integer|min:1900|max:' . date('Y'),
            'state_id'   => 'nullable|integer',
            'prison_id'  => 'nullable|integer',
            'start_date' => 'nullable|date',
            'end_date'   => 'nullable|date|after_or_equal:start_date',
            'zone_id'    => 'nullable|integer',
        ]);

        $statistics = $this->statsService->getPopulationStatistics($validated);

        return response()->json([
            'success'         => true,
            'data'            => $statistics,
            'filters_applied' => $validated,
        ])->header('Cache-Control', 'public, max-age=600');
    }

    public function getDemographic(Request $request): JsonResponse
    {
        if (!Gate::allows('view-statistics-demographic')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'state_id'   => 'nullable|integer',
            'prison_id'  => 'nullable|integer',
            'start_date' => 'nullable|date',
            'end_date'   => 'nullable|date|after_or_equal:start_date',
            'zone_id'    => 'nullable|integer',
        ]);

        $statistics = $this->statsService->getDemographicStatistics($validated);

        return response()->json([
            'success'         => true,
            'data'            => $statistics,
            'filters_applied' => $validated,
        ])->header('Cache-Control', 'public, max-age=600');
    }

    public function getSecurity(Request $request): JsonResponse
    {
        if (!Gate::allows('view-statistics-security')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'state_id'   => 'nullable|integer',
            'prison_id'  => 'nullable|integer',
            'start_date' => 'nullable|date',
            'end_date'   => 'nullable|date|after_or_equal:start_date',
            'zone_id'    => 'nullable|integer',
        ]);

        $statistics = $this->statsService->getSecurityStatistics($validated);

        return response()->json([
            'success'         => true,
            'data'            => $statistics,
            'filters_applied' => $validated,
        ])->header('Cache-Control', 'public, max-age=600');
    }
    public function getHealth(Request $request): JsonResponse
    {
        // Authorization handled by middleware

        $validated = $request->validate([
            'state_id'   => 'nullable|integer',
            'prison_id'  => 'nullable|integer',
            'start_date' => 'nullable|date',
            'end_date'   => 'nullable|date|after_or_equal:start_date',
            'zone_id'    => 'nullable|integer',
        ]);

        $statistics = $this->statsService->getHealthAndWelfareStatistics($validated);

        return response()->json([
            'success'         => true,
            'data'            => $statistics,
            'filters_applied' => $validated,
        ])->header('Cache-Control', 'public, max-age=600');
    }

}
