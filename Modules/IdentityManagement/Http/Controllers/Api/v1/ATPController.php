<?php

namespace Modules\IdentityManagement\Http\Controllers\Api\v1;

use App\Traits\Common;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Gate;
use Modules\IdentityManagement\Entities\ATP;
use Modules\IdentityManagement\Entities\Inmate;

class ATPController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    use Common;

    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }
    /**
     * The index function in this PHP code performs a custom query on the Inmate model and returns the
     * filtered results.
     *
     * @param Request request The  parameter is an instance of the Request class, which
     * represents an HTTP request. It contains information about the request such as the request
     * method, headers, query parameters, and request body.
     *
     * @return The code is returning a paginated list of Inmate models. If the 'count' parameter is
     * present in the query, it will return the count of Inmate models that match the filtered query
     * instead.
     */
    public function index(Request $request)
    {
        Gate::authorize('viewAny', ATP::class);

        /*
        /// Do a custom query on Inmate model,
        */
        $noIncludeArray=array('count');
        $query = $request->query();
        //// This method will do a custom query to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query,$noIncludeArray);
        isset($query['count']) ? $inmate = ATP::where($filteredQuery)->count():$inmate = ATP::orderBy('id','desc')->where($filteredQuery)->paginate();
        return $inmate;

    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        Gate::authorize('create', ATP::class);

        // Implementation needed
        return response()->json(['message' => 'Not implemented'], 501);
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(ATP $awaiting)
    {
        Gate::authorize('view', $awaiting);

        return response()->json(['data' => $awaiting]);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        $awaiting = ATP::findOrFail($id);
        Gate::authorize('update', $awaiting);

        // Implementation needed
        return response()->json(['message' => 'Not implemented'], 501);
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy($id)
    {
        $awaiting = ATP::findOrFail($id);
        Gate::authorize('delete', $awaiting);

        // Implementation needed
        return response()->json(['message' => 'Not implemented'], 501);
    }
}
