<?php

namespace Modules\IdentityManagement\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;

class EscapeeCsvUploadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Gate::allows('manage-escapees');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'csv_file' => [
                'required',
                'file',
                'mimes:csv,txt',
                'max:10240', // 10MB max
            ],
            'batch_title' => 'required|string|max:255',
            'batch_description' => 'nullable|string|max:1000',
            'state_id' => 'required|exists:states,id',
            'prison_id' => 'required|exists:prisons,id',
            'skip_header' => 'boolean',
            'preview_only' => 'boolean',
            'delimiter' => 'nullable|string|in:comma,semicolon,tab,pipe',
            'encoding' => 'nullable|string|in:utf-8,iso-8859-1,windows-1252',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'csv_file.required' => 'Please select a CSV file to upload.',
            'csv_file.file' => 'The uploaded file must be a valid file.',
            'csv_file.mimes' => 'The file must be a CSV file (.csv or .txt).',
            'csv_file.max' => 'The CSV file size cannot exceed 10MB.',
            'batch_title.required' => 'Batch title is required.',
            'batch_title.max' => 'Batch title cannot exceed 255 characters.',
            'batch_description.max' => 'Batch description cannot exceed 1000 characters.',
            'state_id.required' => 'Please select a state.',
            'state_id.exists' => 'The selected state is invalid.',
            'prison_id.required' => 'Please select a prison.',
            'prison_id.exists' => 'The selected prison is invalid.',
            'delimiter.in' => 'Delimiter must be one of: comma, semicolon, tab, pipe.',
            'encoding.in' => 'Encoding must be one of: utf-8, iso-8859-1, windows-1252.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'csv_file' => 'CSV file',
            'batch_title' => 'batch title',
            'batch_description' => 'batch description',
            'state_id' => 'state',
            'prison_id' => 'prison',
            'skip_header' => 'skip header row',
            'preview_only' => 'preview only',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if ($this->hasFile('csv_file')) {
                $file = $this->file('csv_file');
                
                // Check if file is readable
                if (!$file->isValid()) {
                    $validator->errors()->add('csv_file', 'The uploaded file is corrupted or invalid.');
                    return;
                }

                // Basic CSV structure validation
                try {
                    $handle = fopen($file->getPathname(), 'r');
                    if ($handle === false) {
                        $validator->errors()->add('csv_file', 'Unable to read the CSV file.');
                        return;
                    }

                    // Read first few lines to validate structure
                    $lineCount = 0;
                    $delimiter = $this->getDelimiterCharacter();
                    
                    while (($line = fgetcsv($handle, 0, $delimiter)) !== false && $lineCount < 5) {
                        if ($lineCount === 0) {
                            // Validate minimum required columns
                            if (count($line) < 3) {
                                $validator->errors()->add('csv_file', 'CSV file must have at least 3 columns (prisoner_no, escape_date, current_status).');
                                break;
                            }
                        }
                        $lineCount++;
                    }
                    
                    fclose($handle);

                    // Check if file has data rows
                    if ($lineCount < 2) {
                        $validator->errors()->add('csv_file', 'CSV file must contain at least one data row.');
                    }

                } catch (\Exception $e) {
                    $validator->errors()->add('csv_file', 'Error reading CSV file: ' . $e->getMessage());
                }
            }
        });
    }

    /**
     * Get the delimiter character based on the selected option.
     */
    private function getDelimiterCharacter(): string
    {
        return match ($this->input('delimiter', 'comma')) {
            'semicolon' => ';',
            'tab' => "\t",
            'pipe' => '|',
            default => ',',
        };
    }

    /**
     * Get the processed data for the service.
     */
    public function getProcessedData(): array
    {
        return [
            'csv_file' => $this->file('csv_file'),
            'batch_data' => [
                'title' => $this->input('batch_title'),
                'description' => $this->input('batch_description'),
                'state_id' => $this->input('state_id'),
                'prison_id' => $this->input('prison_id'),
            ],
            'options' => [
                'skip_header' => $this->boolean('skip_header', true),
                'preview_only' => $this->boolean('preview_only', false),
                'delimiter' => $this->getDelimiterCharacter(),
                'encoding' => $this->input('encoding', 'utf-8'),
            ],
        ];
    }
}
