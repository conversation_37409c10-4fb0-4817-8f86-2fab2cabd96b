<?php

namespace Modules\AdminManagement\Entities;

use App\Models\States;
use App\Traits\HasRoleBasedSuperAdmin;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Auth\Authenticatable as AuthenticatableTrait;
use Modules\AdminManagement\Database\factories\HqAdminFactory;

class HqAdmin extends Model implements Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, AuthenticatableTrait, HasRoleBasedSuperAdmin;
    protected $table = 'hq_admins';
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'admin_type',
        'first_name',
        'last_name',
        'email',
        'phone',
        'address',
        'state_id',
        'account_type',
        'date',
        'status',
        'password',
    ];


    protected $hidden = [
        'password',
    ];


    protected $casts = [
        'date' => 'datetime',
    ];

    public function state(): BelongsTo
    {
        return $this->belongsTo(States::class, 'state_id');
    }

    /**
     * Check if this HQ Admin is a super admin
     * Super admins have unrestricted access to all system features
     *
     * Two types of super admin access:
     * 1. HqAdmin Super Users: All HQ admins automatically have super admin privileges
     * 2. Role-Based Super Admins: Users assigned roles named 'superadmin', 'super-admin', or 'super_admin'
     */
    public function isSuperAdmin(): bool
    {
        // Type 1: All HQ admins are super admins by default
        // This gives all users in the hq_admins table super admin privileges
        if (true) { // All HQ admins are super admins
            return true;
        }

        // Type 2: Role-based super admin check (fallback, but not needed since all HQ admins are super admins)
        return $this->isRoleBasedSuperAdmin();
    }

    /**
     * Get the admin type name
     */
    public function getAdminTypeName(): string
    {
        $adminTypeNames = [
            1 => 'CGC (Controller General of Corrections)',
            2 => 'DCG (Deputy Controller General)',
            3 => 'ACG (Assistant Controller General)',
            4 => 'MOI (Ministry of Interior)',
            5 => 'CCS (Controller of Corrections Service)',
            6 => 'HQ ICT (Headquarters ICT Admin)',
        ];

        return $adminTypeNames[$this->admin_type] ?? 'Unknown Admin Type';
    }

    /**
     * Check if HQ Admin has a specific permission
     * Super admins have all permissions, others have none by default
     */
    public function hasPermission(string $permission, ?int $stateId = null, ?int $prisonId = null): bool
    {
        // Super admins have unrestricted access to all permissions
        if ($this->isSuperAdmin()) {
            return true;
        }

        // Non-super admin HQ admins don't have permissions by default
        // This can be extended in the future if needed
        return false;
    }

    /**
     * Check if HQ Admin has any of the given permissions
     * Super admins have all permissions, others have none by default
     */
    public function hasAnyPermission(array $permissions, ?int $stateId = null, ?int $prisonId = null): bool
    {
        // Super admins have unrestricted access to all permissions
        if ($this->isSuperAdmin()) {
            return true;
        }

        // Non-super admin HQ admins don't have permissions by default
        return false;
    }

    // protected static function newFactory(): HqAdminFactory
    // {
    //     //return HqAdminFactory::new();
    // }
}
