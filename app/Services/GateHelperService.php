<?php

namespace App\Services;

use Illuminate\Support\Facades\Gate;
use App\Models\Staff;
use Modules\AdminManagement\Entities\HqAdmin;

class GateHelperService
{
    /**
     * Register a simple permission gate
     */
    public static function registerPermissionGate(string $gateName, array $permissions): void
    {
        Gate::define($gateName, function ($user) use ($permissions) {
            // Enhanced Super admin bypass - Check all user types for super admin privileges
            if (method_exists($user, 'isSuperAdmin') && $user->isSuperAdmin()) {
                return true;
            }

            // Legacy HqAdmin check (fallback)
            if ($user instanceof HqAdmin) {
                return true; // All HQ admins are super admins
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission($permissions) ||
                       $user->accountType?->hasAnyDefaultPermission($permissions);
            }

            return false;
        });
    }

    /**
     * Register multiple permission gates at once
     */
    public static function registerPermissionGates(array $gates): void
    {
        foreach ($gates as $gateName => $permissions) {
            self::registerPermissionGate($gateName, (array) $permissions);
        }
    }

    /**
     * Register a role-based gate
     */
    public static function registerRoleGate(string $gateName, array $roles, ?int $minHierarchyLevel = null): void
    {
        Gate::define($gateName, function ($user) use ($roles, $minHierarchyLevel) {
            // Super admin bypass
            if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
                return true;
            }

            if ($user instanceof Staff) {
                $hasRole = $user->hasAnyRole($roles);
                $hasHierarchy = $minHierarchyLevel ?
                    ($user->accountType?->hierarchy_level >= $minHierarchyLevel) : true;

                return $hasRole || $hasHierarchy;
            }

            return false;
        });
    }

    /**
     * Register a scoped permission gate (with state/prison access)
     */
    public static function registerScopedGate(string $gateName, string $permission): void
    {
        Gate::define($gateName, function ($user, $targetStaff = null) use ($permission) {
            // Super admin bypass
            if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
                return true;
            }

            if (!$user instanceof Staff) {
                return false;
            }

            // System admin can access everything
            if ($user->hasPermission('system-admin')) {
                return true;
            }

            // If no target staff, just check permission
            if (!$targetStaff) {
                return $user->hasPermission($permission);
            }

            // Check scoped access
            if ($user->hasPermission('state-admin') &&
                $user->assigned_state === $targetStaff->assigned_state) {
                return true;
            }

            if ($user->hasPermission('prison-admin') &&
                $user->prison === $targetStaff->prison) {
                return true;
            }

            // Self-access
            return $user->id === $targetStaff->id;
        });
    }

    /**
     * Register access gates for state and prison
     */
    public static function registerAccessGates(): void
    {
        Gate::define('access-state', function ($user, int $stateId) {
            if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasStateAccess($stateId);
            }

            return false;
        });

        Gate::define('access-prison', function ($user, int $prisonId) {
            if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPrisonAccess($prisonId);
            }

            return false;
        });
    }

    /**
     * Register all common system gates
     */
    public static function registerSystemGates(): void
    {
        // Role-based gates
        self::registerRoleGate('system-admin', ['super-admin', 'system-admin'], 10);
        self::registerRoleGate('state-admin', ['super-admin', 'system-admin', 'state-admin'], 8);
        self::registerRoleGate('prison-admin', ['super-admin', 'system-admin', 'state-admin', 'prison-admin'], 6);

        // Account type gates
        self::registerRoleGate('account-type-admin', [], 8);
        self::registerRoleGate('account-type-supervisor', [], 6);
        self::registerRoleGate('account-type-officer', [], 4);

        // Access gates
        self::registerAccessGates();

        // RBAC management gates
        self::registerScopedGate('view-staff-permissions', 'view-staff-permissions');
        self::registerScopedGate('assign-roles', 'assign-roles');
        self::registerScopedGate('remove-roles', 'remove-roles');
        self::registerScopedGate('manage-staff-roles', 'manage-staff-roles');
        self::registerScopedGate('manage-staff-permissions', 'manage-staff-permissions');
    }

    /**
     * Register module-specific gates
     */
    public static function registerModuleGates(): void
    {
        // Training Management
        self::registerPermissionGates([
            'view-training-programs' => ['view-training-programs', 'manage-training-programs', 'manage-all-training'],
            'create-training-programs' => ['create-training-programs', 'manage-training-programs', 'manage-all-training'],
            'update-training-programs' => ['update-training-programs', 'manage-training-programs', 'manage-all-training'],
            'delete-training-programs' => ['delete-training-programs', 'manage-training-programs', 'manage-all-training'],
            'manage-training-enrollments' => ['manage-training-enrollments', 'manage-training-programs', 'manage-all-training'],
            'view-training-reports' => ['view-training-reports', 'manage-training-programs', 'manage-all-training'],
            'manage-all-training' => ['manage-all-training'],
        ]);

        // Health Management
        self::registerPermissionGates([
            'view-medical-history' => ['view-medical-history', 'manage-all-health'],
            'create-medical-history' => ['create-medical-history', 'manage-all-health'],
            'update-medical-history' => ['update-medical-history', 'manage-all-health'],
            'delete-medical-history' => ['delete-medical-history', 'manage-all-health'],
            'view-medical-appointments' => ['view-medical-appointments', 'manage-all-health'],
            'create-medical-appointments' => ['create-medical-appointments', 'manage-all-health'],
            'update-medical-appointments' => ['update-medical-appointments', 'manage-all-health'],
            'delete-medical-appointments' => ['delete-medical-appointments', 'manage-all-health'],
            'manage-all-health' => ['manage-all-health'],
        ]);

        // Legal Aid Management
        self::registerPermissionGates([
            'view-legal-aid' => ['view-legal-aid', 'manage-all-legal-aid'],
            'create-legal-aid' => ['create-legal-aid', 'manage-all-legal-aid'],
            'update-legal-aid' => ['update-legal-aid', 'manage-all-legal-aid'],
            'delete-legal-aid' => ['delete-legal-aid', 'manage-all-legal-aid'],
            'view-legal-representatives' => ['view-legal-representatives', 'manage-all-legal-aid'],
            'create-legal-representatives' => ['create-legal-representatives', 'manage-all-legal-aid'],
            'manage-all-legal-aid' => ['manage-all-legal-aid'],
        ]);

        // Belonging Management
        self::registerPermissionGates([
            'view-belongings' => ['view-belongings', 'manage-all-belongings'],
            'create-belongings' => ['create-belongings', 'manage-all-belongings'],
            'update-belongings' => ['update-belongings', 'manage-all-belongings'],
            'delete-belongings' => ['delete-belongings', 'manage-all-belongings'],
            'manage-cash-transactions' => ['manage-cash-transactions', 'manage-all-belongings'],
            'view-cash-statements' => ['view-cash-statements', 'manage-all-belongings'],
            'retrieve-belongings' => ['retrieve-belongings', 'manage-all-belongings'],
            'dispose-belongings' => ['dispose-belongings', 'manage-all-belongings'],
        ]);

        // Admin Management
        self::registerPermissionGates([
            'view-admin-users' => ['view-admin-users', 'manage-admin-users', 'manage-all'],
            'create-admin-users' => ['create-admin-users', 'manage-admin-users', 'manage-all'],
            'update-admin-users' => ['update-admin-users', 'manage-admin-users', 'manage-all'],
            'delete-admin-users' => ['delete-admin-users', 'manage-admin-users', 'manage-all'],
            'manage-admin-types' => ['manage-admin-types', 'manage-all'],
            'manage-account-types' => ['manage-account-types', 'manage-all'],
            'view-state-zone-admins' => ['view-state-zone-admins', 'manage-state-zone-admins', 'manage-all'],
            'create-state-zone-admins' => ['create-state-zone-admins', 'manage-state-zone-admins', 'manage-all'],
            'update-state-zone-admins' => ['update-state-zone-admins', 'manage-state-zone-admins', 'manage-all'],
            'delete-state-zone-admins' => ['delete-state-zone-admins', 'manage-state-zone-admins', 'manage-all'],
            'view-hq-admins' => ['view-hq-admins', 'manage-hq-admins', 'manage-all'],
            'create-hq-admins' => ['create-hq-admins', 'manage-hq-admins', 'manage-all'],
            'update-hq-admins' => ['update-hq-admins', 'manage-hq-admins', 'manage-all'],
            'delete-hq-admins' => ['delete-hq-admins', 'manage-hq-admins', 'manage-all'],
            'manage-admin-users' => ['manage-admin-users', 'manage-all'],
            'manage-state-zone-admins' => ['manage-state-zone-admins', 'manage-all'],
            'manage-hq-admins' => ['manage-hq-admins', 'manage-all'],
        ]);

        // Staff Management
        self::registerPermissionGates([
            'view-staff' => ['view-staff', 'manage-staff', 'manage-all'],
            'create-staff' => ['create-staff', 'manage-staff', 'manage-all'],
            'update-staff' => ['update-staff', 'manage-staff', 'manage-all'],
            'delete-staff' => ['delete-staff', 'manage-staff', 'manage-all'],
            'manage-staff' => ['manage-staff', 'manage-all'],
        ]);

        // Case Management
        self::registerPermissionGates([
            'view-cases' => ['view-cases', 'manage-cases', 'manage-all'],
            'create-cases' => ['create-cases', 'manage-cases', 'manage-all'],
            'update-cases' => ['update-cases', 'manage-cases', 'manage-all'],
            'delete-cases' => ['delete-cases', 'manage-cases', 'manage-all'],
            'view-escort-duties' => ['view-escort-duties', 'manage-escort-duties', 'manage-cases', 'manage-all'],
            'create-escort-duties' => ['create-escort-duties', 'manage-escort-duties', 'manage-cases', 'manage-all'],
            'update-escort-duties' => ['update-escort-duties', 'manage-escort-duties', 'manage-cases', 'manage-all'],
            'delete-escort-duties' => ['delete-escort-duties', 'manage-escort-duties', 'manage-cases', 'manage-all'],
            'manage-cases' => ['manage-cases', 'manage-all'],
            'manage-escort-duties' => ['manage-escort-duties', 'manage-cases', 'manage-all'],
        ]);

        // Cloud Management
        self::registerPermissionGates([
            'upload-to-cloud' => ['upload-to-cloud', 'manage-all'],
            'view-cloud-statistics' => ['view-cloud-statistics', 'manage-all'],
            'retry-cloud-uploads' => ['retry-cloud-uploads', 'manage-all'],
            'verify-cloud-structure' => ['verify-cloud-structure', 'manage-all'],
            'edit-upload' => ['edit-upload', 'manage-all'],
            'sync-data' => ['sync-data', 'manage-all'],
        ]);

        // Statistics Management permissions
        self::registerPermissionGates([
            'view-statistics-dashboard' => ['view-statistics-dashboard', 'manage-statistics', 'manage-all'],
            'view-statistics-summary' => ['view-statistics-summary', 'manage-statistics', 'manage-all'],
            'view-statistics-demographic' => ['view-statistics-demographic', 'manage-statistics', 'manage-all'],
            'view-statistics-security' => ['view-statistics-security', 'manage-statistics', 'manage-all'],
            'view-statistics-health' => ['view-statistics-health', 'manage-statistics', 'manage-all'],
            'manage-statistics' => ['manage-statistics', 'manage-all'],
        ]);

        // Legal Aid Management permissions
        self::registerPermissionGates([
            'view-legal-aid-dashboard' => ['view-legal-aid-dashboard', 'manage-legal-aid', 'manage-all'],
            'view-legal-firms' => ['view-legal-firms', 'manage-legal-aid', 'manage-all'],
            'create-legal-firms' => ['create-legal-firms', 'manage-legal-aid', 'manage-all'],
            'update-legal-firms' => ['update-legal-firms', 'manage-legal-aid', 'manage-all'],
            'delete-legal-firms' => ['delete-legal-firms', 'manage-legal-aid', 'manage-all'],
            'manage-legal-aid' => ['manage-legal-aid', 'manage-all'],
        ]);

        // Health Management permissions
        self::registerPermissionGates([
            'view-health-dashboard' => ['view-health-dashboard', 'manage-health', 'manage-all'],
            'view-health-reports' => ['view-health-reports', 'manage-health', 'manage-all'],
            'view-medical-history' => ['view-medical-history', 'manage-health', 'manage-all'],
            'create-medical-history' => ['create-medical-history', 'manage-health', 'manage-all'],
            'update-medical-history' => ['update-medical-history', 'manage-health', 'manage-all'],
            'delete-medical-history' => ['delete-medical-history', 'manage-health', 'manage-all'],
            'view-medical-diagnostics' => ['view-medical-diagnostics', 'manage-health', 'manage-all'],
            'create-medical-diagnostics' => ['create-medical-diagnostics', 'manage-health', 'manage-all'],
            'update-medical-diagnostics' => ['update-medical-diagnostics', 'manage-health', 'manage-all'],
            'delete-medical-diagnostics' => ['delete-medical-diagnostics', 'manage-health', 'manage-all'],
            'view-medical-appointments' => ['view-medical-appointments', 'manage-health', 'manage-all'],
            'create-medical-appointments' => ['create-medical-appointments', 'manage-health', 'manage-all'],
            'update-medical-appointments' => ['update-medical-appointments', 'manage-health', 'manage-all'],
            'delete-medical-appointments' => ['delete-medical-appointments', 'manage-health', 'manage-all'],
            'view-medical-lab-tests' => ['view-medical-lab-tests', 'manage-health', 'manage-all'],
            'create-medical-lab-tests' => ['create-medical-lab-tests', 'manage-health', 'manage-all'],
            'update-medical-lab-tests' => ['update-medical-lab-tests', 'manage-health', 'manage-all'],
            'delete-medical-lab-tests' => ['delete-medical-lab-tests', 'manage-health', 'manage-all'],
            'view-medical-prescriptions' => ['view-medical-prescriptions', 'manage-health', 'manage-all'],
            'create-medical-prescriptions' => ['create-medical-prescriptions', 'manage-health', 'manage-all'],
            'update-medical-prescriptions' => ['update-medical-prescriptions', 'manage-health', 'manage-all'],
            'delete-medical-prescriptions' => ['delete-medical-prescriptions', 'manage-health', 'manage-all'],
            'manage-health' => ['manage-health', 'manage-all'],
        ]);

        // Belonging Management permissions
        self::registerPermissionGates([
            'view-belongings' => ['view-belongings', 'manage-belongings', 'manage-all'],
            'view-belongings-dashboard' => ['view-belongings-dashboard', 'manage-belongings', 'manage-all'],
            'create-belongings' => ['create-belongings', 'manage-belongings', 'manage-all'],
            'update-belongings' => ['update-belongings', 'manage-belongings', 'manage-all'],
            'delete-belongings' => ['delete-belongings', 'manage-belongings', 'manage-all'],
            'retrieve-belongings' => ['retrieve-belongings', 'manage-belongings', 'manage-all'],
            'dispose-belongings' => ['dispose-belongings', 'manage-belongings', 'manage-all'],
            'view-cash-balances' => ['view-cash-balances', 'manage-belongings', 'manage-all'],
            'manage-cash-transactions' => ['manage-cash-transactions', 'manage-belongings', 'manage-all'],
            'manage-belongings' => ['manage-belongings', 'manage-all'],
        ]);

        // Training Management permissions
        self::registerPermissionGates([
            'view-training-reports' => ['view-training-reports', 'manage-training-programs', 'manage-all'],
            'manage-all-training' => ['manage-all-training', 'manage-training-programs', 'manage-all'],
        ]);

        // Case Management permissions
        self::registerPermissionGates([
            'view-court-sessions' => ['view-court-sessions', 'manage-cases', 'manage-all'],
            'create-court-sessions' => ['create-court-sessions', 'manage-cases', 'manage-all'],
            'update-court-sessions' => ['update-court-sessions', 'manage-cases', 'manage-all'],
            'delete-court-sessions' => ['delete-court-sessions', 'manage-cases', 'manage-all'],
        ]);

        // Identity Management permissions
        self::registerPermissionGates([
            'view-inmates' => ['view-inmates', 'manage-inmates', 'manage-all'],
            'create-inmates' => ['create-inmates', 'manage-inmates', 'manage-all'],
            'update-inmates' => ['update-inmates', 'manage-inmates', 'manage-all'],
            'delete-inmates' => ['delete-inmates', 'manage-inmates', 'manage-all'],
            'view-warrants' => ['view-warrants', 'manage-inmates', 'manage-all'],
            'create-warrants' => ['create-warrants', 'manage-inmates', 'manage-all'],
            'update-warrants' => ['update-warrants', 'manage-inmates', 'manage-all'],
            'delete-warrants' => ['delete-warrants', 'manage-inmates', 'manage-all'],
        ]);

        // Staff Management permissions
        self::registerPermissionGates([
            'view-staff' => ['view-staff', 'manage-staff', 'manage-all'],
            'create-staff' => ['create-staff', 'manage-staff', 'manage-all'],
            'update-staff' => ['update-staff', 'manage-staff', 'manage-all'],
            'delete-staff' => ['delete-staff', 'manage-staff', 'manage-all'],
            'activate-staff' => ['activate-staff', 'manage-staff', 'manage-all'],
            'deactivate-staff' => ['deactivate-staff', 'manage-staff', 'manage-all'],
        ]);

        // Visitation Management permissions
        self::registerPermissionGates([
            'view-visits' => ['view-visits', 'manage-visits', 'manage-all'],
            'create-visits' => ['create-visits', 'manage-visits', 'manage-all'],
            'update-visits' => ['update-visits', 'manage-visits', 'manage-all'],
            'delete-visits' => ['delete-visits', 'manage-visits', 'manage-all'],
            'view-visitors' => ['view-visitors', 'manage-visits', 'manage-all'],
            'create-visitors' => ['create-visitors', 'manage-visits', 'manage-all'],
            'update-visitors' => ['update-visitors', 'manage-visits', 'manage-all'],
            'delete-visitors' => ['delete-visitors', 'manage-visits', 'manage-all'],
        ]);

        // Additional missing permissions
        self::registerPermissionGates([
            'force-delete-belongings' => ['force-delete-belongings', 'manage-belongings', 'manage-all'],
            'restore-belongings' => ['restore-belongings', 'manage-belongings', 'manage-all'],
            'view-legal-representatives' => ['view-legal-representatives', 'manage-legal-aid', 'manage-all'],
            'create-legal-representatives' => ['create-legal-representatives', 'manage-legal-aid', 'manage-all'],
            'update-legal-representatives' => ['update-legal-representatives', 'manage-legal-aid', 'manage-all'],
            'delete-legal-representatives' => ['delete-legal-representatives', 'manage-legal-aid', 'manage-all'],
            'view-medical-history' => ['view-medical-history', 'manage-health', 'manage-all'],
        ]);
    }

    /**
     * Register all gates
     */
    public static function registerAllGates(): void
    {
        self::registerSystemGates();
        self::registerModuleGates();
    }
}
