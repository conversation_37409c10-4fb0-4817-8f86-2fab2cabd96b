<?php

namespace Modules\HealthManagement\Http\Controllers;

use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Gate;
use Illuminate\Contracts\Support\Renderable;

class HealthManagementController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index()
    {
        if (!Gate::allows('view-health-dashboard')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return Inertia::render('../../Modules/HealthManagement/Resources/assets/js/views/index');
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        if (!Gate::allows('create-medical-history')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return view('healthmanagement::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        if (!Gate::allows('create-medical-history')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        //
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function show($id)
    {
        if (!Gate::allows('view-medical-history')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return view('healthmanagement::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        if (!Gate::allows('update-medical-history')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return view('healthmanagement::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        if (!Gate::allows('update-medical-history')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        //
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        if (!Gate::allows('delete-medical-history')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        //
    }
}
