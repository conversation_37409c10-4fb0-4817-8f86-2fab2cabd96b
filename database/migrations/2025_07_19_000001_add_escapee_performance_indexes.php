<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add additional indexes to escapee_batches table for performance
        Schema::table('escapee_batches', function (Blueprint $table) {
            // Composite indexes for common query patterns
            $table->index(['state_id', 'batch_status'], 'idx_escapee_batches_state_status');
            $table->index(['prison_id', 'batch_status'], 'idx_escapee_batches_prison_status');
            $table->index(['batch_status', 'created_at'], 'idx_escapee_batches_status_created');
            
            // Indexes for filtering and sorting
            $table->index('created_by', 'idx_escapee_batches_created_by');
            $table->index('reviewed_at', 'idx_escapee_batches_reviewed_at');
            $table->index('investigated_at', 'idx_escapee_batches_investigated_at');
            $table->index('closed_at', 'idx_escapee_batches_closed_at');
            
            // Full-text search index for title and description (if using MySQL)
            if (config('database.default') === 'mysql') {
                $table->fullText(['title', 'description'], 'idx_escapee_batches_fulltext');
            }
        });

        // Add additional indexes to escapees table for performance
        Schema::table('escapees', function (Blueprint $table) {
            // Composite indexes for common query patterns
            $table->index(['current_status', 'escape_date'], 'idx_escapees_status_escape_date');
            $table->index(['current_status', 'recapture_date'], 'idx_escapees_status_recapture_date');
            $table->index(['batch_id', 'current_status'], 'idx_escapees_batch_status');
            
            // Date range queries
            $table->index(['escape_date', 'current_status'], 'idx_escapees_escape_date_status');
            $table->index(['recapture_date', 'current_status'], 'idx_escapees_recapture_date_status');
            
            // Staff tracking
            $table->index('investigating_officer', 'idx_escapees_investigating_officer');
            $table->index('created_by', 'idx_escapees_created_by');
            $table->index('updated_by', 'idx_escapees_updated_by');
            
            // Location-based queries (through batch relationship)
            // These will be used with joins to escapee_batches
            $table->index(['batch_id', 'escape_date'], 'idx_escapees_batch_escape_date');
            
            // Soft deletes performance
            $table->index(['deleted_at', 'current_status'], 'idx_escapees_deleted_status');
        });

        // Add indexes to inmates_all_class_register for escapee-related queries
        Schema::table('inmates_all_class_register', function (Blueprint $table) {
            // Check if indexes don't already exist
            $sm = Schema::getConnection()->getDoctrineSchemaManager();
            $indexesNames = array_keys($sm->listTableIndexes('inmates_all_class_register'));
            
            if (!in_array('idx_inmates_escapee_status', $indexesNames)) {
                $table->index(['escapee', 'recaptured'], 'idx_inmates_escapee_status');
            }
            
            if (!in_array('idx_inmates_escapee_prisoner_no', $indexesNames)) {
                $table->index(['prisoner_no', 'escapee'], 'idx_inmates_escapee_prisoner_no');
            }
            
            if (!in_array('idx_inmates_prison_escapee', $indexesNames)) {
                $table->index(['prison_id', 'escapee'], 'idx_inmates_prison_escapee');
            }
            
            if (!in_array('idx_inmates_state_escapee', $indexesNames)) {
                $table->index(['state_id', 'escapee'], 'idx_inmates_state_escapee');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes from escapee_batches table
        Schema::table('escapee_batches', function (Blueprint $table) {
            $table->dropIndex('idx_escapee_batches_state_status');
            $table->dropIndex('idx_escapee_batches_prison_status');
            $table->dropIndex('idx_escapee_batches_status_created');
            $table->dropIndex('idx_escapee_batches_created_by');
            $table->dropIndex('idx_escapee_batches_reviewed_at');
            $table->dropIndex('idx_escapee_batches_investigated_at');
            $table->dropIndex('idx_escapee_batches_closed_at');
            
            if (config('database.default') === 'mysql') {
                $table->dropFullText('idx_escapee_batches_fulltext');
            }
        });

        // Drop indexes from escapees table
        Schema::table('escapees', function (Blueprint $table) {
            $table->dropIndex('idx_escapees_status_escape_date');
            $table->dropIndex('idx_escapees_status_recapture_date');
            $table->dropIndex('idx_escapees_batch_status');
            $table->dropIndex('idx_escapees_escape_date_status');
            $table->dropIndex('idx_escapees_recapture_date_status');
            $table->dropIndex('idx_escapees_investigating_officer');
            $table->dropIndex('idx_escapees_created_by');
            $table->dropIndex('idx_escapees_updated_by');
            $table->dropIndex('idx_escapees_batch_escape_date');
            $table->dropIndex('idx_escapees_deleted_status');
        });

        // Drop indexes from inmates_all_class_register table
        Schema::table('inmates_all_class_register', function (Blueprint $table) {
            $sm = Schema::getConnection()->getDoctrineSchemaManager();
            $indexesNames = array_keys($sm->listTableIndexes('inmates_all_class_register'));
            
            if (in_array('idx_inmates_escapee_status', $indexesNames)) {
                $table->dropIndex('idx_inmates_escapee_status');
            }
            
            if (in_array('idx_inmates_escapee_prisoner_no', $indexesNames)) {
                $table->dropIndex('idx_inmates_escapee_prisoner_no');
            }
            
            if (in_array('idx_inmates_prison_escapee', $indexesNames)) {
                $table->dropIndex('idx_inmates_prison_escapee');
            }
            
            if (in_array('idx_inmates_state_escapee', $indexesNames)) {
                $table->dropIndex('idx_inmates_state_escapee');
            }
        });
    }
};
