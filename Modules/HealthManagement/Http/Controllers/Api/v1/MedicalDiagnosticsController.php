<?php

namespace Modules\HealthManagement\Http\Controllers\Api\v1;

use App\Traits\Common;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

use Modules\HealthManagement\Entities\MedicalDiagnostics;

class MedicalDiagnosticsController extends Controller
{
    use Common;

    public function __construct()
    {
        $this->middleware('authorize:view-medical-diagnostics')->only(['index', 'show', 'getDiagnosticsByPrisoner']);
        $this->middleware('authorize:create-medical-diagnostics')->only(['store']);
        $this->middleware('authorize:update-medical-diagnostics')->only(['update']);
        $this->middleware('authorize:delete-medical-diagnostics')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(Request $request)
    {
        // Authorization handled by middleware

        $noIncludeArray = array('count', 'page', 'include', 'paginate', 'start_date', 'end_date', 'diagnostic_status', 'search');
        $relations=array();
        $includeString = explode(',',$request->include);
        $query = $request->query();
        $paginate = $request->paginate? $request->paginate:20;

        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

        $diagnostics = MedicalDiagnostics::orderBy('created_at','desc');

        $diagnostics->where($filteredQuery);

        if ($request->has('start_date') && $request->has('end_date')) {
            $diagnostics->whereBetween('diagnostic_date', [$request->start_date, $request->end_date]);
        } else if ($request->has('start_date')) {
            $diagnostics->where('diagnostic_date', '>=', $request->start_date);
        } else if ($request->has('end_date')) {
            $diagnostics->where('diagnostic_date', '<=', $request->end_date);
        }

        if ($request->has('diagnostic_status')) {
            $diagnostics->where('diagnostic_status', $request->diagnostic_status);
        }

        if ($request->has('search')) {
            $searchTerm = $request->search;
            $diagnostics->where(function($query) use ($searchTerm) {
                $query->where('prisoner_no', 'like', "%{$searchTerm}%")
                      ->orWhere('diagnostic_type', 'like', "%{$searchTerm}%")
                      ->orWhere('results', 'like', "%{$searchTerm}%");
            });
        }

        $results = $diagnostics->paginate($paginate);

        if($request->include){
            foreach($includeString as $string){
                $checkRelationshipArray = $this->shouldIncludeRelation($relations, $string);
                if($checkRelationshipArray) $results->load($string);
            }
        }

        // Append the full name to each model
        $results->getCollection()->each(function ($data) {
            if ($data->inmate) {
                $data->prisoner_fullname = trim("{$data->inmate->surname} {$data->inmate->first_name} {$data->inmate->other_names}");
                unset($data->inmate);
            }
        });

        if($request->count){
            $results = $results->total();
        }

        return response()->json(['data' => $results]);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        return view('healthmanagement::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        // Authorization handled by middleware

        $request->validate([
            'prisoner_no' => 'required|exists:inmates_all_class_register,prisoner_no',
            'lab_test_id' => 'required|exists:medical_lab_tests,id',
            'diagnostic_type' => 'required|string',
            'diagnostic_date' => 'required|date',
            'results' => 'required|string',
            'diagnostic_status' => 'required|in:pending,completed,canceled',
        ]);

        $diagnostic = new MedicalDiagnostics($request->all());
        $diagnostic->save();

        return response()->json([
            'message' => 'Medical diagnostic record created successfully',
            'data' => $diagnostic
        ], 201);
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Authorization handled by middleware

        $diagnostic = MedicalDiagnostics::findOrFail($id);
        return response()->json([
            'data' => $diagnostic
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        return view('healthmanagement::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        // Authorization handled by middleware

        $request->validate([
            'prisoner_no' => 'required|exists:inmates_all_class_register,prisoner_no',
            'lab_test_id' => 'required|exists:medical_lab_tests,id',
            'diagnostic_type' => 'required|string',
            'diagnostic_date' => 'required|date',
            'results' => 'required|string',
            'diagnostic_status' => 'required|in:pending,completed,canceled',
            'status' => 'nullable|integer|in:0,1',
        ]);

        $diagnostic = MedicalDiagnostics::findOrFail($id);
        $diagnostic->update($request->all());

        return response()->json([
            'message' => 'Medical diagnostic record updated successfully',
            'data' => $diagnostic
        ]);
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        // Authorization handled by middleware

        $diagnostic = MedicalDiagnostics::findOrFail($id);
        $diagnostic->delete();

        return response()->json([
            'message' => 'Medical diagnostic record deleted successfully'
        ]);
    }

     /**
     * Get all medical diagnostics for a specific prisoner
     * @param string $prisoner_no
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDiagnosticsByPrisoner($prisoner_no)
    {
        // Authorization handled by middleware

        $diagnostics = MedicalDiagnostics::where('prisoner_no', $prisoner_no)
            ->orderBy('diagnostic_date', 'desc')
            ->get();

        if ($diagnostics->isEmpty()) {
            return response()->json([
                'message' => 'No diagnostic records found for this prisoner',
                'data' => []
            ]);
        }

        return response()->json([
            'data' => $diagnostics
        ]);
    }
}
