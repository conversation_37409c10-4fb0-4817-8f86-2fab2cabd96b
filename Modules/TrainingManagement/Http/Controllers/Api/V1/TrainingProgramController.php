<?php

namespace Modules\TrainingManagement\Http\Controllers\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Auth;
use Modules\TrainingManagement\Entities\TrainingProgram;
use Modules\TrainingManagement\Entities\InmateTrainingEnrollment;
use Modules\TrainingManagement\Http\Requests\StoreTrainingProgramRequest;
use Modules\TrainingManagement\Http\Requests\UpdateTrainingProgramRequest;
use Modules\TrainingManagement\Http\Requests\TrainingProgramRequest;
use Modules\TrainingManagement\Services\TrainingProgramService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class TrainingProgramController extends Controller
{
    use AuthorizesRequests;

    protected TrainingProgramService $trainingProgramService;

    public function __construct(TrainingProgramService $trainingProgramService)
    {
        $this->middleware('auth:sanctum');
        $this->trainingProgramService = $trainingProgramService;
    }

    /**
     * Display a listing of training programs.
     */
    public function index(Request $request): JsonResponse
    {
        // Authorization is now handled by middleware or policy
        Gate::authorize('viewAny', TrainingProgram::class);

        $user = Auth::user();
        $perPage = $request->get('per_page', 20);
        $search = $request->get('search');
        $type = $request->get('type');
        $status = $request->get('status');
        $stateId = $request->get('state_id');
        $prisonId = $request->get('prison_id');

        $query = TrainingProgram::with(['state', 'prison']);

        // Apply state/prison filtering based on user access
        if (!Gate::allows('manage-all-training')) {
            if ($stateId) {
                $query->where('state_id', $stateId);
            }

            if ($prisonId) {
                $query->where('prison_id', $prisonId);
            }

            // If no filters provided, use user's defaults
            if (!$stateId && !$prisonId) {
                if ($user->prison) {
                    $query->where('prison_id', $user->prison);

                    // Only apply state filter if prison has a state
                    $prison = \App\Models\Prison::find($user->prison);
                    if ($prison && $prison->state_id) {
                        $query->where('state_id', $prison->state_id);
                    }
                }
            }
        } else {
            // Admin users can filter by any state/prison
            if ($stateId) {
                $query->where('state_id', $stateId);
            }
            if ($prisonId) {
                $query->where('prison_id', $prisonId);
            }
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('supervisor', 'like', "%{$search}%");
            });
        }

        if ($type) {
            $query->where('type', $type);
        }

        if ($status) {
            $query->where('status', $status);
        }

        $programs = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $programs,
        ]);
    }

    /**
     * Store a newly created training program.
     */
    public function store(StoreTrainingProgramRequest $request): JsonResponse
    {
        $this->authorize('create', TrainingProgram::class);

        try {
            $trainingProgram = TrainingProgram::create($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Training program created successfully.',
                'data' => $trainingProgram->load(['state', 'prison'])
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create training program.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified training program.
     */
    public function show(TrainingProgram $trainingProgram): JsonResponse
    {
        $this->authorize('view', $trainingProgram);

        return response()->json([
            'success' => true,
            'data' => $trainingProgram->load(['state', 'prison', 'enrollments.inmate'])
        ]);
    }

    /**
     * Update the specified training program.
     */
    public function update(UpdateTrainingProgramRequest $request, TrainingProgram $trainingProgram): JsonResponse
    {
        $this->authorize('update', $trainingProgram);

        try {
            $trainingProgram->update($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Training program updated successfully.',
                'data' => $trainingProgram->load(['state', 'prison'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update training program.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified training program.
     */
    public function destroy(TrainingProgram $trainingProgram): JsonResponse
    {
        $this->authorize('delete', $trainingProgram);

        try {
            // Check if there are active enrollments
            $activeEnrollments = $trainingProgram->enrollments()
                ->where('status', 'active')
                ->count();

            if ($activeEnrollments > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete training program with active enrollments.'
                ], 422);
            }

            $trainingProgram->delete();

            return response()->json([
                'success' => true,
                'message' => 'Training program deleted successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete training program.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get training program statistics.
     */
    public function statistics(Request $request): JsonResponse
    {
        // Authorization handled by route middleware

        $user = Auth::user();
        $query = TrainingProgram::query();

        // Apply filtering based on user access
        if (!Gate::allows('manage-all-training')) {
            if ($user->prison) {
                $query->where('prison_id', $user->prison);
            }
            if ($user->assigned_state) {
                $query->where('state_id', $user->assigned_state);
            }
        }

        $totalPrograms = $query->count();
        $activePrograms = $query->where('status', 'active')->count();
        $completedPrograms = $query->where('status', 'completed')->count();
        $totalEnrollments = InmateTrainingEnrollment::whereIn('training_program_id', $query->pluck('id'))->count();

        return response()->json([
            'success' => true,
            'data' => [
                'total_programs' => $totalPrograms,
                'active_programs' => $activePrograms,
                'completed_programs' => $completedPrograms,
                'total_enrollments' => $totalEnrollments
            ]
        ]);
    }

    /**
     * Get available training programs for enrollment.
     */
    public function available(Request $request): JsonResponse
    {
        if (!Gate::allows('view-training-programs')) {
            return response()->json([
                'message' => 'Unauthorized. You do not have permission to view training programs.'
            ], 403);
        }

        $user = Auth::user();
        $query = TrainingProgram::where('status', 'active')
            ->where('enrollment_open', true);

        // Apply filtering based on user access
        if (!Gate::allows('manage-all-training')) {
            if ($user->prison) {
                $query->where('prison_id', $user->prison);
            }
            if ($user->assigned_state) {
                $query->where('state_id', $user->assigned_state);
            }
        }

        $programs = $query->with(['state', 'prison'])->get();

        return response()->json([
            'success' => true,
            'data' => $programs
        ]);
    }
}
