<?php
namespace Modules\StatisticsManagement\Http\Controllers\Api\V1;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Gate;
use Modules\StatisticsManagement\Services\DashboardReturnService;

class DashboardReturnController extends Controller
{
    protected $statsDashService;

    public function __construct(DashboardReturnService $statsDashService)
    {
        $this->middleware('auth:sanctum');
        $this->middleware('authorize:view-statistics-dashboard');
        $this->statsDashService = $statsDashService;
    }
    public function getGeneral(Request $request): JsonResponse
    {
        // Authorization handled by middleware

        $validated = $request->validate([
            'state_id'   => 'nullable|integer',
            'prison_id'  => 'nullable|integer',
            'start_date' => 'nullable|date',
            'end_date'   => 'nullable|date|after_or_equal:start_date',
            'zone_id'    => 'nullable|integer',
        ]);

        $statistics = $this->statsDashService->getGeneralStatistics($validated);

        return response()->json([
            'success'         => true,
            'data'            => $statistics,
            'filters_applied' => $validated,
        ])->header('Cache-Control', 'public, max-age=600');

    }

    public function getThreeMonthsStats(Request $request): JsonResponse
    {
        if (!Gate::allows('view-statistics-dashboard')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'state_id'   => 'nullable|integer',
            'prison_id'  => 'nullable|integer',
            'start_date' => 'nullable|date',
            'end_date'   => 'nullable|date|after_or_equal:start_date',
            'zone_id'    => 'nullable|integer',
        ]);

        $statistics = $this->statsDashService->getMonthlyStatistics($validated);

        return response()->json([
            'success'         => true,
            'data'            => $statistics,
            'filters_applied' => $validated,
        ])->header('Cache-Control', 'public, max-age=600');
    }
}
