<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Modules\AdminManagement\Entities\HqAdmin;

trait SuperAdminTrait
{
    /**
     * Check if the current authenticated user is a super admin
     * Supports all user types with enhanced super admin functionality
     */
    protected function isSuperAdmin(): bool
    {
        $user = auth()->user();

        // Check if user has isSuperAdmin method and call it
        if (method_exists($user, 'isSuperAdmin')) {
            return $user->isSuperAdmin();
        }

        // Fallback: Check if user is HqAdmin (legacy support)
        if ($user instanceof HqAdmin) {
            return true; // All HQ admins are super admins
        }

        return false;
    }

    /**
     * Get the current authenticated super admin user
     * Returns null if user is not a super admin
     * Now supports all user types
     */
    protected function getSuperAdmin()
    {
        $user = auth()->user();
        if ($this->isSuperAdmin()) {
            return $user;
        }
        return null;
    }

    /**
     * Ensure the current user is a super admin, otherwise return error response
     */
    protected function requireSuperAdmin(): ?JsonResponse
    {
        if (!$this->isSuperAdmin()) {
            return response()->json([
                'message' => 'Access denied. Super admin privileges required.',
                'error' => 'Insufficient permissions'
            ], Response::HTTP_FORBIDDEN);
        }
        return null;
    }

    /**
     * Execute a callback only if user is super admin
     */
    protected function ifSuperAdmin(callable $callback, callable $fallback = null)
    {
        if ($this->isSuperAdmin()) {
            return $callback($this->getSuperAdmin());
        }

        if ($fallback) {
            return $fallback();
        }

        return response()->json([
            'message' => 'Access denied. Super admin privileges required.',
            'error' => 'Insufficient permissions'
        ], Response::HTTP_FORBIDDEN);
    }

    /**
     * Get super admin information for logging/auditing
     * Now supports all user types
     */
    protected function getSuperAdminInfo(): array
    {
        $superAdmin = $this->getSuperAdmin();
        if (!$superAdmin) {
            return [];
        }

        $info = [
            'id' => $superAdmin->id ?? null,
            'email' => $superAdmin->email ?? null,
            'user_type' => get_class($superAdmin),
            'timestamp' => now()->toISOString()
        ];

        // Add name based on user type
        if ($superAdmin instanceof HqAdmin) {
            $info['name'] = ($superAdmin->first_name ?? '') . ' ' . ($superAdmin->last_name ?? '');
            $info['admin_type'] = $superAdmin->admin_type ?? null;
            if (method_exists($superAdmin, 'getAdminTypeName')) {
                $info['admin_type_name'] = $superAdmin->getAdminTypeName();
            }
        } elseif (isset($superAdmin->first_name)) {
            $info['name'] = ($superAdmin->first_name ?? '') . ' ' . ($superAdmin->last_name ?? '');
        } elseif (isset($superAdmin->name)) {
            $info['name'] = $superAdmin->name;
        }

        return $info;
    }
}
