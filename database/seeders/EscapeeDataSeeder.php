<?php

namespace Database\Seeders;

use App\Models\Escapee;
use App\Models\EscapeeBatch;
use Modules\IdentityManagement\Entities\Inmate;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EscapeeDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure we have batches and inmates
        if (EscapeeBatch::count() === 0) {
            $this->command->warn('No escapee batches found. Please run EscapeeBatchSeeder first.');
            return;
        }

        if (Inmate::count() === 0) {
            $this->command->warn('No inmates found. Please ensure inmate data is available.');
            return;
        }

        $this->command->info('Creating escapees...');

        // Get all batches
        $batches = EscapeeBatch::all();
        
        // Get a sample of inmates to use as escapees
        $inmates = Inmate::inRandomOrder()->limit(100)->get();
        
        if ($inmates->isEmpty()) {
            $this->command->error('No inmates available for seeding escapees.');
            return;
        }

        // Create escapees for each batch
        foreach ($batches as $batch) {
            // Determine number of escapees for this batch (1-5)
            $escapeeCount = fake()->numberBetween(1, 5);
            
            // Get random inmates for this batch
            $batchInmates = $inmates->random(min($escapeeCount, $inmates->count()));
            
            foreach ($batchInmates as $inmate) {
                // Skip if this inmate is already an escapee
                if (Escapee::where('prisoner_id', $inmate->prisoner_no)->exists()) {
                    continue;
                }
                
                // Create escapee record
                $escapeDate = Carbon::parse($batch->created_at)->subDays(fake()->numberBetween(1, 30));
                $currentStatus = fake()->randomElement(['escaped', 'recaptured', 'deceased', 'unknown']);
                
                $escapeeData = [
                    'batch_id' => $batch->id,
                    'prisoner_id' => $inmate->prisoner_no,
                    'escape_date' => $escapeDate,
                    'escape_location' => fake()->randomElement([
                        'Prison Yard',
                        'Workshop Area',
                        'Kitchen',
                        'Medical Center',
                        'Visiting Room',
                        'Exercise Area',
                        'Dormitory',
                        'Transport Vehicle',
                        'Court Premises',
                        'Hospital',
                        'Work Detail',
                    ]),
                    'escape_circumstances' => fake()->sentence(),
                    'current_status' => $currentStatus,
                    'uploaded' => fake()->boolean(30),
                    'status' => true,
                    'created_at' => $batch->created_at,
                    'updated_at' => now(),
                ];
                
                // Add recapture details if status is recaptured
                if ($currentStatus === 'recaptured') {
                    $escapeeData['recapture_date'] = Carbon::parse($escapeDate)->addDays(fake()->numberBetween(1, 60));
                    $escapeeData['recapture_location'] = fake()->city();
                }
                
                $escapee = Escapee::create($escapeeData);
                
                // Update inmate record
                $inmate->escapee = 1;
                $inmate->recaptured = ($currentStatus === 'recaptured') ? 1 : 0;
                $inmate->save();
            }
        }

        $totalEscapees = Escapee::count();
        $this->command->info("Created {$totalEscapees} escapees successfully.");
    }
}
