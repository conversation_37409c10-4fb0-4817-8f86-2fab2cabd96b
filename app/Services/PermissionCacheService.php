<?php

namespace App\Services;

use App\Models\Staff;
use App\Models\Permission;
use Modules\AdminManagement\Entities\HqAdmin;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PermissionCacheService
{
    protected int $cacheTtl = 3600; // 1 hour
    protected string $cachePrefix = 'rbac';

    /**
     * Get cached permissions for a staff member
     */
    public function getStaffPermissions(Staff $staff, ?int $stateId = null, ?int $prisonId = null): Collection
    {
        $cacheKey = $this->buildPermissionCacheKey($staff->id, $stateId, $prisonId);
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($staff, $stateId, $prisonId) {
            return $this->loadStaffPermissions($staff, $stateId, $prisonId);
        });
    }

    /**
     * Get cached roles for a staff member
     */
    public function getStaffRoles(Staff $staff): Collection
    {
        $cacheKey = $this->buildRolesCacheKey($staff->id);
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($staff) {
            return $this->loadStaffRoles($staff);
        });
    }

    /**
     * Check if staff has specific permission (cached)
     */
    public function staffHasPermission(Staff $staff, string $permission, ?int $stateId = null, ?int $prisonId = null): bool
    {
        // Check account type default permissions first (no caching needed - single query)
        if ($staff->accountType && $staff->accountType->hasDefaultPermission($permission)) {
            return true;
        }

        $permissions = $this->getStaffPermissions($staff, $stateId, $prisonId);
        return $permissions->contains($permission);
    }

    /**
     * Batch check multiple permissions for staff
     */
    public function staffHasAnyPermission(Staff $staff, array $permissions, ?int $stateId = null, ?int $prisonId = null): bool
    {
        // Check account type permissions first
        if ($staff->accountType) {
            foreach ($permissions as $permission) {
                if ($staff->accountType->hasDefaultPermission($permission)) {
                    return true;
                }
            }
        }

        $staffPermissions = $this->getStaffPermissions($staff, $stateId, $prisonId);
        return $staffPermissions->intersect($permissions)->isNotEmpty();
    }

    /**
     * Batch check all permissions for staff
     */
    public function staffHasAllPermissions(Staff $staff, array $permissions, ?int $stateId = null, ?int $prisonId = null): bool
    {
        $staffPermissions = $this->getStaffPermissions($staff, $stateId, $prisonId);
        
        foreach ($permissions as $permission) {
            // Check account type first
            if ($staff->accountType && $staff->accountType->hasDefaultPermission($permission)) {
                continue;
            }
            
            if (!$staffPermissions->contains($permission)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Clear all caches for a specific staff member
     */
    public function clearStaffCache(Staff $staff): void
    {
        $patterns = [
            $this->buildRolesCacheKey($staff->id),
            $this->buildPermissionCacheKey($staff->id, '*', '*'),
        ];

        foreach ($patterns as $pattern) {
            if (str_contains($pattern, '*')) {
                $this->clearCacheByPattern($pattern);
            } else {
                Cache::forget($pattern);
            }
        }

        Log::info("Cleared RBAC cache for staff ID: {$staff->id}");
    }

    /**
     * Clear all RBAC caches
     */
    public function clearAllCaches(): void
    {
        $this->clearCacheByPattern($this->cachePrefix . ':*');
        Log::info('Cleared all RBAC caches');
    }

    /**
     * Warm cache for frequently accessed permissions
     */
    public function warmCache(Staff $staff): void
    {
        // Pre-load common permission combinations
        $commonScopes = [
            [null, null], // System-wide
            [$staff->assigned_state, null], // State-level
            [$staff->assigned_state, $staff->prison], // Prison-level
        ];

        foreach ($commonScopes as [$stateId, $prisonId]) {
            $this->getStaffPermissions($staff, $stateId, $prisonId);
        }

        $this->getStaffRoles($staff);
    }

    /**
     * Batch warm cache for multiple staff members
     */
    public function batchWarmCache(Collection $staffMembers): void
    {
        foreach ($staffMembers as $staff) {
            $this->warmCache($staff);
        }
    }

    /**
     * Load staff permissions from database
     */
    protected function loadStaffPermissions(Staff $staff, ?int $stateId = null, ?int $prisonId = null): Collection
    {
        $permissions = collect();

        // Get account type permissions
        if ($staff->accountType) {
            $accountTypePermissions = $staff->accountType->getAllPermissions();
            $permissions = $permissions->merge($accountTypePermissions);
        }

        // Get role-based permissions with scope filtering
        $query = $staff->roles()
            ->with('permissions')
            ->where('staff_roles.is_active', true)
            ->where(function ($query) {
                $query->whereNull('staff_roles.expires_at')
                      ->orWhere('staff_roles.expires_at', '>', now());
            });

        // Apply scope filtering
        if ($stateId || $prisonId) {
            $query->where(function ($scopeQuery) use ($stateId, $prisonId) {
                // System-wide roles always apply
                $scopeQuery->where('roles.scope', 'system');
                
                if ($stateId) {
                    $scopeQuery->orWhere(function ($stateQuery) use ($stateId) {
                        $stateQuery->where('staff_roles.state_id', $stateId)
                                  ->whereIn('roles.scope', ['state', 'prison']);
                    });
                }
                
                if ($prisonId) {
                    $scopeQuery->orWhere(function ($prisonQuery) use ($prisonId) {
                        $prisonQuery->where('staff_roles.prison_id', $prisonId)
                                   ->where('roles.scope', 'prison');
                    });
                }
            });
        }

        $rolePermissions = $query->get()
            ->pluck('permissions')
            ->flatten()
            ->where('is_active', true)
            ->pluck('name');

        $permissions = $permissions->merge($rolePermissions);

        return $permissions->unique()->values();
    }

    /**
     * Load staff roles from database
     */
    protected function loadStaffRoles(Staff $staff): Collection
    {
        return $staff->roles()
            ->with(['state', 'prison'])
            ->where('staff_roles.is_active', true)
            ->where(function ($query) {
                $query->whereNull('staff_roles.expires_at')
                      ->orWhere('staff_roles.expires_at', '>', now());
            })
            ->get();
    }

    /**
     * Build cache key for permissions
     */
    protected function buildPermissionCacheKey(int $staffId, ?int $stateId = null, ?int $prisonId = null): string
    {
        return "{$this->cachePrefix}:permissions:{$staffId}:" . ($stateId ?? 'null') . ':' . ($prisonId ?? 'null');
    }

    /**
     * Build cache key for roles
     */
    protected function buildRolesCacheKey(int $staffId): string
    {
        return "{$this->cachePrefix}:roles:{$staffId}";
    }

    /**
     * Clear cache by pattern (works with Redis and array cache)
     */
    protected function clearCacheByPattern(string $pattern): void
    {
        try {
            $store = Cache::getStore();
            
            if (method_exists($store, 'getRedis')) {
                // Redis cache
                $redis = $store->getRedis();
                $keys = $redis->keys($pattern);
                if (!empty($keys)) {
                    $redis->del($keys);
                }
            } else {
                // Array/File cache - clear all (less efficient but works)
                if (str_contains($pattern, $this->cachePrefix)) {
                    Cache::flush();
                }
            }
        } catch (\Exception $e) {
            Log::warning("Failed to clear cache by pattern: {$pattern}", ['error' => $e->getMessage()]);
            // Fallback to clearing all cache
            Cache::flush();
        }
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        $totalStaff = Staff::count();
        $cachedStaff = 0;
        
        // This is a simplified version - in production you might want more detailed stats
        return [
            'total_staff' => $totalStaff,
            'estimated_cached_staff' => $cachedStaff,
            'cache_ttl' => $this->cacheTtl,
            'cache_prefix' => $this->cachePrefix,
        ];
    }
}
