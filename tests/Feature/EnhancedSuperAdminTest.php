<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Role;
use App\Models\Staff;
use Modules\AdminManagement\Entities\HqAdmin;
use Modules\AdminManagement\Entities\StateZoneAdmin;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON><PERSON>\Sanctum\Sanctum;

class EnhancedSuperAdminTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations
        $this->artisan('migrate');
        
        // Create super admin role
        Role::create([
            'name' => 'superadmin',
            'display_name' => 'Super Administrator',
            'description' => 'Full system access',
            'scope' => 'system',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function hq_admin_users_are_automatically_super_admins()
    {
        $hqAdmin = HqAdmin::factory()->create([
            'admin_type' => 6, // HQ ICT
            'email' => '<EMAIL>',
            'status' => 1,
        ]);

        $this->assertTrue($hqAdmin->isSuperAdmin());
        $this->assertTrue($hqAdmin->hasPermission('any-permission'));
        $this->assertTrue($hqAdmin->hasAnyPermission(['any-permission', 'another-permission']));
    }

    /** @test */
    public function staff_can_be_super_admin_through_role_assignment()
    {
        $staff = Staff::factory()->create();
        $superAdminRole = Role::where('name', 'superadmin')->first();

        // Initially not a super admin
        $this->assertFalse($staff->isSuperAdmin());

        // Assign super admin role
        $staff->roles()->attach($superAdminRole->id, [
            'state_id' => null,
            'prison_id' => null,
            'assigned_at' => now(),
            'expires_at' => null,
            'is_active' => true,
        ]);

        // Refresh the model to get updated relationships
        $staff->refresh();

        // Now should be a super admin
        $this->assertTrue($staff->isSuperAdmin());
    }

    /** @test */
    public function state_zone_admin_can_be_super_admin_through_role_assignment()
    {
        $stateZoneAdmin = StateZoneAdmin::factory()->create();
        $superAdminRole = Role::where('name', 'superadmin')->first();

        // Initially not a super admin
        $this->assertFalse($stateZoneAdmin->isSuperAdmin());

        // Assign super admin role through universal role assignments
        $stateZoneAdmin->universalRoles()->attach($superAdminRole->id, [
            'state_id' => null,
            'prison_id' => null,
            'assigned_at' => now(),
            'expires_at' => null,
            'is_active' => true,
        ]);

        // Refresh the model to get updated relationships
        $stateZoneAdmin->refresh();

        // Now should be a super admin
        $this->assertTrue($stateZoneAdmin->isSuperAdmin());
        $this->assertTrue($stateZoneAdmin->hasPermission('any-permission'));
        $this->assertTrue($stateZoneAdmin->hasAnyPermission(['any-permission', 'another-permission']));
    }

    /** @test */
    public function super_admin_can_access_protected_routes()
    {
        $hqAdmin = HqAdmin::factory()->create([
            'admin_type' => 6,
            'email' => '<EMAIL>',
            'status' => 1,
        ]);

        Sanctum::actingAs($hqAdmin);

        // Test access to various module routes
        $protectedRoutes = [
            '/api/v1/admin_users',
            '/api/v1/inmates',
            '/api/v1/training-programs',
            '/api/v1/cases/sittings',
            '/api/v1/staff',
        ];

        foreach ($protectedRoutes as $route) {
            $response = $this->getJson($route);
            
            // Should not get 403 Forbidden (super admin should have access)
            $this->assertNotEquals(403, $response->getStatusCode(), 
                "Super admin should have access to {$route}");
        }
    }

    /** @test */
    public function non_super_admin_gets_proper_authorization_checks()
    {
        $regularStaff = Staff::factory()->create();

        Sanctum::actingAs($regularStaff);

        // Test that regular staff without permissions get proper authorization
        $response = $this->getJson('/api/v1/admin_users');
        
        // Should get 403 or other authorization error (not 500)
        $this->assertContains($response->getStatusCode(), [401, 403], 
            'Regular staff should get authorization error, not server error');
    }
}
