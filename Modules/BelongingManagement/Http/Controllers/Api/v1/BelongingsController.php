<?php
namespace Modules\BelongingManagement\Http\Controllers\Api\v1;

use DateTime;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Gate;
use Modules\BelongingManagement\Entities\Belonging;

// use Modules\BelongingsManagement\Entities\WalletTransaction;

class BelongingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('authorize:view-belongings')->only(['index', 'show', 'trashed', 'getInmateBelongings']);
        $this->middleware('authorize:create-belongings')->only(['store']);
        $this->middleware('authorize:update-belongings')->only(['update']);
        $this->middleware('authorize:retrieve-belongings')->only(['retrieve']);
        $this->middleware('authorize:dispose-belongings')->only(['dispose']);
        $this->middleware('authorize:delete-belongings')->only(['destroy']);
        $this->middleware('authorize:view-cash-balances')->only(['getBalance']);
        $this->middleware('authorize:manage-cash-transactions')->only(['deposit', 'withdraw']);
        $this->middleware('authorize:view-cash-statements')->only(['statement']);
        $this->middleware('authorize:view-belongings-dashboard')->only(['getDashboardStats']);
        $this->middleware('authorize:force-delete-belongings')->only(['forceDelete']);
        $this->middleware('authorize:restore-belongings')->only(['restore']);
    }

    public function index(Request $request)
    {
        // Authorization handled by middleware

        $query = Belonging::query();

        // Apply filters
        if ($request->has('prisoner_no')) {
            $query->where('prisoner_no', $request->input('prisoner_no'));
        }

        if ($request->has('item_type')) {
            $query->where('item_type', $request->input('item_type'));
        }

        if ($request->has('is_stored')) {
            $query->where('is_stored', filter_var($request->input('is_stored'), FILTER_VALIDATE_BOOLEAN));
        }

        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }

        if ($request->has('item_name')) {
            $query->where('item_name', 'like', '%' . $request->input('item_name') . '%');
        }

        $query->with([
            'walletTransactions' => function ($q) {
                $q->orderBy('transaction_date');
            },
            'images' // Add images relationship
        ]);

        // Pagination
        $perPage    = $request->input('per_page', 15); // Default to 15 items per page
        $belongings = $query->paginate($perPage);

        // Format the response
        $response = collect($belongings->items())->map(function ($belonging) {
            $data = [
                'id'               => $belonging->id,
                'prisoner_no'      => $belonging->prisoner_no,
                'prisoner_fullname' => trim("{$belonging->inmate->surname} {$belonging->inmate->first_name} {$belonging->inmate->other_names}"),
                'item_name'        => $belonging->item_name,
                'description'      => $belonging->description,
                'condition'        => $belonging->condition,
                'item_type'        => $belonging->item_type,
                'quantity'         => $belonging->quantity,
                'is_stored'        => $belonging->is_stored,
                'storage_location' => $belonging->storage_location,
                'received_at'      => $belonging->received_at?->toDateTimeString(),
                'retrieved_at'     => $belonging->retrieved_at?->toDateTimeString(),
                'disposed_at'      => $belonging->disposed_at?->toDateTimeString(),
                'status'           => $belonging->status,
                'notes'            => $belonging->notes,
                'service_no'       => $belonging->service_no,
                'created_by'       => [
                    'service_no' => $belonging->creator->service_no,
                    'name' => $belonging->creator->full_name
                ],
                'retrieved_by'     => $belonging->retrieved_by ? [
                    'service_no' => $belonging->retriever->service_no,
                    'name' => $belonging->retriever->full_name
                ] : null,
                'disposed_by'      => $belonging->disposed_by ? [
                    'service_no' => $belonging->disposer->service_no,
                    'name' => $belonging->disposer->full_name
                ] : null,
                'deleted_by'       => $belonging->deleted_by ? [
                    'service_no' => $belonging->deleter->service_no,
                    'name' => $belonging->deleter->full_name
                ] : null,
                'deleted_at'       => $belonging->deleted_at?->toDateTimeString(),
                'images'           => $belonging->images->map(function ($image) {
                    return [
                        'id'       => $image->id,
                        'path'     => $image->path,
                        'filename' => $image->filename,
                        'url'      => asset('storage/' . $image->path)
                    ];
                })
            ];

            if ($belonging->item_type === 'cash') {
                $data['cash_amount']  = $belonging->cash_amount;
                $data['transactions'] = $belonging->walletTransactions->map(function ($transaction) {
                    return [
                        'type'        => $transaction->transaction_type,
                        'amount'      => $transaction->amount,
                        'balance'     => $transaction->balance,
                        'description' => $transaction->description,
                        'date'        => $transaction->transaction_date->toDateTimeString(),
                    ];
                })->all();
            }

            return $data;
        })->all();

        return response()->json([
            'data'       => $response,
            'pagination' => [
                'total'        => $belongings->total(),
                'per_page'     => $belongings->perPage(),
                'current_page' => $belongings->currentPage(),
                'last_page'    => $belongings->lastPage(),
                'from'         => $belongings->firstItem(),
                'to'           => $belongings->lastItem(),
            ],
        ]);
    }

    /**
     * Display a listing of soft deleted belongings.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function trashed(Request $request)
    {
        // Authorization handled by middleware

        $query = Belonging::onlyTrashed();

        // Apply filters
        if ($request->has('prisoner_no')) {
            $query->where('prisoner_no', $request->input('prisoner_no'));
        }

        if ($request->has('item_type')) {
            $query->where('item_type', $request->input('item_type'));
        }

        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }

        if ($request->has('item_name')) {
            $query->where('item_name', 'like', '%' . $request->input('item_name') . '%');
        }

        if ($request->has('deleted_by')) {
            $query->where('deleted_by', $request->input('deleted_by'));
        }

        $query->with([
            'walletTransactions' => function ($q) {
                $q->orderBy('transaction_date');
            },
            'images',
            'creator',
            'retriever',
            'disposer',
            'deleter',
            'inmate'
        ]);

        // Pagination
        $perPage = $request->input('per_page', 15);
        $belongings = $query->paginate($perPage);

        // Format the response
        $response = collect($belongings->items())->map(function ($belonging) {
            $data = [
                'id' => $belonging->id,
                'prisoner_no' => $belonging->prisoner_no,
                'prisoner_fullname' => trim("{$belonging->inmate->surname} {$belonging->inmate->first_name} {$belonging->inmate->other_names}"),
                'item_name' => $belonging->item_name,
                'description' => $belonging->description,
                'condition' => $belonging->condition,
                'item_type' => $belonging->item_type,
                'quantity' => $belonging->quantity,
                'is_stored' => $belonging->is_stored,
                'storage_location' => $belonging->storage_location,
                'received_at' => $belonging->received_at?->toDateTimeString(),
                'retrieved_at' => $belonging->retrieved_at?->toDateTimeString(),
                'disposed_at' => $belonging->disposed_at?->toDateTimeString(),
                'status' => $belonging->status,
                'notes' => $belonging->notes,
                'service_no' => $belonging->service_no,
                'created_by' => [
                    'service_no' => $belonging->creator->service_no,
                    'name' => $belonging->creator->full_name
                ],
                'retrieved_by' => $belonging->retrieved_by ? [
                    'service_no' => $belonging->retriever->service_no,
                    'name' => $belonging->retriever->full_name
                ] : null,
                'disposed_by' => $belonging->disposed_by ? [
                    'service_no' => $belonging->disposer->service_no,
                    'name' => $belonging->disposer->full_name
                ] : null,
                'deleted_by' => $belonging->deleted_by ? [
                    'service_no' => $belonging->deleter->service_no,
                    'name' => $belonging->deleter->full_name
                ] : null,
                'deleted_at' => $belonging->deleted_at?->toDateTimeString(),
                'images' => $belonging->images->map(function ($image) {
                    return [
                        'id' => $image->id,
                        'path' => $image->path,
                        'filename' => $image->filename,
                        'url' => asset('storage/' . $image->path)
                    ];
                })
            ];

            if ($belonging->item_type === 'cash') {
                $data['cash_amount'] = $belonging->cash_amount;
                $data['transactions'] = $belonging->walletTransactions->map(function ($transaction) {
                    return [
                        'type' => $transaction->transaction_type,
                        'amount' => $transaction->amount,
                        'balance' => $transaction->balance,
                        'description' => $transaction->description,
                        'date' => $transaction->transaction_date->toDateTimeString(),
                    ];
                })->all();
            }

            return $data;
        })->all();

        return response()->json([
            'data' => $response,
            'pagination' => [
                'total' => $belongings->total(),
                'per_page' => $belongings->perPage(),
                'current_page' => $belongings->currentPage(),
                'last_page' => $belongings->lastPage(),
                'from' => $belongings->firstItem(),
                'to' => $belongings->lastItem(),
            ],
        ]);
    }
    public function store(Request $request)
    {
        // Authorization handled by middleware

        // Get service_no from authenticated staff
        $serviceNo = Auth::user()->service_no;

        // Check if this is a bulk upload
        $isBulkUpload = $request->has('belongings');

        if ($isBulkUpload) {
            // Validate the bulk upload structure
            $request->validate([
                'belongings' => 'required|array',
                'belongings.*.prisoner_no' => 'required|exists:inmates_all_class_register,prisoner_no',
                'belongings.*.item_name' => 'required|string|max:255',
                'belongings.*.description' => 'nullable|string',
                'belongings.*.condition' => 'nullable|string',
                'belongings.*.item_type' => 'required|in:physical,cash',
                'belongings.*.cash_amount' => 'nullable|numeric|min:0|required_if:belongings.*.item_type,cash',
                'belongings.*.quantity' => 'nullable|integer|min:1',
                'belongings.*.is_stored' => 'boolean',
                'belongings.*.storage_location' => 'nullable|string',
                'belongings.*.received_at' => 'nullable|date',
                'belongings.*.notes' => 'nullable|string',
                'belongings.*.images' => 'nullable|array',
                'belongings.*.images.*' => 'nullable|image|max:5120', // 5MB max per image
            ]);

            $createdBelongings = [];

            foreach ($request->belongings as $belongingData) {
                $belonging = $this->createBelonging($belongingData);

                // Handle images if present
                if (isset($belongingData['images']) && is_array($belongingData['images'])) {
                    $this->storeImages($belonging, $belongingData['images']);
                }

                $createdBelongings[] = $belonging;
            }

            return response()->json([
                'message' => 'Multiple belongings stored successfully',
                'data' => $createdBelongings
            ], 201);
        } else {
            // Single belonging validation
            $validated = $request->validate([
                'prisoner_no' => 'required|exists:inmates_all_class_register,prisoner_no',
                'item_name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'condition' => 'nullable|string',
                'item_type' => 'required|in:physical,cash',
                'cash_amount' => 'nullable|numeric|min:0|required_if:item_type,cash',
                'quantity' => 'nullable|integer|min:1',
                'is_stored' => 'boolean',
                'storage_location' => 'nullable|string',
                'received_at' => 'nullable|date',
                'notes' => 'nullable|string',
                'images' => 'nullable|array',
                'images.*' => 'nullable|image|max:5120', // 5MB max per image
            ]);

            // Add service_no to validated data
            $validated['service_no'] = $serviceNo;

            $belonging = $this->createBelonging($validated);

            // Handle images if present
            if ($request->hasFile('images')) {
                $this->storeImages($belonging, $request->file('images'));
            }

            return response()->json(['message' => 'Belonging stored', 'data' => $belonging], 201);
        }
    }

    /**
     * Create a belonging record from validated data
     *
     * @param array $data
     * @return Belonging
     */
       /**
     * Create a belonging record from validated data
     *
     * @param array $data
     * @return Belonging
     */
    private function createBelonging(array $data)
    {
        $defaults = [
            'received_at' => $data['received_at'] ?? now(),
            'is_stored' => $data['is_stored'] ?? true,
            'condition' => $data['condition'] ?? 'good',
            'quantity' => $data['quantity'] ?? 1,
        ];

        $initialCashAmount = 0;
        $depositAmount = 0;

        if (isset($data['item_type']) && $data['item_type'] === 'cash' && isset($data['cash_amount']) && $data['cash_amount'] > 0) {
            $depositAmount = $data['cash_amount'];
            $data['cash_amount'] = $initialCashAmount; // Start with zero
        }

        $belonging = Belonging::create(array_merge($data, $defaults));

        // If it's a cash belonging, record the initial deposit
        if ($belonging->item_type === 'cash' && $depositAmount > 0) {
            $belonging->deposit($depositAmount, 'Initial deposit on storage');

            // Refresh the model to get the updated cash_amount
            $belonging->refresh();
        }

        return $belonging;
    }

    /**
     * Store images for a belonging
     *
     * @param Belonging $belonging
     * @param array $images
     * @return void
     */
    private function storeImages(Belonging $belonging, array $images)
    {
        foreach ($images as $image) {
            $path = $image->store('belongings/' . $belonging->prisoner_no, 'public');

            $belonging->images()->create([
                'path' => $path,
                'filename' => $image->getClientOriginalName(),
                'mime_type' => $image->getMimeType(),
                'size' => $image->getSize(),
            ]);
        }
    }
    public function show($id)
    {
        // Authorization handled by middleware

        $belonging = Belonging::with([
            'walletTransactions' => function ($query) {
                $query->orderBy('transaction_date');
            },
            'images'
        ])->findOrFail($id);

        $response = [
            'id'               => $belonging->id,
            'prisoner_no'      => $belonging->prisoner_no,
            'item_name'        => $belonging->item_name,
            'description'      => $belonging->description,
            'condition'        => $belonging->condition,
            'item_type'        => $belonging->item_type,
            'quantity'         => $belonging->quantity,
            'is_stored'        => $belonging->is_stored,
            'storage_location' => $belonging->storage_location,
            'received_at'      => $belonging->received_at?->toDateTimeString(),
            'retrieved_at'     => $belonging->retrieved_at?->toDateTimeString(),
            'disposed_at'      => $belonging->disposed_at?->toDateTimeString(),
            'status'           => $belonging->status,
            'notes'            => $belonging->notes,
            'images'           => $belonging->images->map(function ($image) {
                return [
                    'id'       => $image->id,
                    'path'     => $image->path,
                    'filename' => $image->filename,
                    'url'      => asset('storage/' . $image->path)
                ];
            })
        ];

        if ($belonging->item_type === 'cash') {
            $response['cash_amount']  = $belonging->cash_amount;
            $response['transactions'] = $belonging->walletTransactions->map(function ($transaction) {
                return [
                    'type'        => $transaction->transaction_type,
                    'amount'      => $transaction->amount,
                    'balance'     => $transaction->balance,
                    'description' => $transaction->description,
                    'date'        => $transaction->transaction_date->toDateTimeString(),
                ];
            })->all();
        }

        return response()->json($response);
    }

    public function update(Request $request, $id)
    {
        // Authorization handled by middleware

        $belonging = Belonging::findOrFail($id);

        $validated = $request->validate([
            'item_name'        => 'sometimes|string|max:255',
            'description'      => 'nullable|string',
            'condition'        => 'sometimes|in:good,fair,poor',
            'item_type'        => 'sometimes|in:physical,cash',
            'cash_amount'      => 'nullable|numeric|min:0|required_if:item_type,cash',
            'quantity'         => 'sometimes|integer|min:1',
            'is_stored'        => 'sometimes|boolean',
            'storage_location' => 'nullable|string',
            'received_at'      => 'nullable|date',
            'retrieved_at'     => 'nullable|date',
            'disposed_at'      => 'nullable|date',
            'status'           => 'sometimes|in:stored,retrieved,disposed',
            'notes'            => 'nullable|string',
            'images'           => 'nullable|array',
            'images.*'         => 'nullable|image|max:5120', // 5MB max per image
            'remove_images'    => 'nullable|array',
            'remove_images.*'  => 'nullable|exists:belonging_images,id',
        ]);

        $validated['service_no'] = Auth::user()->service_no;

        if ($request->has('item_type') && $validated['item_type'] === 'cash' && $belonging->item_type !== 'cash' && isset($validated['cash_amount'])) {
            $belonging->update($validated);
            $belonging->deposit($validated['cash_amount'], 'Initial deposit after update to cash');
        } else {
            $belonging->update($validated);
        }

        // Handle image removals if specified
        if ($request->has('remove_images') && is_array($request->remove_images)) {
            foreach ($request->remove_images as $imageId) {
                $image = $belonging->images()->find($imageId);
                if ($image) {
                    // Delete the file from storage
                    if (Storage::disk('public')->exists($image->path)) {
                        Storage::disk('public')->delete($image->path);
                    }
                    // Delete the record
                    $image->delete();
                }
            }
        }

        // Handle new image uploads
        if ($request->hasFile('images')) {
            $this->storeImages($belonging, $request->file('images'));
        }

        // Load images relationship for the response
        $belonging->load('images', 'walletTransactions');

        return response()->json(['message' => 'Belonging updated', 'data' => $belonging]);
    }

    public function retrieve($id)
    {
        // Authorization handled by middleware

        $belonging = Belonging::findOrFail($id);
        $serviceNo = Auth::user()->service_no;

        if (!$belonging->is_stored) {
            return response()->json(['message' => 'Belonging already retrieved'], 400);
        }

        try {
            DB::beginTransaction();

            // Withdraw all cash if it's a cash belonging
            if ($belonging->item_type === 'cash') {
                $belonging->withdrawAll("Retrieved by $serviceNo");
            }

            $belonging->update([
                'is_stored' => false,
                'retrieved_at' => now(),
                'retrieved_by' => $serviceNo,
                'status' => 'retrieved'
            ]);

            DB::commit();

            return response()->json(['message' => 'Belonging retrieved', 'data' => $belonging]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Error retrieving belonging', 'error' => $e->getMessage()], 500);
        }
    }

    public function dispose($id)
    {
        // Authorization handled by middleware

        $belonging = Belonging::findOrFail($id);
        $serviceNo = Auth::user()->service_no;

        if ($belonging->item_type === 'cash') {
            return response()->json(['message' => 'Cash belongings cannot be disposed'], 400);
        }

        if ($belonging->status === 'disposed') {
            return response()->json(['message' => 'Belonging already disposed'], 400);
        }

        try {
            DB::beginTransaction();

            $belonging->update([
                'is_stored' => false,
                'disposed_at' => now(),
                'disposed_by' => $serviceNo,
                'status' => 'disposed'
            ]);

            DB::commit();

            return response()->json(['message' => 'Belonging disposed', 'data' => $belonging]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Error disposing belonging', 'error' => $e->getMessage()], 500);
        }
    }

    public function deposit(Request $request, $id)
    {
        $belonging = Belonging::findOrFail($id);
        Gate::authorize('manageCashTransactions', $belonging);

        $validated = $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'description' => 'nullable|string',
            'service_no' => 'required|exists:staff,service_no'
        ]);

        try {
            DB::beginTransaction();

            $belonging->deposit(
                $validated['amount'],
                $validated['description'],
                $validated['service_no']  // Pass service_no to the deposit method
            );

            DB::commit();
            return response()->json([
                'message' => 'Cash deposited',
                'balance' => $belonging->cash_amount,
                'processed_by' => [
                    'service_no' => $validated['service_no']
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error processing deposit',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function withdraw(Request $request, $id)
    {
        $belonging = Belonging::findOrFail($id);
        Gate::authorize('manageCashTransactions', $belonging);

        $belonging = Belonging::findOrFail($id);

        $validated = $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'description' => 'nullable|string',
            'service_no' => 'required|exists:staff,service_no'
        ]);

        try {
            DB::beginTransaction();

            $belonging->withdraw(
                $validated['amount'],
                $validated['description'],
                $validated['service_no']  // Pass service_no to the withdraw method
            );

            DB::commit();
            return response()->json([
                'message' => 'Cash withdrawn',
                'balance' => $belonging->cash_amount,
                'processed_by' => [
                    'service_no' => $validated['service_no']
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error processing withdrawal',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function statement(Request $request, $id = null)
    {
        // Authorization handled by middleware

        // Validate request parameters
        $validated = $request->validate([
            'prisoner_no' => 'required_without:id|exists:inmates_all_class_register,prisoner_no',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        // Determine if we're looking up by ID or prisoner_no
        if ($id) {
            $belonging = Belonging::findOrFail($id);
            if ($belonging->item_type !== 'cash') {
                return response()->json(['message' => 'Not a cash belonging'], 400);
            }
            $belongings = collect([$belonging]);
        } else {
            $prisoner_no = $validated['prisoner_no'];
            $belongings = Belonging::where('prisoner_no', $prisoner_no)
                ->where('item_type', 'cash')
                ->get();

            if ($belongings->isEmpty()) {
                return response()->json(['message' => 'No cash belongings found for this inmate'], 404);
            }
        }

        $start_date = new DateTime($validated['start_date']);
        $end_date = new DateTime($validated['end_date']);

        // Add one day to end_date to include transactions on the end date
        $end_date->modify('+1 day');

        $result = [];

        foreach ($belongings as $belonging) {
            // Get all transactions for the period
            $allTransactions = $belonging->walletTransactions()
                ->whereBetween('transaction_date', [$start_date, $end_date])
                ->orderBy('transaction_date')
                ->get();

            // Get opening balance (balance before start date)
            $openingBalanceTransaction = $belonging->walletTransactions()
                ->where('transaction_date', '<', $start_date)
                ->orderBy('transaction_date', 'desc')
                ->first();

            $openingBalance = $openingBalanceTransaction ? $openingBalanceTransaction->balance : 0;

            // Calculate totals
            $deposits = $allTransactions->where('transaction_type', 'deposit');
            $withdrawals = $allTransactions->where('transaction_type', 'withdrawal');

            $totalDeposits = $deposits->sum('amount');
            $totalWithdrawals = $withdrawals->sum('amount');

            $statement = [
                'belonging_id' => $belonging->id,
                'prisoner_no' => $belonging->prisoner_no,
                'item_name' => $belonging->item_name,
                'period' => [
                    'start_date' => $start_date->format('Y-m-d'),
                    'end_date' => (clone $end_date)->modify('-1 day')->format('Y-m-d'),
                ],
                'summary' => [
                    'opening_balance' => $openingBalance,
                    'current_balance' => $belonging->cash_amount,
                    'total_deposits' => $totalDeposits,
                    'total_withdrawals' => $totalWithdrawals,
                    'net_change' => $totalDeposits - $totalWithdrawals,
                ],
                'deposits' => $deposits->map(function ($entry) {
                    return [
                        'amount' => $entry->amount,
                        'balance' => $entry->balance,
                        'description' => $entry->description,
                        'date' => $entry->transaction_date->toDateTimeString(),
                    ];
                })->values()->all(),
                'withdrawals' => $withdrawals->map(function ($entry) {
                    return [
                        'amount' => $entry->amount,
                        'balance' => $entry->balance,
                        'description' => $entry->description,
                        'date' => $entry->transaction_date->toDateTimeString(),
                    ];
                })->values()->all(),
                'all_transactions' => $allTransactions->map(function ($entry) {
                    return [
                        'type' => $entry->transaction_type,
                        'amount' => $entry->amount,
                        'balance' => $entry->balance,
                        'description' => $entry->description,
                        'date' => $entry->transaction_date->toDateTimeString(),
                    ];
                })->values()->all(),
            ];

            $result[] = $statement;
        }

        // If we looked up by ID, return just that statement, otherwise return all
        return response()->json($id ? $result[0] : [
            'prisoner_no' => $validated['prisoner_no'],
            'period' => [
                'start_date' => $start_date->format('Y-m-d'),
                'end_date' => (clone $end_date)->modify('-1 day')->format('Y-m-d'),
            ],
            'statements' => $result
        ]);
    }

    public function getInmateBelongings($prisoner_no)
    {
        Gate::authorize('viewAny', Belonging::class);

        if (! DB::table('inmates_all_class_register')->where('prisoner_no', $prisoner_no)->exists()) {
            return response()->json(['message' => 'Inmate not found'], 404);
        }

        $belongings = Belonging::where('prisoner_no', $prisoner_no)
            ->with([
                'walletTransactions' => function ($query) {
                    $query->orderBy('transaction_date');
                },
                'images'
            ])
            ->get();

        $response = $belongings->map(function ($belonging) {
            $data = [
                'id'               => $belonging->id,
                'item_name'        => $belonging->item_name,
                'description'      => $belonging->description,
                'condition'        => $belonging->condition,
                'item_type'        => $belonging->item_type,
                'quantity'         => $belonging->quantity,
                'is_stored'        => $belonging->is_stored,
                'storage_location' => $belonging->storage_location,
                'received_at'      => $belonging->received_at?->toDateTimeString(),
                'retrieved_at'     => $belonging->retrieved_at?->toDateTimeString(),
                'disposed_at'      => $belonging->disposed_at?->toDateTimeString(),
                'status'           => $belonging->status,
                'notes'            => $belonging->notes,
                'images'           => $belonging->images->map(function ($image) {
                    return [
                        'id'       => $image->id,
                        'path'     => $image->path,
                        'filename' => $image->filename,
                        'url'      => asset('storage/' . $image->path)
                    ];
                })
            ];

            if ($belonging->item_type === 'cash') {
                $data['cash_amount']  = $belonging->cash_amount;
                $data['transactions'] = $belonging->walletTransactions->map(function ($transaction) {
                    return [
                        'type'        => $transaction->transaction_type,
                        'amount'      => $transaction->amount,
                        'balance'     => $transaction->balance,
                        'description' => $transaction->description,
                        'date'        => $transaction->transaction_date->toDateTimeString(),
                    ];
                });
            }

            return $data;
        });

        return response()->json([
            'prisoner_no' => $prisoner_no,
            'belongings'  => $response,
        ]);
    }

    /**
     * Get dashboard statistics for belongings
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDashboardStats()
    {
        // Authorization handled by middleware

        $stats = [
            'total_belongings' => [
                'all' => Belonging::count(),
                'in_storage' => Belonging::where('status', 'in-storage')->count(),
                'retrieved' => Belonging::where('status', 'retrieved')->count(),
                'disposed' => Belonging::where('status', 'disposed')->count()
            ],
            'cash_belongings' => [
                'total_accounts' => Belonging::where('item_type', 'cash')->count(),
                'total_balance' => Belonging::where('item_type', 'cash')
                    ->sum('cash_amount'),
                'recent_transactions' => Belonging::where('item_type', 'cash')
                    ->with(['walletTransactions' => function($query) {
                        $query->orderBy('transaction_date', 'desc')
                            ->limit(10);
                    }])
                    ->limit(5)
                    ->get()
                    ->map(function($belonging) {
                        return [
                            'prisoner_no' => $belonging->prisoner_no,
                            'prisoner_fullname' => trim("{$belonging->inmate->surname} {$belonging->inmate->first_name} {$belonging->inmate->other_names}"),
                            'balance' => $belonging->cash_amount,
                            'recent_activities' => $belonging->walletTransactions->map(function($transaction) {
                                return [
                                    'type' => $transaction->transaction_type,
                                    'amount' => $transaction->amount,
                                    'date' => $transaction->transaction_date->format('Y-m-d H:i:s')
                                ];
                            })
                        ];
                    })
            ],
            'physical_belongings' => [
                'total' => Belonging::where('item_type', 'physical')->count(),
                'by_condition' => Belonging::where('item_type', 'physical')
                    ->select('condition', DB::raw('count(*) as total'))
                    ->groupBy('condition')
                    ->get(),
                'recent_additions' => Belonging::where('item_type', 'physical')
                    ->orderBy('received_at', 'desc')
                    ->limit(10)
                    ->get()
                    ->map(function ($belonging) {
                        return [
                            'prisoner_no' => $belonging->prisoner_no,
                            'item_name' => $belonging->item_name,
                            'condition' => $belonging->condition,
                            'received_at' => $belonging->received_at,
                            'prisoner_fullname' => trim("{$belonging->inmate->surname} {$belonging->inmate->first_name} {$belonging->inmate->other_names}")
                        ];
                    })
            ],
            'storage_metrics' => [
                'by_location' => Belonging::select('storage_location', DB::raw('count(*) as total'))
                    ->whereNotNull('storage_location')
                    ->groupBy('storage_location')
                    ->get(),
                'items_pending_retrieval' => Belonging::where('status', 'in-storage')
                    ->count()
            ],
            'activity_summary' => [
                'today' => [
                    'received' => Belonging::whereDate('received_at', today())->count(),
                    'retrieved' => Belonging::whereDate('retrieved_at', today())->count(),
                    'disposed' => Belonging::whereDate('disposed_at', today())->count()
                ],
                'this_month' => [
                    'received' => Belonging::whereMonth('received_at', now()->month)->count(),
                    'retrieved' => Belonging::whereMonth('retrieved_at', now()->month)->count(),
                    'disposed' => Belonging::whereMonth('disposed_at', now()->month)->count()
                ],
                'recent_activities' => Belonging::with('inmate')
                    ->whereNotNull('received_at')
                    ->orWhereNotNull('retrieved_at')
                    ->orWhereNotNull('disposed_at')
                    ->orderBy('updated_at', 'desc')
                    ->limit(10)
                    ->get()
                    ->map(function($belonging) {
                        return [
                            'prisoner_no' => $belonging->prisoner_no,
                            'prisoner_fullname' => trim("{$belonging->inmate->surname} {$belonging->inmate->first_name} {$belonging->inmate->other_names}"),
                            'item_name' => $belonging->item_name,
                            'action' => $this->determineLastAction($belonging),
                            'date' => $this->getLastActionDate($belonging)->format('Y-m-d H:i:s')
                        ];
                    })
            ]
        ];

        return response()->json(['data' => $stats]);
    }

    /**
     * Determine the last action performed on a belonging
     * @param Belonging $belonging
     * @return string
     */
    private function determineLastAction($belonging)
    {
        if ($belonging->disposed_at && $belonging->disposed_at->gt($belonging->retrieved_at ?? new DateTime('1900-01-01'))) {
            return 'disposed';
        }
        if ($belonging->retrieved_at && $belonging->retrieved_at->gt($belonging->received_at)) {
            return 'retrieved';
        }
        return 'received';
    }

    /**
     * Get the date of the last action performed on a belonging
     * @param Belonging $belonging
     * @return \Carbon\Carbon
     */
    private function getLastActionDate($belonging)
    {
        return collect([
            $belonging->disposed_at,
            $belonging->retrieved_at,
            $belonging->received_at
        ])->filter()->last();
    }

     /**
     * Get current cash balance for an inmate
     *
     * @param string $prisoner_no
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBalance($prisoner_no)
    {
        // Authorization handled by middleware

        $inmate = DB::table('inmates_all_class_register')
            ->where('prisoner_no', $prisoner_no)
            ->first();

        if (! $inmate) {
            return response()->json(['message' => 'Inmate not found'], 404);
        }

        $cashBelongings = Belonging::where('prisoner_no', $prisoner_no)
            ->where('item_type', 'cash')
            ->get();

        if ($cashBelongings->isEmpty()) {
            return response()->json([
                'prisoner_no' => $prisoner_no,
                'inmate_details' => $inmate,
                'total_balance' => 0,
                'accounts' => []
            ]);
        }

        $totalBalance = $cashBelongings->sum('cash_amount');

        $accounts = $cashBelongings->map(function ($belonging) {
            return [
                'id' => $belonging->id,
                'item_name' => $belonging->item_name,
                'balance' => $belonging->cash_amount,
                'last_transaction' => $belonging->walletTransactions()
                    ->orderBy('transaction_date', 'desc')
                    ->first()?->transaction_date?->toDateTimeString()
            ];
        });

        return response()->json([
            'prisoner_no' => $prisoner_no,
            'inmate_details' => $inmate,
            'total_balance' => $totalBalance,
            'accounts' => $accounts
        ]);
    }
        /**
         * Soft delete a belonging
         *
         * @param int $id
         * @return \Illuminate\Http\JsonResponse
         */
        public function destroy($id)
    {
        // Authorization handled by middleware

        $belonging = Belonging::findOrFail($id);
        $serviceNo = Auth::user()->service_no;

            try {
                DB::beginTransaction();

                // Update deleted_by before soft delete
                $belonging->update(['deleted_by' => $serviceNo]);

                // Soft delete related models
                $belonging->images()->delete();
                if ($belonging->item_type === 'cash') {
                    $belonging->walletTransactions()->delete();
                }

                $belonging->delete();

                DB::commit();

                return response()->json([
                    'message' => 'Belonging deleted successfully'
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                return response()->json([
                    'message' => 'Error deleting belonging',
                    'error' => $e->getMessage()
                ], 500);
            }
        }

        /**
         * Force delete a belonging and its images
         *
         * @param int $id
         * @return \Illuminate\Http\JsonResponse
         */
        public function forceDelete($id)
    {
        // Authorization handled by middleware

        $belonging = Belonging::withTrashed()->findOrFail($id);

            try {
                DB::beginTransaction();

                // Delete physical files only during force delete
                foreach ($belonging->images as $image) {
                    if (Storage::disk('public')->exists($image->path)) {
                        Storage::disk('public')->delete($image->path);
                    }
                }

                // Delete the belonging's directory if it exists
                $directory = 'belongings/' . $belonging->prisoner_no;
                if (Storage::disk('public')->exists($directory)) {
                    Storage::disk('public')->deleteDirectory($directory);
                }

                // Force delete related models
                $belonging->images()->forceDelete();
                if ($belonging->item_type === 'cash') {
                    $belonging->walletTransactions()->forceDelete();
                }

                $belonging->forceDelete();

                DB::commit();

                return response()->json([
                    'message' => 'Belonging permanently deleted'
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                return response()->json([
                    'message' => 'Error force deleting belonging',
                    'error' => $e->getMessage()
                ], 500);
            }
        }

        /**
         * Restore a soft deleted belonging
         *
         * @param int $id
         * @return \Illuminate\Http\JsonResponse
         */
        public function restore($id)
    {
        // Authorization handled by middleware

        $belonging = Belonging::withTrashed()->findOrFail($id);

            try {
                DB::beginTransaction();

                // Restore the belonging
                $belonging->restore();

                // Restore related models if needed
                $belonging->images()->restore();
                if ($belonging->item_type === 'cash') {
                    $belonging->walletTransactions()->restore();
                }

                DB::commit();

                return response()->json([
                    'message' => 'Belonging restored successfully',
                    'data' => $belonging->fresh(['images', 'walletTransactions'])
                ]);

            } catch (\Exception $e) {
                DB::rollBack();

                return response()->json([
                    'message' => 'Error restoring belonging',
                    'error' => $e->getMessage()
                ], 500);
            }
        }
}
