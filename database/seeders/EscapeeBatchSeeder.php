<?php

namespace Database\Seeders;

use App\Models\EscapeeBatch;
use App\Models\States;
use App\Models\Prison;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EscapeeBatchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure we have states and prisons
        if (States::count() === 0 || Prison::count() === 0) {
            $this->command->warn('No states or prisons found. Please run StateSeeder and PrisonSeeder first.');
            return;
        }

        $this->command->info('Creating escapee batches...');

        // Create sample batches with different statuses
        $batches = [
            [
                'title' => 'Lagos State Prison Break - January 2024',
                'description' => 'Multiple escapees from Lagos State Prison during power outage',
                'batch_status' => 'investigated',
                'state_id' => States::where('state', 'Lagos')->first()?->id ?? 1,
            ],
            [
                'title' => 'Kano Central Prison Escape - February 2024',
                'description' => 'Three inmates escaped during transport to court',
                'batch_status' => 'closed',
                'state_id' => States::where('state', 'Kano')->first()?->id ?? 2,
            ],
            [
                'title' => 'Abuja Correctional Center - March 2024',
                'description' => 'Single escapee during medical visit',
                'batch_status' => 'under_review',
                'state_id' => States::where('state', 'FCT')->first()?->id ?? 37,
            ],
            [
                'title' => 'Port Harcourt Prison Break - April 2024',
                'description' => 'Mass escape during visiting hours',
                'batch_status' => 'created',
                'state_id' => States::where('state', 'Rivers')->first()?->id ?? 3,
            ],
            [
                'title' => 'Kaduna State Prison Incident - May 2024',
                'description' => 'Two escapees overpowered guards during work detail',
                'batch_status' => 'investigated',
                'state_id' => States::where('state', 'Kaduna')->first()?->id ?? 4,
            ],
        ];

        foreach ($batches as $batchData) {
            // Get a random prison from the state
            $prison = Prison::where('state_id', $batchData['state_id'])->inRandomOrder()->first();
            
            if ($prison) {
                $batchData['prison_id'] = $prison->id;
                $batchData['uploaded'] = fake()->boolean(30);
                $batchData['status'] = true;
                $batchData['created_at'] = fake()->dateTimeBetween('-6 months', 'now');
                $batchData['updated_at'] = now();
                
                EscapeeBatch::create($batchData);
            }
        }

        // Create additional random batches using factory
        $states = States::pluck('id')->toArray();
        $prisons = Prison::pluck('id')->toArray();

        if (!empty($states) && !empty($prisons)) {
            // Create batches for different statuses
            EscapeeBatch::factory(5)->created()->create();
            EscapeeBatch::factory(3)->underReview()->create();
            EscapeeBatch::factory(4)->investigated()->create();
            EscapeeBatch::factory(2)->closed()->create();
            
            // Create some recent batches
            EscapeeBatch::factory(3)->recent()->create();
            
            // Create some uploaded batches
            EscapeeBatch::factory(2)->uploaded()->create();
        }

        $totalBatches = EscapeeBatch::count();
        $this->command->info("Created {$totalBatches} escapee batches successfully.");
    }
}
