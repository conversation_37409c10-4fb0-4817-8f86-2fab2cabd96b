<?php

namespace App\Policies;

use App\Models\Staff;
use App\Policies\BasePolicy;
use Modules\AdminManagement\Entities\HqAdmin;
use Modules\HealthManagement\Entities\MedicalAppointments;

class MedicalAppointmentPolicy extends BasePolicy
{
    /**
     * Get the base permission name for medical appointments
     */
    protected function getBasePermission(): string
    {
        return 'medical-appointments';
    }

    /**
     * Override view permissions to include manage permissions
     */
    protected function getViewPermissions(): array
    {
        return [
            'view-medical-appointments',
            'manage-medical-appointments',
            'manage-all-health',
            'manage-all'
        ];
    }

    /**
     * Custom authorization for scheduling appointments
     */
    public function schedule($user): bool
    {
        return $this->hasAnyPermission($user, [
            'create-medical-appointments',
            'manage-medical-appointments',
            'manage-all-health',
            'manage-all'
        ]);
    }

    /**
     * Custom authorization for canceling appointments
     */
    public function cancel($user, MedicalAppointments $appointment): bool
    {
        return $this->canManageResource($user, $appointment, 'update-medical-appointments');
    }

    /**
     * Custom authorization for completing appointments
     */
    public function complete($user, MedicalAppointments $appointment): bool
    {
        return $this->canManageResource($user, $appointment, 'update-medical-appointments');
    }
}
