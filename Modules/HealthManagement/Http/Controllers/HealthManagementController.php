<?php

namespace Modules\HealthManagement\Http\Controllers;

use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Gate;
use Illuminate\Contracts\Support\Renderable;

class HealthManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('authorize:view-health-dashboard')->only(['index']);
        $this->middleware('authorize:create-medical-history')->only(['create', 'store']);
        $this->middleware('authorize:view-medical-history')->only(['show']);
        $this->middleware('authorize:update-medical-history')->only(['edit', 'update']);
        $this->middleware('authorize:delete-medical-history')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index()
    {
        // Authorization handled by middleware

        return Inertia::render('../../Modules/HealthManagement/Resources/assets/js/views/index');
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        // Authorization handled by middleware

        return view('healthmanagement::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        // Authorization handled by middleware

        //
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function show($id)
    {
        // Authorization handled by middleware

        return view('healthmanagement::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        // Authorization handled by middleware

        return view('healthmanagement::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        // Authorization handled by middleware

        //
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        if (!Gate::allows('delete-medical-history')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        //
    }
}
