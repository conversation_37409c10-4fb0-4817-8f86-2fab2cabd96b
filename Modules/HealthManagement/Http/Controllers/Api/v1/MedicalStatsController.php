<?php
namespace Modules\HealthManagement\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Modules\HealthManagement\Entities\MedicalAppointments;
use Modules\HealthManagement\Entities\MedicalDiagnostics;
use Modules\HealthManagement\Entities\MedicalHistory;
use Modules\HealthManagement\Entities\MedicalLabTest;
use Modules\HealthManagement\Entities\MedicalPrescriptions;
use Illuminate\Support\Carbon;

class MedicalStatsController extends Controller
{
  public function __construct()
  {
    $this->middleware('authorize:view-health-dashboard')->only(['index']);
    $this->middleware('authorize:view-health-reports')->only(['getHealthDashboardStats']);
  }

  /**
   * Display a listing of the resource.
   */
  public function index()
  {
    // Authorization handled by middleware

    return view('healthmanagement::index');
  }


  public function getHealthDashboardStats(Request $request)
  {
    // Authorization handled by middleware

    $validated = $request->validate([
      'year' => 'nullable|integer|min:1900|max:' . date('Y'),
      'period_type' => 'nullable|in:quarterly,monthly,weekly,daily',
    ]);

    $year = $validated['year'] ?? now()->year;
    $periodType = $validated['period_type'] ?? null;

    // Helper function to apply year filter to queries
    $applyYearFilter = function ($query, $dateColumn) use ($year) {
      return $query->whereYear($dateColumn, $year);
    };

    $stats = [
      'appointments' => [
        'today' => $applyYearFilter(MedicalAppointments::whereDate('appointment_date', today()), 'appointment_date')->count(),
        'total_appointments' => $applyYearFilter(MedicalAppointments::query(), 'appointment_date')->count(),
        'pending_appointments' => $applyYearFilter(MedicalAppointments::where('appointment_status', 'pending'), 'appointment_date')->count(),
        'completed_appointments' => $applyYearFilter(MedicalAppointments::where('appointment_status', 'completed'), 'appointment_date')->count(),
        'cancelled_appointments' => $applyYearFilter(MedicalAppointments::where('appointment_status', 'canceled'), 'appointment_date')->count(),
        'appointments_this_month' => $applyYearFilter(MedicalAppointments::whereMonth('appointment_date', now()->month), 'appointment_date')->count(),
        'upcoming_appointments' => $applyYearFilter(MedicalAppointments::where('appointment_date', '>', now())
          ->where('appointment_status', 'pending'), 'appointment_date')->count(),
        'recent_appointments' => $applyYearFilter(MedicalAppointments::orderBy('appointment_date', 'desc'), 'appointment_date')
          ->take(10)
          ->get(),
      ],
      'diagnostics' => [
        'total_diagnostics' => $applyYearFilter(MedicalDiagnostics::query(), 'diagnostic_date')->count(),
        'diagnostics_by_type' => $applyYearFilter(MedicalDiagnostics::select('diagnostic_type', DB::raw('count(*) as total'))->groupBy('diagnostic_type'), 'diagnostic_date')->get(),
        'pending_diagnostics' => $applyYearFilter(MedicalDiagnostics::where('diagnostic_status', 'pending'), 'diagnostic_date')->count(),
        'completed_diagnostics' => $applyYearFilter(MedicalDiagnostics::where('diagnostic_status', 'completed'), 'diagnostic_date')->count(),
        'total_recent_diagnostics' => $applyYearFilter(MedicalDiagnostics::whereDate('diagnostic_date', '>=', now()->subDays(30)), 'diagnostic_date')->count(),
        'recent_diagnostics' => $applyYearFilter(MedicalDiagnostics::orderBy('diagnostic_date', 'desc'), 'diagnostic_date')
          ->take(10)
          ->get(),
      ],
      'lab_tests' => [
        'completed_this_week' => $applyYearFilter(MedicalLabTest::where('test_status', 'completed')
          ->whereDate('test_date', '>=', now()->subDays(7)), 'test_date')->count(),
        'total_tests' => $applyYearFilter(MedicalLabTest::query(), 'test_date')->count(),
        'tests_by_type' => $applyYearFilter(MedicalLabTest::select('test_type', DB::raw('count(*) as total'))->groupBy('test_type'), 'test_date')->get(),
        'pending_tests' => $applyYearFilter(MedicalLabTest::where('test_status', 'pending'), 'test_date')->count(),
        'completed_tests' => $applyYearFilter(MedicalLabTest::where('test_status', 'completed'), 'test_date')->count(),
        'tests_this_month' => $applyYearFilter(MedicalLabTest::whereMonth('test_date', now()->month), 'test_date')->count(),
      ],
      'active_conditions' => MedicalHistory::where('medical_status', 'Ongoing')->count(),
      'active_prescriptions' => MedicalPrescriptions::where('status', 1)->count(),
      'total_prescriptions' => MedicalPrescriptions::count(),
      'prescriptions_this_month' => $applyYearFilter(MedicalPrescriptions::whereMonth('prescription_date', now()->month), 'prescription_date')->count(),
      'prescriptions_by_medication' => $applyYearFilter(MedicalPrescriptions::select('medication', DB::raw('count(*) as total'))
        ->groupBy('medication')
        ->orderBy('total', 'desc')
        ->limit(10), 'prescription_date')->get(),
      'health_trends' => [
        'monthly_appointments' => $applyYearFilter(MedicalAppointments::select(
          DB::raw('MONTH(appointment_date) as month'),
          DB::raw('count(*) as total')
        )
          ->groupBy('month'), 'appointment_date')->get(),
      ],
    ];

    // If period_type is specified, add periodic data
    if ($periodType) {
      $stats['periodic_data'] = [
        'appointments' => $this->getPeriodicStats(MedicalAppointments::class, 'appointment_date', $periodType, $year),
        'diagnostics' => $this->getPeriodicStats(MedicalDiagnostics::class, 'diagnostic_date', $periodType, $year),
        'lab_tests' => $this->getPeriodicStats(MedicalLabTest::class, 'test_date', $periodType, $year),
      ];
    }

    return response()->json([
      'success' => true,
      'data' => $stats,
      'filters_applied' => $validated,
    ]);
  }

  private function getPeriodicStats($model, $dateColumn, $periodType, $year)
  {
    $query = $model::query()->whereYear($dateColumn, $year);

    switch ($periodType) {
      case 'quarterly':
        return $query->select(
          DB::raw('QUARTER(' . $dateColumn . ') as period'),
          DB::raw('COUNT(*) as count')
        )
          ->groupBy(DB::raw('QUARTER(' . $dateColumn . ')'))
          ->orderBy('period')
          ->get()
          ->mapWithKeys(function ($item) {
            return ['Quarter ' . $item->period => $item->count];
          });

      case 'monthly':
        return $query->select(
          DB::raw('MONTH(' . $dateColumn . ') as period'),
          DB::raw('COUNT(*) as count')
        )
          ->groupBy(DB::raw('MONTH(' . $dateColumn . ')'))
          ->orderBy('period')
          ->get()
          ->mapWithKeys(function ($item) use ($year) {
            $monthName = Carbon::create($year, $item->period, 1)->format('F');
            return [$monthName => $item->count];
          });

      case 'weekly':
        return $query->select(
          DB::raw('WEEK(' . $dateColumn . ', 1) as period'),
          DB::raw('COUNT(*) as count')
        )
          ->groupBy(DB::raw('WEEK(' . $dateColumn . ', 1)'))
          ->orderBy('period')
          ->get()
          ->mapWithKeys(function ($item) {
            return ['Week ' . $item->period => $item->count];
          });

      case 'daily':
        return $query->select(
          DB::raw('DAYOFWEEK(' . $dateColumn . ') as period'),
          DB::raw('DAYNAME(' . $dateColumn . ') as day_name'),
          DB::raw('COUNT(*) as count')
        )
          ->groupBy(
            DB::raw('DAYNAME(' . $dateColumn . ')'),
            DB::raw('DAYOFWEEK(' . $dateColumn . ')')
          )
          ->orderBy('period')
          ->get()
          ->mapWithKeys(function ($item) {
            return [$item->day_name => $item->count];
          });
    }

    return [];
  }
}
