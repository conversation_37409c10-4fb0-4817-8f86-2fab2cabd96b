<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Modules\AdminManagement\Entities\HqAdmin;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing HealthManagement Authorization System\n";
echo "============================================\n\n";

// Test with HqAdmin
$hqAdmin = HqAdmin::first();
if ($hqAdmin) {
    auth()->login($hqAdmin);
    
    echo "Logged in as HqAdmin: {$hqAdmin->email}\n";
    echo "Is Super Admin: " . ($hqAdmin->isSuperAdmin() ? 'YES ✅' : 'NO ❌') . "\n\n";
    
    // Test all HealthManagement gates
    $healthGates = [
        // General Health Management
        'view-health-dashboard' => 'Health Dashboard',
        'view-health-reports' => 'Health Reports',
        
        // Medical History
        'view-medical-history' => 'View Medical History',
        'create-medical-history' => 'Create Medical History',
        'update-medical-history' => 'Update Medical History',
        'delete-medical-history' => 'Delete Medical History',
        
        // Medical Diagnostics
        'view-medical-diagnostics' => 'View Medical Diagnostics',
        'create-medical-diagnostics' => 'Create Medical Diagnostics',
        'update-medical-diagnostics' => 'Update Medical Diagnostics',
        'delete-medical-diagnostics' => 'Delete Medical Diagnostics',
        
        // Medical Appointments
        'view-medical-appointments' => 'View Medical Appointments',
        'create-medical-appointments' => 'Create Medical Appointments',
        'update-medical-appointments' => 'Update Medical Appointments',
        'delete-medical-appointments' => 'Delete Medical Appointments',
        
        // Medical Lab Tests
        'view-medical-lab-tests' => 'View Medical Lab Tests',
        'create-medical-lab-tests' => 'Create Medical Lab Tests',
        'update-medical-lab-tests' => 'Update Medical Lab Tests',
        'delete-medical-lab-tests' => 'Delete Medical Lab Tests',
        
        // Medical Prescriptions
        'view-medical-prescriptions' => 'View Medical Prescriptions',
        'create-medical-prescriptions' => 'Create Medical Prescriptions',
        'update-medical-prescriptions' => 'Update Medical Prescriptions',
        'delete-medical-prescriptions' => 'Delete Medical Prescriptions',
        
        // General Health Management
        'manage-health' => 'Manage Health',
    ];
    
    echo "Testing HealthManagement Gate Authorization:\n";
    echo "===========================================\n";
    
    $passedCount = 0;
    $totalCount = count($healthGates);
    
    foreach ($healthGates as $gate => $description) {
        $allowed = Gate::allows($gate);
        $status = $allowed ? '✅ PASS' : '❌ FAIL';
        echo sprintf("%-35s: %s\n", $description, $status);
        if ($allowed) $passedCount++;
    }
    
    echo "\n";
    echo "Summary:\n";
    echo "========\n";
    echo "Total Gates Tested: {$totalCount}\n";
    echo "Passed: {$passedCount}\n";
    echo "Failed: " . ($totalCount - $passedCount) . "\n";
    echo "Success Rate: " . round(($passedCount / $totalCount) * 100, 2) . "%\n";
    
    if ($passedCount === $totalCount) {
        echo "\n🎉 ALL HEALTH MANAGEMENT TESTS PASSED! Unified authorization system is working correctly!\n";
    } else {
        echo "\n⚠️ Some tests failed. Please check the gate definitions.\n";
    }
    
    auth()->logout();
} else {
    echo "No HqAdmin found\n";
}

echo "\nHealthManagement Authorization Test Complete! 🎉\n";
