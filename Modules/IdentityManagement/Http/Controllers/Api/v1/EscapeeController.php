<?php
namespace Modules\IdentityManagement\Http\Controllers\Api\v1;

use App\Services\EscapeeService;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class EscapeeController extends Controller
{
    protected EscapeeService $service;

    public function __construct(EscapeeService $service)
    {
        $this->service = $service;
    }

    public function index(Request $request)
    {
        return $this->service->listBatches($request->all());
    }

    public function show($id)
    {
        return $this->service->getBatch($id);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title'     => 'required|string',
            'message'   => 'nullable|string',
            'state_id'  => 'required|exists:states,id',
            'prison_id' => 'required|exists:prisons,id',
        ]);

        return $this->service->createBatch($validated);
    }

    public function update(Request $request, $id)
    {
        return $this->service->updateBatch($id, $request->all());
    }

    public function destroy($id)
    {
        return $this->service->deleteBatch($id);
    }
}
