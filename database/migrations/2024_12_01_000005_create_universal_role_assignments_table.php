<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('universal_role_assignments', function (Blueprint $table) {
            $table->id();
            $table->morphs('user'); // user_type and user_id columns for polymorphic relationship
            $table->unsignedBigInteger('role_id');
            $table->unsignedBigInteger('state_id')->nullable();
            $table->unsignedBigInteger('prison_id')->nullable();
            $table->timestamp('assigned_at')->useCurrent();
            $table->timestamp('expires_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
            $table->foreign('state_id')->references('id')->on('states')->onDelete('cascade');
            $table->foreign('prison_id')->references('id')->on('prisons')->onDelete('cascade');
            
            $table->unique(['user_type', 'user_id', 'role_id', 'state_id', 'prison_id'], 'unique_user_role_assignment');
            $table->index(['user_type', 'user_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('universal_role_assignments');
    }
};
