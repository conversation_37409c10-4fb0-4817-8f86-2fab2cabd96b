<?php

namespace Modules\LegalAidManagement\Http\Controllers\Api\v1;

use App\Traits\Common;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Contracts\Support\Renderable;
use Modules\LegalAidManagement\Entities\LegalAidFirm;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class LegalAidFirmController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware('authorize:view-legal-firms')->only(['index', 'show']);
        $this->middleware('authorize:create-legal-firms')->only(['store']);
        $this->middleware('authorize:update-legal-firms')->only(['update']);
        $this->middleware('authorize:delete-legal-firms')->only(['destroy']);
    }
    use Common; //Trait
    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(Request $request)
    {
        // Authorization handled by middleware
        $noIncludeArray = ['count', 'page', 'include', 'paginate', 'search', 'legalreps'];
        $relations      = ['legalreps'];
        $includeString  = explode(',', $request->include);
        $query          = $request->query();
        $paginate       = $request->paginate ?: 20;

        // This method will do a custom query based on query string to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

        $legalAidFirm = LegalAidFirm::orderBy('created_at', 'desc');

        // Add unified search functionality
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $legalAidFirm->where(function ($q) use ($searchTerm) {
                $q->where('name', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('phone', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('email', 'LIKE', '%' . $searchTerm . '%');
            });
        }

        $legalAidFirm = $legalAidFirm->where($filteredQuery)->paginate($paginate);

        if ($request->include) {
            foreach ($includeString as $string) {
                $checkRelationshipArray = $this->shouldIncludeRelation($relations, $string);
                if ($checkRelationshipArray) {
                    $legalAidFirm->load($string);
                }

            }
        }

        $response = ['data' => $legalAidFirm];

        if ($request->count) {
            $response['count'] = $legalAidFirm->total();
        }

        return response()->json($response);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        return view('legalaidmanagement::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        if (!Gate::allows('create-legal-firms')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }
        $request->validate([
            'name'  => 'required',
            'phone' => 'required',
            'email' => 'required|email',
        ]);

        ////////Check if there is a Firm with the same phone number
        $check_phone_number = LegalAidFirm::where("phone", $request->phone)->first();
        if ($check_phone_number) {
            return response()->json(['message' => 'This Phone number already exists for a Firm'], 422);
        }

        ////////Check if there is a Firm with the same name
        $check_name = LegalAidFirm::where("name", $request->name)->first();
        if ($check_name) {
            return response()->json(['message' => 'This Name already exists for a Firm'], 422);
        }

        ////////Check if there is a Firm with the same email
        $check_email = LegalAidFirm::where("email", $request->email)->first();
        if ($check_email) {
            return response()->json(['message' => 'This Email already exists for a Firm'], 422);
        }

        $firm = new LegalAidFirm($request->all());
        $firm->save();
        return response()->json(['message' => 'New Firm saved']);
    }

    public function search(Request $request)
    {
        //return $request->all();

        $legal_aid_firm = new LegalAidFirm();
        $paginate       = $request->paginate ? $request->paginate : 20;

        //return response()->json(['data'=>$request->prisoner_no, 422]);
        //// Search by name keyword, search full name
        $legal_aid_firm = $legal_aid_firm->when($request->legal_firm_name_filter, function ($query) use ($request) {
            return $query->where(function ($query) use ($request) {
                $query->where('name', 'LIKE', '%' . $request->legal_firm_name_filter . '%');
            });
        });

        //// Search by phone number keyword, search phone
        $legal_aid_firm = $legal_aid_firm->when($request->legal_firm_phone_filter, function ($query) use ($request) {
            return $query->where(function ($query) use ($request) {
                $query->where('phone', 'LIKE', '%' . $request->legal_firm_phone_filter . '%');
            });
        });

        /////// No include query string for custom query
        $noIncludeArray = ['legal_firm_phone_filter', 'legal_firm_name_filter', 'page', 'include', 'paginate'];
        $query          = $request->query();
        //// This method will do a custom query based on query string to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

        $legal_aid_firm = $legal_aid_firm->where($filteredQuery)->orderBy('created_at', 'desc')->paginate($paginate);
        $relations      = [];
        $includeString  = explode(',', $request->include);

        if ($request->include) {
            foreach ($includeString as $string) {
                $checkRelationshipArray = $this->shouldIncludeRelation($relations, $string);
                if ($checkRelationshipArray) {
                    $legal_aid_firm->load($string);
                }

            }
        }
        if ($request->count) {
            $inmate = $legal_aid_firm->total();
        }

        return response()->json(['data' => $legal_aid_firm]);
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function show($id)
    {
        // Authorization handled by middleware
        return view('legalaidmanagement::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        return view('legalaidmanagement::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, LegalAidFirm $legalaidfirm)
     {
         if (!Gate::allows('update-legal-firms')) {
             return response()->json(['error' => 'Unauthorized'], 403);
         }
        // Check if at least one field is present
        if (! $request->hasAny(['name', 'phone', 'email', 'status', 'firm_type', 'address'])) {
            return response()->json(['errors' => 'At least one field must be provided for update'], 422);
        }

        // Validate the request data
        $request->validate([
            'name'      => 'sometimes|string|max:255',
            'phone'     => 'sometimes|string|max:20',
            'email'     => 'sometimes|email|max:255',
            'status'    => 'sometimes|in:0,1',
            'firm_type' => 'sometimes|in:1,2,3',
            'address'   => 'sometimes|string|max:255',
        ]);

        // Check for existing records with the same name, email, or phone
        $existingFirm = LegalAidFirm::where(function ($query) use ($request, $legalaidfirm) {
            if ($request->filled('name')) {
                $query->orWhere('name', $request->name);
            }
            if ($request->filled('email')) {
                $query->orWhere('email', $request->email);
            }
            if ($request->filled('phone')) {
                $query->orWhere('phone', $request->phone);
            }
        })->where('id', '!=', $legalaidfirm->id)->first();

        if ($existingFirm) {
            return response()->json(['message' => 'A record with the same name, email, or phone already exists'], 422);
        }

        // Update all provided fields
        $legalaidfirm->fill($request->only([
            'name',
            'phone',
            'email',
            'status',
            'firm_type',
            'address',
        ]));

        $legalaidfirm->save();

        return response()->json(['message' => 'Firm record updated'], 200);
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy(LegalAidFirm $legalaidfirm)
     {
         if (!Gate::allows('delete-legal-firms')) {
             return response()->json(['error' => 'Unauthorized'], 403);
         }
        //$legalaidfirm->delete();
        return response()->json(['deleted' => $legalaidfirm->delete()], 201);
    }
}
