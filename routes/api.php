<?php

use App\Http\Controllers\Api\GenericApiController;
use App\Http\Controllers\ZoneController;
use App\Http\Controllers\Api\StaffAuthController;
use App\Http\Controllers\Api\UploadToCloudController;
use App\Http\Controllers\Api\SyncToCloudController;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Route;
use Modules\AdminManagement\Http\Controllers\Api\V1\HqAccountTypeController;
use Modules\IdentityManagement\Http\Controllers\Api\v1\ScannerConnectionController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::prefix('v1')->group(function () {

    // Route::middleware('auth:sanctum')->group(function () {
    //     Route::get('/staff', function (Request $request) {
    //         return $request->user();
    //     })->name('staff');
    // });

    //////// Login
    Route::post('login', [StaffAuthController::class, 'login'])->name('api-login');

    //////// RBAC Routes
    require __DIR__ . '/rbac.php';
    Route::post('state_zone_login', [StaffAuthController::class, 'state_zone_login'])->name('api-state_zone-login');
    Route::post('hq_login', [StaffAuthController::class, 'hqLogin'])->name('api-hq-login');
    Route::get('hq-account-types', [HqAccountTypeController::class, 'index'])->name('hq-account-controller.index');

    /*
    The states route belongs to the General Api Controller
    */
    Route::get('states', [GenericApiController::class, 'getStates'])->name('states');

    /*
    Zone routes
    */
    Route::apiResource('zones', ZoneController::class);
    Route::get('zones/active/list', [ZoneController::class, 'getActiveZones'])->name('zones.active');

    ////AUTH MIDDLEWARE
    // Route::middleware('auth:sanctum')->group(function () {

    Route::get('frequently_called_table', [GenericApiController::class, 'getAllFrequentlyCalledTables'])->name('reused_tables');

    ///////// API FOR IDENTITY MANAGEMENT DASHBOARD CHARTS
    Route::get('get_all_methods_for_chart', [GenericApiController::class, 'getAllMethodsForChartDistribution'])->name('charts_data');

    ///////// API FOR CASES MANAGEMENT DASHBOARD CHARTS
    Route::get('get_all_methods_for_chart_for_case', [GenericApiController::class, 'getAllMethodsForChartForCases'])->name('cases_charts_data');

    ///////// API FOR LEGAL AID MANAGEMENT DASHBOARD CHARTS
    Route::get('get_all_methods_for_chart_for_welfare', [GenericApiController::class, 'getAllMethodsForChartForWelfare'])->name('welfare_charts_data');

    ///////// API STATE/ZONAL ADMIN MANAGEMENT DASHBOARD CHARTS
    Route::get('get_all_methods_for_chart_for_admin', [GenericApiController::class, 'getAllMethodsForChartForAdmin'])->name('zonal_charts_data');

    Route::get('get_inmates_data_for_upload', [GenericApiController::class, 'getInmateDataForUpload'])->name('get_inmates_data_for_upload');
    Route::get('process_uploaded_inmate_data', [GenericApiController::class, 'processUploadedInmateData'])->name('process_uploaded_inmate_data');
    Route::middleware('auth:sanctum')->group(function () {
        Route::apiResource('scanner-connections', ScannerConnectionController::class);
    });

    Route::prefix('cloud-upload')->middleware('auth:sanctum')->group(function () {
        Route::post('/upload', [UploadToCloudController::class, 'uploadVettedData']);
        Route::get('/statistics', [UploadToCloudController::class, 'getStatistics']);
        Route::post('/retry', [UploadToCloudController::class, 'retryFailedUploads']);
        Route::get('/verify-structure', [UploadToCloudController::class, 'verifyTableStructure']);
    });

    Route::prefix('cloud-sync')->middleware('auth:sanctum')->group(function () {
        Route::post('/sync', [SyncToCloudController::class, 'syncData']);
        Route::get('/statistics', [SyncToCloudController::class, 'getStatistics']);
        Route::post('/retry', [SyncToCloudController::class, 'retryFailedSync']);
        Route::get('/verify-structure', [SyncToCloudController::class, 'verifyTableStructure']);
    });

    // Escapee Management Routes
    Route::prefix('escapee-batches')->middleware('auth:sanctum')->group(function () {
        Route::get('/', [App\Http\Controllers\Api\EscapeeBatchController::class, 'index']);
        Route::post('/', [App\Http\Controllers\Api\EscapeeBatchController::class, 'store']);
        Route::get('/{id}', [App\Http\Controllers\Api\EscapeeBatchController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\Api\EscapeeBatchController::class, 'update']);
        Route::delete('/{id}', [App\Http\Controllers\Api\EscapeeBatchController::class, 'destroy']);
        Route::post('/upload-csv', [App\Http\Controllers\Api\EscapeeBatchController::class, 'uploadCsv']);
        Route::get('/download/template', [App\Http\Controllers\Api\EscapeeBatchController::class, 'downloadTemplate']);
        Route::get('/statistics/overview', [App\Http\Controllers\Api\EscapeeBatchController::class, 'statistics']);

        // Nested escapees routes
        Route::get('/{batchId}/escapees', [App\Http\Controllers\Api\EscapeeController::class, 'batchEscapees']);
    });

    Route::prefix('escapees')->middleware('auth:sanctum')->group(function () {
        Route::get('/', [App\Http\Controllers\Api\EscapeeController::class, 'index']);
        Route::post('/', [App\Http\Controllers\Api\EscapeeController::class, 'store']);
        Route::get('/{id}', [App\Http\Controllers\Api\EscapeeController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\Api\EscapeeController::class, 'update']);
        Route::delete('/{id}', [App\Http\Controllers\Api\EscapeeController::class, 'destroy']);
        Route::post('/bulk-update', [App\Http\Controllers\Api\EscapeeController::class, 'bulkUpdateStatus']);
    });

    Route::prefix('migrate')->middleware(['auth:sanctum'])->get('/run-command', function () {
        // Artisan::call('some:command');
        // return Artisan::output();

        // Run first command
        Artisan::call('command:first');

// Run second command
        Artisan::call('command:second');

// Get output if needed
        $output1 = Artisan::output();

        Artisan::call('command:third');
        $output2 = Artisan::output();

        return "Commands executed.\n" . $output1 . "\n" . $output2;

    });
});

//NOTE:for the biometric device, where the biometric device gets the latest info database
Route::get('fetch_scanner_details', [ScannerConnectionController::class, 'last']);
