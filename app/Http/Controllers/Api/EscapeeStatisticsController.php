<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\EscapeeService;
use App\Models\Escapee;
use App\Models\EscapeeBatch;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EscapeeStatisticsController extends Controller
{
    protected EscapeeService $escapeeService;

    public function __construct(EscapeeService $escapeeService)
    {
        $this->middleware('auth:sanctum');
        $this->escapeeService = $escapeeService;
    }

    /**
     * Get comprehensive escapee statistics
     */
    public function overview(Request $request): JsonResponse
    {
        if (!Gate::allows('view-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $filters = $request->only(['state_id', 'prison_id', 'date_from', 'date_to']);
        $statistics = $this->escapeeService->generateStatistics($filters);

        return response()->json([
            'success' => true,
            'data' => $statistics,
        ]);
    }

    /**
     * Get escape rates by time period
     */
    public function escapeRates(Request $request): JsonResponse
    {
        if (!Gate::allows('view-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'period' => 'nullable|in:daily,weekly,monthly,quarterly,yearly',
            'state_id' => 'nullable|exists:states,id',
            'prison_id' => 'nullable|exists:prisons,id',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        $period = $validated['period'] ?? 'monthly';
        $dateFrom = $validated['date_from'] ?? Carbon::now()->subYear()->format('Y-m-d');
        $dateTo = $validated['date_to'] ?? Carbon::now()->format('Y-m-d');

        $query = Escapee::whereBetween('escape_date', [$dateFrom, $dateTo]);

        if (isset($validated['state_id'])) {
            $query->whereHas('batch', function($q) use ($validated) {
                $q->where('state_id', $validated['state_id']);
            });
        }

        if (isset($validated['prison_id'])) {
            $query->whereHas('batch', function($q) use ($validated) {
                $q->where('prison_id', $validated['prison_id']);
            });
        }

        $dateFormat = match($period) {
            'daily' => '%Y-%m-%d',
            'weekly' => '%Y-%u',
            'monthly' => '%Y-%m',
            'quarterly' => '%Y-Q%q',
            'yearly' => '%Y',
            default => '%Y-%m',
        };

        $escapeRates = $query->selectRaw("DATE_FORMAT(escape_date, '{$dateFormat}') as period, count(*) as count")
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'period_type' => $period,
                'date_range' => ['from' => $dateFrom, 'to' => $dateTo],
                'escape_rates' => $escapeRates,
            ],
        ]);
    }

    /**
     * Get recapture success metrics
     */
    public function recaptureMetrics(Request $request): JsonResponse
    {
        if (!Gate::allows('view-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'state_id' => 'nullable|exists:states,id',
            'prison_id' => 'nullable|exists:prisons,id',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        $query = Escapee::query();

        if (isset($validated['date_from'])) {
            $query->where('escape_date', '>=', $validated['date_from']);
        }

        if (isset($validated['date_to'])) {
            $query->where('escape_date', '<=', $validated['date_to']);
        }

        if (isset($validated['state_id'])) {
            $query->whereHas('batch', function($q) use ($validated) {
                $q->where('state_id', $validated['state_id']);
            });
        }

        if (isset($validated['prison_id'])) {
            $query->whereHas('batch', function($q) use ($validated) {
                $q->where('prison_id', $validated['prison_id']);
            });
        }

        $totalEscapees = $query->count();
        $recaptured = $query->where('current_status', 'recaptured')->count();
        $stillAtLarge = $query->whereIn('current_status', ['escaped', 'unknown'])->count();
        $deceased = $query->where('current_status', 'deceased')->count();

        // Average time to recapture
        $avgRecaptureTime = $query->where('current_status', 'recaptured')
            ->whereNotNull('escape_date')
            ->whereNotNull('recapture_date')
            ->selectRaw('AVG(DATEDIFF(recapture_date, escape_date)) as avg_days')
            ->first()
            ->avg_days ?? 0;

        // Recapture rate by time periods
        $recaptureByPeriod = $query->where('current_status', 'recaptured')
            ->whereNotNull('recapture_date')
            ->selectRaw('
                SUM(CASE WHEN DATEDIFF(recapture_date, escape_date) <= 7 THEN 1 ELSE 0 END) as within_week,
                SUM(CASE WHEN DATEDIFF(recapture_date, escape_date) <= 30 THEN 1 ELSE 0 END) as within_month,
                SUM(CASE WHEN DATEDIFF(recapture_date, escape_date) <= 90 THEN 1 ELSE 0 END) as within_quarter,
                SUM(CASE WHEN DATEDIFF(recapture_date, escape_date) <= 365 THEN 1 ELSE 0 END) as within_year
            ')
            ->first();

        return response()->json([
            'success' => true,
            'data' => [
                'total_escapees' => $totalEscapees,
                'recaptured' => $recaptured,
                'still_at_large' => $stillAtLarge,
                'deceased' => $deceased,
                'recapture_rate' => $totalEscapees > 0 ? round(($recaptured / $totalEscapees) * 100, 2) : 0,
                'avg_recapture_time_days' => round($avgRecaptureTime, 1),
                'recapture_by_period' => [
                    'within_week' => $recaptureByPeriod->within_week ?? 0,
                    'within_month' => $recaptureByPeriod->within_month ?? 0,
                    'within_quarter' => $recaptureByPeriod->within_quarter ?? 0,
                    'within_year' => $recaptureByPeriod->within_year ?? 0,
                ],
                'filters_applied' => $validated,
            ],
        ]);
    }

    /**
     * Get trending analysis
     */
    public function trends(Request $request): JsonResponse
    {
        if (!Gate::allows('view-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'state_id' => 'nullable|exists:states,id',
            'prison_id' => 'nullable|exists:prisons,id',
            'months' => 'nullable|integer|min:3|max:24',
        ]);

        $months = $validated['months'] ?? 12;
        $startDate = Carbon::now()->subMonths($months)->startOfMonth();

        $query = Escapee::where('escape_date', '>=', $startDate);

        if (isset($validated['state_id'])) {
            $query->whereHas('batch', function($q) use ($validated) {
                $q->where('state_id', $validated['state_id']);
            });
        }

        if (isset($validated['prison_id'])) {
            $query->whereHas('batch', function($q) use ($validated) {
                $q->where('prison_id', $validated['prison_id']);
            });
        }

        // Monthly escape trends
        $monthlyTrends = $query->selectRaw('DATE_FORMAT(escape_date, "%Y-%m") as month, count(*) as escapes')
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Status distribution trends
        $statusTrends = $query->selectRaw('
                DATE_FORMAT(escape_date, "%Y-%m") as month,
                current_status,
                count(*) as count
            ')
            ->groupBy('month', 'current_status')
            ->orderBy('month')
            ->get()
            ->groupBy('month');

        // Location trends (top escape locations)
        $locationTrends = $query->selectRaw('escape_location, count(*) as count')
            ->whereNotNull('escape_location')
            ->groupBy('escape_location')
            ->orderByDesc('count')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'period_months' => $months,
                'start_date' => $startDate->format('Y-m-d'),
                'monthly_trends' => $monthlyTrends,
                'status_trends' => $statusTrends,
                'location_trends' => $locationTrends,
                'filters_applied' => $validated,
            ],
        ]);
    }

    /**
     * Get prison-specific statistics
     */
    public function prisonStats(Request $request): JsonResponse
    {
        if (!Gate::allows('view-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'state_id' => 'nullable|exists:states,id',
            'limit' => 'nullable|integer|min:5|max:50',
        ]);

        $limit = $validated['limit'] ?? 20;

        $query = DB::table('escapees')
            ->join('escapee_batches', 'escapees.batch_id', '=', 'escapee_batches.id')
            ->join('prisons', 'escapee_batches.prison_id', '=', 'prisons.id')
            ->join('states', 'prisons.state_id', '=', 'states.id');

        if (isset($validated['state_id'])) {
            $query->where('states.id', $validated['state_id']);
        }

        $prisonStats = $query->selectRaw('
                prisons.id as prison_id,
                prisons.prison_name,
                states.state as state_name,
                count(escapees.id) as total_escapees,
                sum(case when escapees.current_status = "escaped" then 1 else 0 end) as still_escaped,
                sum(case when escapees.current_status = "recaptured" then 1 else 0 end) as recaptured,
                sum(case when escapees.current_status = "deceased" then 1 else 0 end) as deceased,
                sum(case when escapees.current_status = "unknown" then 1 else 0 end) as unknown_status,
                round(avg(case when escapees.current_status = "recaptured" and escapees.recapture_date is not null 
                    then datediff(escapees.recapture_date, escapees.escape_date) else null end), 1) as avg_recapture_days
            ')
            ->groupBy('prisons.id', 'prisons.prison_name', 'states.state')
            ->orderByDesc('total_escapees')
            ->limit($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'prison_statistics' => $prisonStats,
                'filters_applied' => $validated,
            ],
        ]);
    }
}
