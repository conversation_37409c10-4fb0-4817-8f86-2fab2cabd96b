<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;

class EscapeeBatchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Gate::allows('manage-escapees');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'batch_status' => 'nullable|in:created,under_review,investigated,closed',
            'state_id' => 'nullable|exists:states,id',
            'prison_id' => 'nullable|exists:prisons,id',
        ];

        // Additional rules for update requests
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            $rules['batch_status'] = 'sometimes|in:created,under_review,investigated,closed';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Batch title is required.',
            'title.max' => 'Batch title cannot exceed 255 characters.',
            'description.max' => 'Batch description cannot exceed 1000 characters.',
            'batch_status.in' => 'Invalid batch status. Must be one of: created, under_review, investigated, closed.',
            'state_id.exists' => 'The selected state is invalid.',
            'prison_id.exists' => 'The selected prison is invalid.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'title' => 'batch title',
            'description' => 'batch description',
            'batch_status' => 'batch status',
            'state_id' => 'state',
            'prison_id' => 'prison',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Custom validation logic
            if ($this->filled('prison_id') && $this->filled('state_id')) {
                // Verify that the prison belongs to the selected state
                $prison = \App\Models\Prison::find($this->prison_id);
                if ($prison && $prison->state_id != $this->state_id) {
                    $validator->errors()->add('prison_id', 'The selected prison does not belong to the selected state.');
                }
            }
        });
    }
}
