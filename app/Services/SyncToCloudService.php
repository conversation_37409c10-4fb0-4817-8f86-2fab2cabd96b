<?php
namespace App\Services;

use Carbon\Carbon;
use Exception;
// use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use Modules\CaseManagement\Entities\EscortDuties;
use Modules\CaseManagement\Entities\Sittings;
use Modules\IdentityManagement\Entities\ATP;
use Modules\IdentityManagement\Entities\Convict;
use Modules\IdentityManagement\Entities\Inmate;

class SyncToCloudService
{
    protected $cloudEndpoint;
    protected $apiKey;
    protected $batchSize;
    protected $maxRetries;

    public function __construct()
    {
        $this->cloudEndpoint = config('services.cloud.endpoint');
        $this->apiKey        = config('services.cloud.api_key');
        $this->batchSize     = config('services.cloud.batch_size', 200);
        $this->maxRetries    = config('services.cloud.max_retries', 3);
    }

    /**
     * Upload vetted inmate data to cloud
     */
    public function uploadVettedData(array $options = [])
    {
        $tables = $options['tables'] ?? [
            'inmates_all_class_register',
            'inmates_atp_register',
            'inmates_convict_register',
            'inmates_sittings',
            'escort_duties',
            'legal_aid_firms',
            'inmate_legal_representative',
            'legal_representatives',
            'legal_rep_defaulters',
        ];
        $results = [];

        try {
            DB::beginTransaction();

            foreach ($tables as $table) {
                $results[$table] = $this->uploadTableData($table);
            }
            DB::commit();

            Log::info('Sync upload completed successfully', $results);

            return [
                'success' => true,
                'message' => 'Data sync to cloud successfully',
                'results' => $results,
            ];

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Sync upload failed: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Sync upload failed: ' . $e->getMessage(),
                'results' => $results,
            ];
        }
    }

    /**
     * Upload data for a specific table
     */
    protected function uploadTableData(string $table)
    {
        $query        = $this->buildQuery($table);
        $totalRecords = $query->count();

        if ($totalRecords === 0) {
            return [
                'table'            => $table,
                'total_records'    => 0,
                'uploaded_records' => 0,
                'failed_records'   => 0,
                'status'           => 'no_data',
            ];
        }

        $uploadedCount = 0;
        $failedCount   = 0;
        $offset        = 0;

        while ($offset < $totalRecords) {
            $batch = $query->offset($offset)->limit($this->batchSize)->get();

            if ($batch->isEmpty()) {
                break;
            }

            $batchData = $batch instanceof \Illuminate\Support\Collection
            ? $batch->toArray()
            : $batch;

            $batchResult = $this->uploadBatch($table, $batchData);

            if ($batchResult['success']) {
                $uploadedCount += $batchResult['synced_count'];
                $ids = $batch instanceof \Illuminate\Support\Collection
                ? $batch->pluck('id')->toArray()
                : collect($batch)->pluck('id')->toArray();

                $this->markAsSynced($table, $ids);
            } else {
                $failedCount += count($batchData);
                Log::warning("Failed to sync batch for table {$table}", $batchResult);
            }

            $offset += $this->batchSize;
        }

        return [
            'table'            => $table,
            'total_records'    => $totalRecords,
            'uploaded_records' => $uploadedCount,
            'failed_records'   => $failedCount,
            'status'           => $failedCount === 0 ? 'completed' : 'partial',
        ];
    }

    /**
     * Build query based on table - uses models where available, direct DB queries otherwise
     */
    protected function buildQuery(string $table)
    {
        if ($this->hasModel($table)) {
            $model = $this->getModelForTable($table);
            return $model::where('vetted', true)
                ->where('upload_to_cloud', 0);
        }

        return DB::table($table)
            ->where('vetted', 1)
            ->where('upload_to_cloud', 0);
    }

    /**
     * Check if table has a corresponding Eloquent model
     */
    protected function hasModel(string $table): bool
    {
        $models = [
            'inmates_all_class_register' => Inmate::class,
            'inmates_atp_register'       => ATP::class,
            'inmates_convict_register'   => Convict::class,
            'inmates_sittings'           => Sittings::class,
            'escort_duties'              => EscortDuties::class,
        ];

        return isset($models[$table]);
    }

    /**
     * Upload a batch of records to cloud
     */
    protected function uploadBatch(string $table, array $data)
    {
        $retries = 0;

        $user = Auth::user();

        while ($retries < $this->maxRetries) {
            try {
                $response = Http::timeout(60)
                    ->withHeaders([
                        'Authorization' => 'Bearer ' . $this->apiKey,
                        'Content-Type'  => 'application/json',
                        'X-Table-Name'  => $table,
                        'X-Batch-Size'  => count($data),
                        'X-Logger'      => $user?->service_no,
                    ])
                    ->post($this->cloudEndpoint . '/api/bulk-sync', [
                        'table'     => $table,
                        'data'      => $data,
                        'timestamp' => Carbon::now()->toISOString(),
                        'batch_id'  => uniqid('batch_', true),
                    ]);

                if ($response->successful()) {
                    return [
                        'success'        => true,
                        'synced_count' => count($data),
                        'response'       => $response->json(),
                    ];
                }

                throw new Exception("HTTP Error: " . $response->status() . " - " . $response->body());

            } catch (Exception $e) {
                $retries++;

                if ($retries >= $this->maxRetries) {
                    return [
                        'success'        => false,
                        'error'          => $e->getMessage(),
                        'synced_count' => 0,
                    ];
                }

                sleep(pow(2, $retries));
            }
        }
    }

    /**
     * Mark records as uploaded to cloud - handles both models and direct DB queries
     */
    protected function markAsSynced(string $table, array $ids)
    {
        
        if ($this->hasModel($table)) {
            $model = $this->getModelForTable($table);
            $model::whereIn('id', $ids)->update([
                'upload_to_cloud' => 1,
                'uploaded_date'   => Carbon::now(),
            ]);
        } else {
            DB::table($table)
                ->whereIn('id', $ids)
                ->update([
                    'upload_to_cloud' => 1,
                    'uploaded_date'   => Carbon::now(),
                ]);
        }
    }

    /**
     * Get model class for table name
     */
    protected function getModelForTable(string $table)
    {
        $models = [
            'inmates_all_class_register' => Inmate::class,
            'inmates_atp_register'       => ATP::class,
            'inmates_convict_register'   => Convict::class,
            'inmates_sittings'           => Sittings::class,
            'escort_duties'              => EscortDuties::class,
        ];

        if (! isset($models[$table])) {
            throw new Exception("Model not found for table: {$table}");
        }

        return $models[$table];
    }

    /**
     * Get basic upload statistics for a specific table
     */
    public function getTableStatistics(string $table)
    {
        if ($this->hasModel($table)) {
            $model = $this->getModelForTable($table);
            $query = $model::query();

            return [
                'table'            => $table,
                'total_records'    => $query->count(),
                'vetted_records'   => $query->where('vetted', 1)->count(),
                'uploaded_records' => $query->where('upload_to_cloud', 1)->count(),
                'pending_upload'   => $query->where('vetted', 1)->where('upload_to_cloud', 0)->count(),
            ];
        } else {
            return [
                'table'            => $table,
                'total_records'    => DB::table($table)->count(),
                'vetted_records'   => DB::table($table)->where('vetted', 1)->count(),
                'uploaded_records' => DB::table($table)->where('upload_to_cloud', 1)->count(),
                'pending_upload'   => DB::table($table)->where('vetted', 1)->where('upload_to_cloud', 0)->count(),
            ];
        }
    }

    /**
     * Retry failed uploads
     */
    public function retryFailedUploads(array $options = [])
    {
        $tables = $options['tables'] ?? [
            'inmates_all_class_register',
            'inmates_atp_register',
            'inmates_convict_register',
            'inmates_sittings',
            'escort_duties',
            'legal_aid_firms',
            'inmate_legal_representative',
            'legal_representatives',
            'legal_rep_defaulters',
        ];

        foreach ($tables as $table) {
            if ($this->hasModel($table)) {
                $model = $this->getModelForTable($table);
                $model::where('vetted', 1)
                    ->where('upload_to_cloud', 0)
                    ->whereNotNull('uploaded_date')
                    ->update(['uploaded_date' => null]);
            } else {
                DB::table($table)
                    ->where('vetted', 1)
                    ->where('upload_to_cloud', 0)
                    ->whereNotNull('uploaded_date')
                    ->update(['uploaded_date' => null]);
            }
        }

       

        return $this->uploadVettedData($options);
    }

    /**
     * Verify table structure before upload
     */
    public function verifyTableStructure(string $table)
    {
        try {
            $columns         = DB::getSchemaBuilder()->getColumnListing($table);
            $requiredColumns = ['id', 'vetted', 'upload_to_cloud'];

            $missingColumns = array_diff($requiredColumns, $columns);

            return [
                'table'            => $table,
                'exists'           => ! empty($columns),
                'columns'          => $columns,
                'required_columns' => $requiredColumns,
                'missing_columns'  => $missingColumns,
                'is_valid'         => empty($missingColumns),
            ];
        } catch (Exception $e) {
            return [
                'table'  => $table,
                'exists' => false,
                'error'  => $e->getMessage(),
            ];
        }
    }
}
