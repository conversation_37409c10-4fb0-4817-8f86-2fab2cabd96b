<?php

namespace App\Services;

use Modules\IdentityManagement\Entities\Inmate;
use App\Models\Escapee;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class InmateEscapeeUpdateService
{
    /**
     * Update inmate record to mark as escapee and/or recaptured
     */
    public function updateInmateEscapeeStatus(string $prisonerNo, bool $isEscapee, bool $isRecaptured): bool
    {
        try {
            $inmate = Inmate::where('prisoner_no', $prisonerNo)->first();
            
            if (!$inmate) {
                Log::warning("Inmate not found for prisoner number: {$prisonerNo}");
                return false;
            }
            
            $inmate->escapee = $isEscapee ? 1 : 0;
            $inmate->recaptured = $isRecaptured ? 1 : 0;
            $inmate->save();
            
            Log::info("Updated inmate escapee status for prisoner {$prisonerNo}: escapee={$isEscapee}, recaptured={$isRecaptured}");
            
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to update inmate escapee status: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Bulk update inmate records based on current escapee data
     */
    public function syncAllInmateEscapeeStatuses(): array
    {
        $updated = 0;
        $errors = 0;
        $processed = 0;

        try {
            DB::beginTransaction();

            // First, reset all escapee and recaptured flags
            Inmate::query()->update(['escapee' => 0, 'recaptured' => 0]);

            // Get all unique prisoner IDs from escapees table
            $escapeeData = Escapee::select('prisoner_id', 'current_status')
                ->get()
                ->groupBy('prisoner_id');

            foreach ($escapeeData as $prisonerId => $escapees) {
                $processed++;
                
                // Determine the current status for this prisoner
                $isEscapee = true; // If they appear in escapees table, they are/were an escapee
                $isRecaptured = false;
                
                // Check if any of their records show recaptured status
                foreach ($escapees as $escapee) {
                    if ($escapee->current_status === 'recaptured') {
                        $isRecaptured = true;
                        break;
                    }
                }
                
                // Update the inmate record
                if ($this->updateInmateEscapeeStatus($prisonerId, $isEscapee, $isRecaptured)) {
                    $updated++;
                } else {
                    $errors++;
                }
            }

            DB::commit();

            Log::info("Bulk sync completed: {$updated} updated, {$errors} errors, {$processed} processed");

            return [
                'success' => true,
                'updated' => $updated,
                'errors' => $errors,
                'processed' => $processed,
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Bulk sync failed: {$e->getMessage()}");
            
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'updated' => $updated,
                'errors' => $errors,
                'processed' => $processed,
            ];
        }
    }

    /**
     * Get inmates with mismatched escapee status
     */
    public function getStatusMismatches(): array
    {
        // Get inmates marked as escapees but not in escapees table
        $falsePositives = Inmate::where('escapee', 1)
            ->whereNotIn('prisoner_no', function($query) {
                $query->select('prisoner_id')->from('escapees');
            })
            ->select('prisoner_no', 'surname', 'first_name', 'escapee', 'recaptured')
            ->get();

        // Get inmates in escapees table but not marked as escapees
        $falseNegatives = Inmate::where('escapee', 0)
            ->whereIn('prisoner_no', function($query) {
                $query->select('prisoner_id')->from('escapees');
            })
            ->select('prisoner_no', 'surname', 'first_name', 'escapee', 'recaptured')
            ->get();

        // Get inmates marked as recaptured but current status is not recaptured
        $recapturedMismatches = DB::table('inmates_all_class_register as i')
            ->join('escapees as e', 'i.prisoner_no', '=', 'e.prisoner_id')
            ->where('i.recaptured', 1)
            ->where('e.current_status', '!=', 'recaptured')
            ->select('i.prisoner_no', 'i.surname', 'i.first_name', 'i.escapee', 'i.recaptured', 'e.current_status')
            ->get();

        return [
            'false_positives' => $falsePositives,
            'false_negatives' => $falseNegatives,
            'recaptured_mismatches' => $recapturedMismatches,
            'total_mismatches' => $falsePositives->count() + $falseNegatives->count() + $recapturedMismatches->count(),
        ];
    }

    /**
     * Fix status mismatches
     */
    public function fixStatusMismatches(): array
    {
        $fixed = 0;
        $errors = 0;

        try {
            DB::beginTransaction();

            $mismatches = $this->getStatusMismatches();

            // Fix false positives (inmates marked as escapees but not in escapees table)
            foreach ($mismatches['false_positives'] as $inmate) {
                try {
                    $inmateRecord = Inmate::where('prisoner_no', $inmate->prisoner_no)->first();
                    if ($inmateRecord) {
                        $inmateRecord->escapee = 0;
                        $inmateRecord->recaptured = 0;
                        $inmateRecord->save();
                        $fixed++;
                    }
                } catch (\Exception $e) {
                    $errors++;
                    Log::error("Failed to fix false positive for {$inmate->prisoner_no}: {$e->getMessage()}");
                }
            }

            // Fix false negatives (inmates in escapees table but not marked as escapees)
            foreach ($mismatches['false_negatives'] as $inmate) {
                try {
                    // Get the current status from escapees table
                    $escapee = Escapee::where('prisoner_id', $inmate->prisoner_no)->first();
                    if ($escapee) {
                        $isRecaptured = $escapee->current_status === 'recaptured';
                        
                        $inmateRecord = Inmate::where('prisoner_no', $inmate->prisoner_no)->first();
                        if ($inmateRecord) {
                            $inmateRecord->escapee = 1;
                            $inmateRecord->recaptured = $isRecaptured ? 1 : 0;
                            $inmateRecord->save();
                            $fixed++;
                        }
                    }
                } catch (\Exception $e) {
                    $errors++;
                    Log::error("Failed to fix false negative for {$inmate->prisoner_no}: {$e->getMessage()}");
                }
            }

            // Fix recaptured mismatches
            foreach ($mismatches['recaptured_mismatches'] as $mismatch) {
                try {
                    $inmateRecord = Inmate::where('prisoner_no', $mismatch->prisoner_no)->first();
                    if ($inmateRecord) {
                        $inmateRecord->recaptured = $mismatch->current_status === 'recaptured' ? 1 : 0;
                        $inmateRecord->save();
                        $fixed++;
                    }
                } catch (\Exception $e) {
                    $errors++;
                    Log::error("Failed to fix recaptured mismatch for {$mismatch->prisoner_no}: {$e->getMessage()}");
                }
            }

            DB::commit();

            Log::info("Status mismatches fixed: {$fixed} fixed, {$errors} errors");

            return [
                'success' => true,
                'fixed' => $fixed,
                'errors' => $errors,
                'mismatches_found' => $mismatches['total_mismatches'],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to fix status mismatches: {$e->getMessage()}");
            
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'fixed' => $fixed,
                'errors' => $errors,
            ];
        }
    }

    /**
     * Get summary of inmate escapee statuses
     */
    public function getStatusSummary(): array
    {
        $totalInmates = Inmate::count();
        $totalEscapees = Inmate::where('escapee', 1)->count();
        $totalRecaptured = Inmate::where('recaptured', 1)->count();
        $activeEscapees = Inmate::where('escapee', 1)->where('recaptured', 0)->count();

        // Get counts from escapees table for comparison
        $escapeeTableTotal = Escapee::count();
        $escapeeTableRecaptured = Escapee::where('current_status', 'recaptured')->count();
        $escapeeTableActive = Escapee::whereIn('current_status', ['escaped', 'unknown'])->count();

        return [
            'inmate_register' => [
                'total_inmates' => $totalInmates,
                'marked_as_escapees' => $totalEscapees,
                'marked_as_recaptured' => $totalRecaptured,
                'active_escapees' => $activeEscapees,
            ],
            'escapee_table' => [
                'total_escapee_records' => $escapeeTableTotal,
                'recaptured_records' => $escapeeTableRecaptured,
                'active_escapee_records' => $escapeeTableActive,
            ],
            'sync_status' => [
                'in_sync' => $totalEscapees === $escapeeTableTotal && $totalRecaptured === $escapeeTableRecaptured,
                'last_sync' => null, // Could be stored in a settings table
            ],
        ];
    }
}
