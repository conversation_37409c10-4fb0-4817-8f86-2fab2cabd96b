<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Staff;
use App\Services\PermissionCacheService;
use Modules\AdminManagement\Entities\HqAdmin;
use Modules\TrainingManagement\Entities\TrainingProgram;
use Modules\IdentityManagement\Entities\Inmate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Cache;

class RbacImplementationTest extends TestCase
{
    use RefreshDatabase;

    protected PermissionCacheService $cacheService;
    protected Staff $staff;
    protected HqAdmin $superAdmin;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->cacheService = app(PermissionCacheService::class);
        
        // Create test staff
        $this->staff = Staff::factory()->create([
            'status' => 'active',
            'is_active' => true,
        ]);
        
        // Create super admin
        $this->superAdmin = HqAdmin::factory()->create([
            'is_super_admin' => true,
        ]);
    }

    /** @test */
    public function permission_cache_service_caches_permissions()
    {
        // Clear any existing cache
        $this->cacheService->clearStaffCache($this->staff);
        
        // First call should hit database and cache result
        $permissions1 = $this->cacheService->getStaffPermissions($this->staff);
        
        // Second call should use cache
        $permissions2 = $this->cacheService->getStaffPermissions($this->staff);
        
        $this->assertEquals($permissions1->toArray(), $permissions2->toArray());
        
        // Verify cache exists
        $cacheKey = "rbac:permissions:{$this->staff->id}:null:null";
        $this->assertTrue(Cache::has($cacheKey));
    }

    /** @test */
    public function staff_observer_clears_cache_on_update()
    {
        // Warm cache
        $this->cacheService->warmCache($this->staff);
        
        $cacheKey = "rbac:permissions:{$this->staff->id}:null:null";
        $this->assertTrue(Cache::has($cacheKey));
        
        // Update staff (should trigger observer)
        $this->staff->update(['status' => 'inactive']);
        
        // Cache should be cleared
        $this->assertFalse(Cache::has($cacheKey));
    }

    /** @test */
    public function super_admin_bypasses_all_authorization()
    {
        $this->actingAs($this->superAdmin);
        
        // Super admin should pass all gate checks
        $this->assertTrue(Gate::allows('view-inmates'));
        $this->assertTrue(Gate::allows('manage-all-training'));
        $this->assertTrue(Gate::allows('system-admin'));
        $this->assertTrue(Gate::allows('any-permission-that-doesnt-exist'));
    }

    /** @test */
    public function authorize_middleware_handles_single_permission()
    {
        $this->actingAs($this->staff);
        
        // Mock staff to have specific permission
        $this->staff->shouldReceive('hasPermission')
                   ->with('view-inmates', null, null)
                   ->andReturn(true);
        
        $response = $this->get('/api/v1/inmates');
        
        // Should not get 403 if permission exists
        $this->assertNotEquals(403, $response->status());
    }

    /** @test */
    public function authorize_middleware_handles_multiple_permissions()
    {
        $this->actingAs($this->staff);
        
        // Test route with multiple permissions (any)
        $response = $this->get('/api/v1/training-programs');
        
        // Response depends on actual permissions, but should not error
        $this->assertNotEquals(500, $response->status());
    }

    /** @test */
    public function base_policy_provides_consistent_authorization()
    {
        $this->actingAs($this->staff);
        
        $trainingProgram = TrainingProgram::factory()->create();
        
        // Test policy methods
        $canView = Gate::allows('view', $trainingProgram);
        $canViewAny = Gate::allows('viewAny', TrainingProgram::class);
        
        // Should return boolean values
        $this->assertIsBool($canView);
        $this->assertIsBool($canViewAny);
    }

    /** @test */
    public function cache_service_handles_batch_permission_checks()
    {
        $permissions = ['view-inmates', 'manage-inmates', 'view-training-programs'];
        
        // Test hasAnyPermission
        $hasAny = $this->cacheService->staffHasAnyPermission($this->staff, $permissions);
        $this->assertIsBool($hasAny);
        
        // Test hasAllPermissions
        $hasAll = $this->cacheService->staffHasAllPermissions($this->staff, $permissions);
        $this->assertIsBool($hasAll);
    }

    /** @test */
    public function cache_service_handles_scoped_permissions()
    {
        $stateId = 1;
        $prisonId = 1;
        
        // Test scoped permission check
        $hasPermission = $this->cacheService->staffHasPermission(
            $this->staff, 
            'view-inmates', 
            $stateId, 
            $prisonId
        );
        
        $this->assertIsBool($hasPermission);
        
        // Verify cache key includes scope
        $cacheKey = "rbac:permissions:{$this->staff->id}:{$stateId}:{$prisonId}";
        $this->assertTrue(Cache::has($cacheKey));
    }

    /** @test */
    public function staff_model_methods_use_cache_service()
    {
        // Test that Staff model methods delegate to cache service
        $hasPermission = $this->staff->hasPermission('view-inmates');
        $this->assertIsBool($hasPermission);
        
        $hasAny = $this->staff->hasAnyPermission(['view-inmates', 'manage-inmates']);
        $this->assertIsBool($hasAny);
        
        $hasAll = $this->staff->hasAllPermissions(['view-inmates', 'manage-inmates']);
        $this->assertIsBool($hasAll);
        
        $allPermissions = $this->staff->getAllPermissions();
        $this->assertIsArray($allPermissions);
    }

    /** @test */
    public function cache_invalidation_patterns_work()
    {
        // Warm cache for multiple scopes
        $this->cacheService->getStaffPermissions($this->staff, 1, 1);
        $this->cacheService->getStaffPermissions($this->staff, 1, 2);
        $this->cacheService->getStaffPermissions($this->staff, 2, 1);
        
        // Clear all cache for staff
        $this->cacheService->clearStaffCache($this->staff);
        
        // All cache keys should be cleared
        $this->assertFalse(Cache::has("rbac:permissions:{$this->staff->id}:1:1"));
        $this->assertFalse(Cache::has("rbac:permissions:{$this->staff->id}:1:2"));
        $this->assertFalse(Cache::has("rbac:permissions:{$this->staff->id}:2:1"));
    }

    /** @test */
    public function cache_service_provides_statistics()
    {
        // Perform some cache operations
        $this->cacheService->getStaffPermissions($this->staff);
        $this->cacheService->staffHasPermission($this->staff, 'view-inmates');
        
        $stats = $this->cacheService->getCacheStats();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_keys', $stats);
        $this->assertArrayHasKey('memory_usage', $stats);
    }

    /** @test */
    public function policies_extend_base_policy_correctly()
    {
        $inmate = Inmate::factory()->create();
        
        // Test that policies extend BasePolicy
        $policy = Gate::getPolicyFor($inmate);
        $this->assertInstanceOf(\App\Policies\BasePolicy::class, $policy);
        
        // Test base policy methods work
        $this->actingAs($this->superAdmin);
        $this->assertTrue(Gate::allows('view', $inmate));
        $this->assertTrue(Gate::allows('viewAny', Inmate::class));
    }

    /** @test */
    public function middleware_registration_works()
    {
        // Test that authorize middleware is registered
        $kernel = app(\Illuminate\Contracts\Http\Kernel::class);
        $middlewareGroups = $kernel->getMiddlewareGroups();
        $routeMiddleware = $kernel->getRouteMiddleware();
        
        $this->assertArrayHasKey('authorize', $routeMiddleware);
        $this->assertEquals(\App\Http\Middleware\AuthorizeMiddleware::class, $routeMiddleware['authorize']);
    }

    /** @test */
    public function gate_helper_service_registers_gates()
    {
        // Test that common gates are registered
        $this->assertTrue(Gate::has('view-training-programs'));
        $this->assertTrue(Gate::has('view-inmates'));
        $this->assertTrue(Gate::has('manage-belongings'));
        $this->assertTrue(Gate::has('system-admin'));
    }

    protected function tearDown(): void
    {
        // Clean up cache
        Cache::flush();
        parent::tearDown();
    }
}
