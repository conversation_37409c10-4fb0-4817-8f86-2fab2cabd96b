<?php

namespace Modules\IdentityManagement\Http\Controllers\Api\v1;

use App\Traits\Common;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Gate;
use Modules\IdentityManagement\Entities\Inmate;
use Modules\IdentityManagement\Entities\Convict;

class ConvictController extends Controller
{
  /**
   * Display a listing of the resource.
   * @return Response
   */
  use Common;

  public function __construct()
  {
      $this->middleware('auth:sanctum');
      $this->middleware('authorize:view-inmates')->only(['index', 'show']);
      $this->middleware('authorize:create-inmates')->only(['store']);
      $this->middleware('authorize:update-inmates')->only(['update']);
      $this->middleware('authorize:delete-inmates')->only(['destroy']);
  }
  /**
   * The index function in this PHP code performs a custom query on the Inmate model, filtering out
   * the count value if specified in the request query, and returns the result as a paginated list of
   * inmates.
   *
   * @param Request request The  parameter is an instance of the Request class, which
   * represents an HTTP request. It contains information about the request such as the request
   * method, headers, query parameters, and request body.
   *
   * @return The code is returning either a count of the filtered inmates or a paginated list of the
   * filtered inmates.
   */
  public function index(Request $request)
  {
    // Authorization handled by middleware

    /*
    /// Do a custom query on Inmate model,
    */
    $noIncludeArray = array('count');
    $query = $request->query();
    //// This method will do a custom query to filter out the count value
    $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);
    isset($query['count']) ? $inmate = Inmate::has('convicted')->where($filteredQuery)->count() : $inmate = Inmate::has('convicted')->where($filteredQuery)->paginate();
    return $inmate;
  }

  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @return Response
   */
  public function store(Request $request)
  {
    // Authorization handled by middleware

    $request->validate([
      'prisoner_no' => 'required',
      'prosecuting_agency' => 'required',
      'charge_no_suit_no' => 'required',
      'court_of_arraignment' => 'required',
      'arraignment_date' => 'required',
      'sentence_type' => 'required',
      'sentence_passed' => 'required',
      'date_convicted' => 'required',
    ]);

    $checkConvict = Convict::where('prisoner_no', $request->prisoner_no)
      ->where('charge_no_suit_no', $request->charge_no_suit_no)
      ->first();

    if ($checkConvict) {
      return response()->json([
        'success' => false,
        'message' => 'Convict record already exists',
        'data' => $checkConvict
      ], 409);
    }

    $convict = Convict::create($request->all());

    return response()->json([
      'success' => true,
      'message' => 'Convict record created successfully',
      'data' => $convict
    ], 201);
  }

  /**
   * Show the specified resource.
   * @param int $id
   * @return Response
   */
  public function show($id)
  {
    if (!Gate::allows('view-inmates')) {
        return response()->json(['message' => 'Unauthorized'], 403);
    }

    //
  }

  /**
   * Update the specified resource in storage.
   * @param Request $request
   * @param int $id
   * @return Response
   */
  public function update(Request $request, $id)
  {
    // Authorization handled by middleware

    //
  }

  /**
   * Remove the specified resource from storage.
   * @param int $id
   * @return Response
   */
  public function destroy($id)
  {
    // Authorization handled by middleware

    //
  }
}
