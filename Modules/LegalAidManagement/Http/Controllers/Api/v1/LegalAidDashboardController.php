<?php

namespace Modules\LegalAidManagement\Http\Controllers\Api\v1;

use App\Traits\Common;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Contracts\Support\Renderable;
use Modules\LegalAidManagement\Entities\LegalRepresentative;
use Illuminate\Support\Facades\DB;
use Modules\LegalAidManagement\Entities\LegalRepDefaulters;
use Modules\LegalAidManagement\Entities\InmateLegalReps;
use Illuminate\Support\Facades\Gate;
use Modules\LegalAidManagement\Services\LegalAidDashboardService;

class LegalAidDashboardController extends Controller
{
    protected $legalAidDashboardService;

    public function __construct(LegalAidDashboardService $legalAidDashboardService)
    {
        $this->middleware('auth:sanctum');
        $this->middleware('authorize:view-legal-aid-dashboard');

        $this->legalAidDashboardService = $legalAidDashboardService;
    }

    public function periodic_stats(Request $request)
    {
        // Authorization handled by middleware

        $validated = $request->validate([
            'year'        => 'nullable|integer|min:1900|max:' . date('Y'),
            'state_id'    => 'nullable|integer',
            'prison_id'   => 'nullable|integer',
            'zone_id'     => 'nullable|integer',
            'period_type' => 'nullable|in:quarterly,monthly,weekly,daily',

        ]);

        $statistics = $this->legalAidDashboardService->generatePeriodicStats($validated);

        return response()->json([
            'success'         => true,
            'data'            => $statistics,
            'filters_applied' => $validated,
        ]);

    }
}
