<?php
namespace App\Models;

use App\Models\Escapee;
use App\Models\Prison;
use App\Models\State;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EscapeeBatch extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function escapees()
    {
        return $this->hasMany(Escapee::class, 'batch_id', 'id');
    }

    public function state()
    {
        return $this->belongsTo(State::class);
    }

    public function prison()
    {
        return $this->belongsTo(Prison::class);
    }

}
