<?php

namespace App\Policies;

use App\Models\Staff;
use App\Policies\BasePolicy;
use Modules\AdminManagement\Entities\HqAdmin;
use Modules\IdentityManagement\Entities\Convict;

class ConvictPolicy extends BasePolicy
{
    /**
     * Get the base permission name for Convicts
     */
    protected function getBasePermission(): string
    {
        return 'inmates'; // Convicts use same permissions as inmates
    }

    /**
     * Override view permissions to include manage permissions
     */
    protected function getViewPermissions(): array
    {
        return [
            'view-inmates',
            'view-convicts',
            'manage-inmates',
            'manage-all'
        ];
    }

    /**
     * Override create permissions
     */
    protected function getCreatePermission(): string
    {
        return 'create-inmates';
    }

    /**
     * Override update permissions
     */
    protected function getUpdatePermission(): string
    {
        return 'update-inmates';
    }

    /**
     * Override delete permissions
     */
    protected function getDeletePermission(): string
    {
        return 'delete-inmates';
    }
}
