<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\Staff;
use Modules\AdminManagement\Entities\StateZoneAdmin;

class SuperAdminRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create super admin roles
        $superAdminRoles = [
            [
                'name' => 'superadmin',
                'display_name' => 'Super Administrator',
                'description' => 'Full system access with all permissions',
                'scope' => 'system',
            ],
            [
                'name' => 'super-admin',
                'display_name' => 'Super Admin (Alternative)',
                'description' => 'Full system access with all permissions (alternative naming)',
                'scope' => 'system',
            ],
            [
                'name' => 'super_admin',
                'display_name' => 'Super Admin (Underscore)',
                'description' => 'Full system access with all permissions (underscore naming)',
                'scope' => 'system',
            ],
        ];

        foreach ($superAdminRoles as $roleData) {
            $role = Role::firstOrCreate(
                ['name' => $roleData['name']],
                $roleData
            );

            // Assign all permissions to super admin roles
            $allPermissions = Permission::where('is_active', true)->get();
            $role->permissions()->sync($allPermissions->pluck('id'));

            $this->command->info("Created/Updated super admin role: {$role->name}");
        }

        // Example: Assign super admin role to a test user
        $this->assignTestSuperAdminRoles();
    }

    /**
     * Assign super admin roles to test users for demonstration
     */
    private function assignTestSuperAdminRoles(): void
    {
        $superAdminRole = Role::where('name', 'superadmin')->first();

        if (!$superAdminRole) {
            $this->command->warn('Super admin role not found, skipping test assignments');
            return;
        }

        // Example: Assign to a Staff user (if exists)
        $testStaff = Staff::first();
        if ($testStaff) {
            $testStaff->roles()->syncWithoutDetaching([
                $superAdminRole->id => [
                    'state_id' => null,
                    'prison_id' => null,
                    'assigned_at' => now(),
                    'expires_at' => null,
                    'is_active' => true,
                ]
            ]);
            $staffName = $testStaff->name ?? $testStaff->service_no ?? 'Unknown';
            $this->command->info("Assigned super admin role to Staff: {$staffName}");
        }

        // Example: Assign to a StateZoneAdmin user (if exists)
        $testStateZoneAdmin = StateZoneAdmin::first();
        if ($testStateZoneAdmin) {
            $testStateZoneAdmin->universalRoles()->syncWithoutDetaching([
                $superAdminRole->id => [
                    'state_id' => null,
                    'prison_id' => null,
                    'assigned_at' => now(),
                    'expires_at' => null,
                    'is_active' => true,
                ]
            ]);
            $adminName = ($testStateZoneAdmin->first_name ?? '') . ' ' . ($testStateZoneAdmin->last_name ?? '');
            $this->command->info("Assigned super admin role to StateZoneAdmin: {$adminName}");
        }

        $this->command->info('Test super admin role assignments completed');
    }
}
