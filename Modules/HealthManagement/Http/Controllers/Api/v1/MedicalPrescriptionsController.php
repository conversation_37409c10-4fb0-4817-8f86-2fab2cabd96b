<?php

namespace Modules\HealthManagement\Http\Controllers\Api\v1;

use App\Traits\Common;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Contracts\Support\Renderable;
use Modules\HealthManagement\Entities\MedicalPrescriptions;

class MedicalPrescriptionsController extends Controller
{
    use Common;

    public function __construct()
    {
        $this->middleware('authorize:view-medical-prescriptions')->only(['index', 'show', 'getPrescriptionsByPrisoner']);
        $this->middleware('authorize:create-medical-prescriptions')->only(['store']);
        $this->middleware('authorize:update-medical-prescriptions')->only(['update']);
        $this->middleware('authorize:delete-medical-prescriptions')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(Request $request)
    {
        // Authorization handled by middleware

        $noIncludeArray = array('count', 'page', 'include', 'paginate', 'start_date', 'end_date', 'search');
        $relations = array('diagnostic');
        $includeString = explode(',', $request->include);
        $query = $request->query();
        $paginate = $request->paginate ? $request->paginate : 20;

        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

        $prescriptions = MedicalPrescriptions::orderBy('prescription_date', 'desc');

        $prescriptions->where($filteredQuery);

        if ($request->has('start_date') && $request->has('end_date')) {
            $prescriptions->whereBetween('prescription_date', [$request->start_date, $request->end_date]);
        } else if ($request->has('start_date')) {
            $prescriptions->where('prescription_date', '>=', $request->start_date);
        } else if ($request->has('end_date')) {
            $prescriptions->where('prescription_date', '<=', $request->end_date);
        }

        if ($request->has('search')) {
            $searchTerm = $request->search;
            $prescriptions->where(function($query) use ($searchTerm) {
                $query->where('prisoner_no', 'like', "%{$searchTerm}%")
                      ->orWhere('medication', 'like', "%{$searchTerm}%")
                      ->orWhere('dosage', 'like', "%{$searchTerm}%")
                      ->orWhere('instruction', 'like', "%{$searchTerm}%");
            });
        }

        // Paginate results
        $results = $prescriptions->paginate($paginate);

        if($request->include){
            foreach($includeString as $string){
                $checkRelationshipArray = $this->shouldIncludeRelation($relations, $string);
                if($checkRelationshipArray) $results->load($string);
            }
        }

        $results->getCollection()->each(function ($data) {
            if ($data->inmate) {
                $data->prisoner_fullname = trim("{$data->inmate->surname} {$data->inmate->first_name} {$data->inmate->other_names}");
                unset($data->inmate);
            }
        });

        if($request->count){
            $results = $results->total();
        }

        return response()->json(['data' => $results]);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        return view('healthmanagement::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        // Authorization handled by middleware

        $request->validate([
            'prisoner_no' => 'required|exists:inmates_all_class_register,prisoner_no',
            'diagnostic_id' => 'required|exists:medical_diagnostics,id',
            'prescription_date' => 'required|date',
            'medication' => 'required|string',
            'dosage' => 'required|string',
            'instruction' => 'required|string',
        ]);

        $prescription = new MedicalPrescriptions($request->all());
        $prescription->save();

        return response()->json([
            'message' => 'Medical prescription created successfully',
            'data' => $prescription
        ], 201);
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function show($id)
    {
        // Authorization handled by middleware

        $prescription = MedicalPrescriptions::findOrFail($id);
        return response()->json(['data' => $prescription]);
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        return view('healthmanagement::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        // Authorization handled by middleware

        $request->validate([
            'prisoner_no' => 'required|exists:inmates_all_class_register,prisoner_no',
            'diagnostic_id' => 'required|exists:medical_diagnostics,id',
            'prescription_date' => 'required|date',
            'medication' => 'required|string',
            'dosage' => 'required|string',
            'instruction' => 'required|string',
            'status' => 'nullable|integer|in:0,1',
        ]);

        $prescription = MedicalPrescriptions::findOrFail($id);
        $prescription->update($request->all());

        return response()->json([
            'message' => 'Medical prescription updated successfully',
            'data' => $prescription
        ]);
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        // Authorization handled by middleware

        $prescription = MedicalPrescriptions::findOrFail($id);
        $prescription->delete();

        return response()->json([
            'message' => 'Medical prescription deleted successfully'
        ]);
    }

     /**
     * Get all prescriptions for a specific prisoner
     * @param string $prisoner_no
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPrescriptionsByPrisoner($prisoner_no)
    {
        // Authorization handled by middleware

        $prescriptions = MedicalPrescriptions::where('prisoner_no', $prisoner_no)
            ->orderBy('prescription_date', 'desc')
            ->get();

        if ($prescriptions->isEmpty()) {
            return response()->json([
                'message' => 'No prescriptions found for this prisoner',
                'data' => []
            ]);
        }

        return response()->json([
            'data' => $prescriptions
        ]);
    }
}
