<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Auth\Access\AuthorizationException;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Staff;
use Modules\AdminManagement\Entities\HqAdmin;

class AuthorizeMiddleware
{
    /**
     * Handle an incoming request.
     *
     * Unified middleware for all authorization checks.
     * Supports multiple authorization patterns:
     * - authorize:permission - Check specific permission
     * - authorize:permission,state_id,prison_id - Check permission with scope
     * - authorize:ability,model - Check gate/policy ability
     * - authorize:any:perm1,perm2,perm3 - Check any of multiple permissions
     * - authorize:all:perm1,perm2,perm3 - Check all permissions
     */
    public function handle(Request $request, Closure $next, string $authorization, ...$parameters): Response
    {
        if (!auth()->check()) {
            throw new AuthorizationException('Authentication required.');
        }

        $user = auth()->user();

        // Enhanced Super admin bypass - Check all user types for super admin privileges
        if ($this->isSuperAdmin($user)) {
            return $next($request);
        }

        // Parse authorization string
        $authParts = explode(':', $authorization);
        $authType = $authParts[0];

        switch ($authType) {
            case 'any':
                $this->checkAnyPermissions($user, $authParts[1] ?? '', $parameters);
                break;

            case 'all':
                $this->checkAllPermissions($user, $authParts[1] ?? '', $parameters);
                break;

            case 'ability':
                $this->checkAbility($user, $authorization, $parameters, $request);
                break;

            default:
                $this->checkPermission($user, $authorization, $parameters, $request);
                break;
        }

        return $next($request);
    }

    /**
     * Check if user has any of the specified permissions
     */
    protected function checkAnyPermissions($user, string $permissionsString, array $parameters): void
    {
        if (empty($permissionsString)) {
            throw new AuthorizationException('No permissions specified.');
        }

        $permissions = explode(',', $permissionsString);
        $stateId = $this->getStateId($parameters);
        $prisonId = $this->getPrisonId($parameters);

        if (!$user instanceof Staff) {
            throw new AuthorizationException('User type not supported for permission checks.');
        }

        if (!$user->hasAnyPermission($permissions, $stateId, $prisonId)) {
            throw new AuthorizationException('Insufficient permissions. Required any of: ' . implode(', ', $permissions));
        }
    }

    /**
     * Check if user has all of the specified permissions
     */
    protected function checkAllPermissions($user, string $permissionsString, array $parameters): void
    {
        if (empty($permissionsString)) {
            throw new AuthorizationException('No permissions specified.');
        }

        $permissions = explode(',', $permissionsString);
        $stateId = $this->getStateId($parameters);
        $prisonId = $this->getPrisonId($parameters);

        if (!$user instanceof Staff) {
            throw new AuthorizationException('User type not supported for permission checks.');
        }

        if (!$user->hasAllPermissions($permissions, $stateId, $prisonId)) {
            throw new AuthorizationException('Insufficient permissions. Required all of: ' . implode(', ', $permissions));
        }
    }

    /**
     * Check gate/policy ability
     */
    protected function checkAbility($user, string $authorization, array $parameters, Request $request): void
    {
        $ability = $parameters[0] ?? null;
        $modelClass = $parameters[1] ?? null;

        if (!$ability) {
            throw new AuthorizationException('No ability specified.');
        }

        if ($modelClass) {
            // Policy-based authorization
            $modelId = $request->route($this->getRouteParameterName($modelClass));

            if ($modelId) {
                $model = $modelClass::findOrFail($modelId);
                Gate::authorize($ability, $model);
            } else {
                Gate::authorize($ability, $modelClass);
            }
        } else {
            // Gate-based authorization
            Gate::authorize($ability);
        }
    }

    /**
     * Check single permission with optional scope
     */
    protected function checkPermission($user, string $permission, array $parameters, Request $request): void
    {
        if (!$user instanceof Staff) {
            throw new AuthorizationException('User type not supported for permission checks.');
        }

        $stateId = $this->getStateId($parameters, $request);
        $prisonId = $this->getPrisonId($parameters, $request);

        if (!$user->hasPermission($permission, $stateId, $prisonId)) {
            throw new AuthorizationException("Insufficient permissions. Required: {$permission}");
        }
    }

    /**
     * Get state ID from parameters or request
     */
    protected function getStateId(array $parameters, ?Request $request = null): ?int
    {
        // From middleware parameters
        if (isset($parameters[0]) && is_numeric($parameters[0])) {
            return (int) $parameters[0];
        }

        // From request route parameters
        if ($request) {
            $stateId = $request->route('state_id') ?? $request->route('state') ?? $request->input('state_id');
            if ($stateId && is_numeric($stateId)) {
                return (int) $stateId;
            }
        }

        return null;
    }

    /**
     * Get prison ID from parameters or request
     */
    protected function getPrisonId(array $parameters, ?Request $request = null): ?int
    {
        // From middleware parameters
        if (isset($parameters[1]) && is_numeric($parameters[1])) {
            return (int) $parameters[1];
        }

        // From request route parameters
        if ($request) {
            $prisonId = $request->route('prison_id') ?? $request->route('prison') ?? $request->input('prison_id');
            if ($prisonId && is_numeric($prisonId)) {
                return (int) $prisonId;
            }
        }

        return null;
    }

    /**
     * Get route parameter name for model
     */
    protected function getRouteParameterName(string $modelClass): string
    {
        $className = class_basename($modelClass);
        return strtolower($className);
    }

    /**
     * Handle authorization failure with detailed error information
     */
    protected function handleAuthorizationFailure(string $message, array $context = []): void
    {
        $user = auth()->user();

        $errorContext = [
            'user_id' => $user->id ?? null,
            'user_type' => get_class($user),
            'requested_permission' => $context['permission'] ?? null,
            'state_id' => $context['state_id'] ?? null,
            'prison_id' => $context['prison_id'] ?? null,
            'route' => request()->route()?->getName(),
            'url' => request()->url(),
        ];

        logger()->warning('Authorization failed', $errorContext);

        throw new AuthorizationException($message);
    }

    /**
     * Check if the user is a super admin (supports all user types)
     */
    protected function isSuperAdmin($user): bool
    {
        // Check if user has isSuperAdmin method and call it
        if (method_exists($user, 'isSuperAdmin')) {
            return $user->isSuperAdmin();
        }

        // Fallback: Check if user is HqAdmin (legacy support)
        if ($user instanceof HqAdmin) {
            return true; // All HQ admins are super admins
        }

        return false;
    }
}
