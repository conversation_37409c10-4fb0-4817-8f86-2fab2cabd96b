<?php

namespace Database\Factories;

use App\Models\EscapeeBatch;
use App\Models\States;
use App\Models\Prison;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EscapeeBatch>
 */
class EscapeeBatchFactory extends Factory
{
    protected $model = EscapeeBatch::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $states = States::pluck('id')->toArray();
        $prisons = Prison::pluck('id')->toArray();
        
        return [
            'old_batch_id' => $this->faker->optional()->uuid(),
            'title' => $this->faker->sentence(4),
            'description' => $this->faker->optional()->paragraph(),
            'batch_status' => $this->faker->randomElement(['created', 'under_review', 'investigated', 'closed']),
            'state_id' => $this->faker->randomElement($states),
            'prison_id' => $this->faker->randomElement($prisons),
            'uploaded' => $this->faker->boolean(30), // 30% chance of being uploaded
            'status' => $this->faker->boolean(95), // 95% chance of being active
            'created_at' => $this->faker->dateTimeBetween('-2 years', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the batch is created status.
     */
    public function created(): static
    {
        return $this->state(fn (array $attributes) => [
            'batch_status' => 'created',
        ]);
    }

    /**
     * Indicate that the batch is under review.
     */
    public function underReview(): static
    {
        return $this->state(fn (array $attributes) => [
            'batch_status' => 'under_review',
        ]);
    }

    /**
     * Indicate that the batch is investigated.
     */
    public function investigated(): static
    {
        return $this->state(fn (array $attributes) => [
            'batch_status' => 'investigated',
        ]);
    }

    /**
     * Indicate that the batch is closed.
     */
    public function closed(): static
    {
        return $this->state(fn (array $attributes) => [
            'batch_status' => 'closed',
        ]);
    }

    /**
     * Indicate that the batch has been uploaded.
     */
    public function uploaded(): static
    {
        return $this->state(fn (array $attributes) => [
            'uploaded' => true,
        ]);
    }

    /**
     * Indicate that the batch is for a specific state.
     */
    public function forState(int $stateId): static
    {
        return $this->state(fn (array $attributes) => [
            'state_id' => $stateId,
        ]);
    }

    /**
     * Indicate that the batch is for a specific prison.
     */
    public function forPrison(int $prisonId): static
    {
        return $this->state(fn (array $attributes) => [
            'prison_id' => $prisonId,
        ]);
    }

    /**
     * Indicate that the batch is recent (created within last 30 days).
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
        ]);
    }
}
