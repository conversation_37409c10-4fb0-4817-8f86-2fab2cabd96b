<?php

namespace Modules\CaseManagement\Http\Controllers;
use Inertia\Inertia;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Gate;

class CaseManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('authorize:any:view-cases,manage-cases,manage-all')->only(['index']);
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index()
    {
        // Authorization handled by middleware

        return Inertia::render('../../Modules/CaseManagement/Resources/assets/js/views/index');
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        return view('casemanagement::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        Gate::authorize('create-cases');

        // Implementation needed
        return response()->json(['message' => 'Not implemented'], 501);
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function show($id)
    {
        Gate::authorize('view-cases');

        return view('casemanagement::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        return view('casemanagement::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        Gate::authorize('update-cases');

        // Implementation needed
        return response()->json(['message' => 'Not implemented'], 501);
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        Gate::authorize('delete-cases');

        // Implementation needed
        return response()->json(['message' => 'Not implemented'], 501);
    }
}
