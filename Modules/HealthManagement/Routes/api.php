<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\HealthManagement\Http\Controllers\Api\v1\MedicalAppointmentsController;
use Modules\HealthManagement\Http\Controllers\Api\v1\MedicalDiagnosticsController;
use Modules\HealthManagement\Http\Controllers\Api\v1\MedicalHistoryController;
use Modules\HealthManagement\Http\Controllers\Api\v1\MedicalLabTestController;
use Modules\HealthManagement\Http\Controllers\Api\v1\MedicalPrescriptionsController;
use Modules\HealthManagement\Http\Controllers\Api\v1\MedicalStatsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/healthmanagement', function (Request $request) {
    return $request->user();

});

Route::prefix('v1/health')->middleware(['auth:sanctum', 'universal.auth'])->group(function() {

    // Medical Appointments Routes with new RBAC
    Route::middleware('authorize:any:view-medical-appointments,manage-medical-appointments,manage-all-health,manage-all')->group(function () {
        Route::get('appointments', [MedicalAppointmentsController::class, 'index']);
        Route::get('appointments/{appointment}', [MedicalAppointmentsController::class, 'show']);
        Route::get('appointments/prisoner/{prisoner_no}', [MedicalAppointmentsController::class, 'getAppointmentsByPrisoner']);
    });

    Route::middleware('authorize:any:create-medical-appointments,manage-medical-appointments,manage-all-health,manage-all')->group(function () {
        Route::post('appointments', [MedicalAppointmentsController::class, 'store']);
    });

    Route::middleware('can:update-medical-appointments')->group(function () {
        Route::put('appointments/{appointment}', [MedicalAppointmentsController::class, 'update']);
        Route::patch('appointments/{appointment}', [MedicalAppointmentsController::class, 'update']);
    });

    Route::middleware('can:delete-medical-appointments')->group(function () {
        Route::delete('appointments/{appointment}', [MedicalAppointmentsController::class, 'destroy']);
    });

    // Medical Diagnostics Routes with RBAC
    Route::middleware('can:view-medical-diagnostics')->group(function () {
        Route::get('diagnostics', [MedicalDiagnosticsController::class, 'index']);
        Route::get('diagnostics/{diagnostic}', [MedicalDiagnosticsController::class, 'show']);
        Route::get('diagnostics/prisoner/{prisoner_no}', [MedicalDiagnosticsController::class, 'getDiagnosticsByPrisoner']);
    });

    Route::middleware('can:create-medical-diagnostics')->group(function () {
        Route::post('diagnostics', [MedicalDiagnosticsController::class, 'store']);
    });

    Route::middleware('can:update-medical-diagnostics')->group(function () {
        Route::put('diagnostics/{diagnostic}', [MedicalDiagnosticsController::class, 'update']);
        Route::patch('diagnostics/{diagnostic}', [MedicalDiagnosticsController::class, 'update']);
    });

    Route::middleware('can:delete-medical-diagnostics')->group(function () {
        Route::delete('diagnostics/{diagnostic}', [MedicalDiagnosticsController::class, 'destroy']);
    });

    // Medical History Routes with RBAC
    Route::middleware('can:view-medical-history')->group(function () {
        Route::get('history', [MedicalHistoryController::class, 'index']);
        Route::get('history/{history}', [MedicalHistoryController::class, 'show']);
    });

    Route::middleware('can:create-medical-history')->group(function () {
        Route::post('history', [MedicalHistoryController::class, 'store']);
    });

    Route::middleware('can:update-medical-history')->group(function () {
        Route::put('history/{history}', [MedicalHistoryController::class, 'update']);
        Route::patch('history/{history}', [MedicalHistoryController::class, 'update']);
    });

    Route::middleware('can:delete-medical-history')->group(function () {
        Route::delete('history/{history}', [MedicalHistoryController::class, 'destroy']);
    });

    // Medical Lab Tests Routes with RBAC
    Route::middleware('can:view-medical-lab-tests')->group(function () {
        Route::get('lab_test', [MedicalLabTestController::class, 'index']);
        Route::get('lab_test/{lab_test}', [MedicalLabTestController::class, 'show']);
        Route::get('lab_test/prisoner/{prisoner_no}', [MedicalLabTestController::class, 'getLabTestsByPrisoner']);
    });

    Route::middleware('can:create-medical-lab-tests')->group(function () {
        Route::post('lab_test', [MedicalLabTestController::class, 'store']);
    });

    Route::middleware('can:update-medical-lab-tests')->group(function () {
        Route::put('lab_test/{lab_test}', [MedicalLabTestController::class, 'update']);
        Route::patch('lab_test/{lab_test}', [MedicalLabTestController::class, 'update']);
    });

    Route::middleware('can:delete-medical-lab-tests')->group(function () {
        Route::delete('lab_test/{lab_test}', [MedicalLabTestController::class, 'destroy']);
    });

    // Medical Prescriptions Routes with RBAC
    Route::middleware('can:view-medical-prescriptions')->group(function () {
        Route::get('prescriptions', [MedicalPrescriptionsController::class, 'index']);
        Route::get('prescriptions/{prescription}', [MedicalPrescriptionsController::class, 'show']);
        Route::get('prescriptions/prisoner/{prisoner_no}', [MedicalPrescriptionsController::class, 'getPrescriptionsByPrisoner']);
    });

    Route::middleware('can:create-medical-prescriptions')->group(function () {
        Route::post('prescriptions', [MedicalPrescriptionsController::class, 'store']);
    });

    Route::middleware('can:update-medical-prescriptions')->group(function () {
        Route::put('prescriptions/{prescription}', [MedicalPrescriptionsController::class, 'update']);
        Route::patch('prescriptions/{prescription}', [MedicalPrescriptionsController::class, 'update']);
    });

    Route::middleware('can:delete-medical-prescriptions')->group(function () {
        Route::delete('prescriptions/{prescription}', [MedicalPrescriptionsController::class, 'destroy']);
    });

    // Health Dashboard Stats (view-health-dashboard permission)
    Route::middleware('can:view-health-dashboard')->group(function () {
        Route::get('get_dashboard_stats_for_health', [MedicalStatsController::class, 'getHealthDashboardStats'])->name('health_stats_data');
    });

});

// Route::prefix('v1/health')->middleware('auth:sanctum')->group(function () {
//     Route::apiResource('history', MedicalHistoryController::class);
// });
