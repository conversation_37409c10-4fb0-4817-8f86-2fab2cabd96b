<?php

namespace App\Policies;

use App\Models\Staff;
use App\Policies\BasePolicy;
use Modules\AdminManagement\Entities\HqAdmin;
use Modules\IdentityManagement\Entities\Inmate;

class InmatePolicy extends BasePolicy
{
    /**
     * Get the base permission name for inmates
     */
    protected function getBasePermission(): string
    {
        return 'inmates';
    }

    /**
     * Override view permissions to include manage permissions
     */
    protected function getViewPermissions(): array
    {
        return [
            'view-inmates',
            'manage-inmates',
            'manage-all'
        ];
    }

    /**
     * Custom authorization for batch operations
     */
    public function batchRelease($user): bool
    {
        return $this->hasAnyPermission($user, [
            'update-inmates',
            'manage-inmates',
            'manage-all'
        ]);
    }

    /**
     * Custom authorization for batch transfers
     */
    public function batchTransfer($user): bool
    {
        return $this->hasAnyPermission($user, [
            'update-inmates',
            'manage-inmates',
            'manage-all'
        ]);
    }

    /**
     * Custom authorization for verifying inmates
     */
    public function verify($user): bool
    {
        return $this->hasAnyPermission($user, [
            'create-inmates',
            'update-inmates',
            'manage-inmates',
            'manage-all'
        ]);
    }

    /**
     * Custom authorization for searching inmates
     */
    public function search($user): bool
    {
        return $this->hasAnyPermission($user, [
            'view-inmates',
            'manage-inmates',
            'manage-all'
        ]);
    }

    /**
     * Custom authorization for viewing specific inmate categories
     */
    public function viewCategory($user, string $category): bool
    {
        $permissions = [
            'view-inmates',
            'manage-inmates',
            'manage-all'
        ];

        // Add category-specific permissions if they exist
        switch ($category) {
            case 'remand':
                $permissions[] = 'view-remand-inmates';
                break;
            case 'lifers':
                $permissions[] = 'view-lifer-inmates';
                break;
            case 'death-roll':
                $permissions[] = 'view-death-roll-inmates';
                break;
        }

        return $this->hasAnyPermission($user, $permissions);
    }

    /**
     * Custom authorization for updating biometrics
     */
    public function updateBiometrics($user, Inmate $inmate): bool
    {
        return $this->canManageResource($user, $inmate, 'update-inmates');
    }
}
