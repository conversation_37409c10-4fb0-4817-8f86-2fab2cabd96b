<?php
namespace Modules\IdentityManagement\Http\Controllers\Api\v1;

use App\Traits\Common;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use Modules\IdentityManagement\Entities\ATP;
use Modules\IdentityManagement\Entities\Convict;
use Modules\IdentityManagement\Entities\Inmate;
use Modules\IdentityManagement\Entities\Warrant;

class InmateController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }
    /**
     * Display a listing of the resource.
     * @return Response
     */

    use Common; //Trait
    /**
     * The index function in this PHP code retrieves a list of inmates based on custom query parameters
     * and includes related data if specified, and returns the result.
     *
     * @param Request request The  parameter is an instance of the Request class, which
     * represents an HTTP request. It contains information about the request, such as the request
     * method, headers, query parameters, and request body. In this code, it is used to retrieve query
     * parameters and include relationships in the query.
     *
     * @return the result of the query on the Inmate model. If the "count" parameter is present in the
     * request, it will return the total count of the query results. Otherwise, it will return a
     * paginated list of inmates based on the query parameters. If the "include" parameter is present,
     * it will load the specified relationships on the inmates.
     */
    public function index(Request $request)
    {
        // Authorization is now handled by middleware or policy
        Gate::authorize('viewAny', Inmate::class);

        /*
        /// Do a custom query on Inmate model,
        */

        $noIncludeArray = ['count', 'page', 'include', 'paginate', 'has'];
        $relations      = ['awaiting', 'sittings', 'convicted', 'medical_history', 'center', 'warrants', 'legal_rep'];
        $includeString  = explode(',', $request->include);
        $query          = $request->query();
        $paginate       = $request->paginate ? $request->paginate : 20;
        //// This method will do a custom query based on query string to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

        $inmate = Inmate::whereNull('who_authorised_release_or_discharge')->whereNull('who_authorised_transfer')->whereNotNull('inmate_status')->orderBy('id', 'desc')->where($filteredQuery);

        ///////// BUILD THE WHERE QUERY STATEMENT
        $inmate = $inmate->when($request->has, function ($query) use ($request) {
            return $query->whereHas($request->has);
        });

        ///////// IF THE REQUEST NEEDS PAGINATION

        $inmate = $inmate->when($request->paginate, function ($query) use ($request, $paginate) {
            return $paginate != 'all' ? $query->paginate($paginate) : $query->get();
        });

        ////////IF THE RECORD NEEDED HAS A RELATIONSHIP

        if ($request->include) {
            foreach ($includeString as $string) {
                $checkRelationshipArray = $this->shouldIncludeRelation($relations, $string);
                if ($checkRelationshipArray) {
                    $inmate->load($string);
                }
            }
        }

        if ($request->count) {
            $inmate = $inmate->when($request->count, function ($query) use ($request, $paginate) {
                return $query->count();
            });
        }

        return response()->json(['data' => $inmate]);
    }

    /**
     * The search function in this PHP code filters inmates based on name, date, and record type, and
     * returns the results paginated.
     *
     * @param Request request The  parameter is an instance of the Request class, which
     * represents the current HTTP request. It contains all the data sent with the request, including
     * query parameters, form data, and more.
     *
     * @return a paginated list of inmates that match the search criteria.
     */
    public function search(Request $request)
    {
        $today    = Carbon::today();
        $inmate   = new Inmate;
        $paginate = $request->paginate ? $request->paginate : 20;

        //return response()->json(['data'=>$request->prisoner_no, 422]);
        //// Search by name keyword, search surname nad first name
        // $inmate = $inmate->when($request->name_or_prisoner_no_filter, function ($query) use ($request) {
        //     return $query->where(function ($query) use ($request) {
        //         $query->where('surname','LIKE','%'.$request->name_or_prisoner_no_filter.'%')
        //               ->orWhere('first_name','LIKE','%'.$request->name_or_prisoner_no_filter.'%')
        //               ->orWhere('prisoner_no','LIKE','%'.$request->name_or_prisoner_no_filter.'%');
        //     });
        // });

        $inmate = $inmate->when($request->name_or_prisoner_no_filter, function ($query, $filterValue) {
            $searchTerms = array_filter(explode(' ', $filterValue)); // Split the search string by spaces and remove empty strings

            if (count($searchTerms) > 1) {
                foreach ($searchTerms as $term) {
                    $query->where(function ($q) use ($term) {
                        $q->where('surname', 'LIKE', '%' . $term . '%')
                            ->orWhere('first_name', 'LIKE', '%' . $term . '%');
                    });
                }
            } else {
                $query->where('surname', 'LIKE', '%' . $filterValue . '%')
                    ->orWhere('first_name', 'LIKE', '%' . $filterValue . '%')
                    ->orWhere('prisoner_no', 'LIKE', '%' . $filterValue . '%');
            }
            return $query;
        });

        //// Search by date filter, search today and later
        $inmate = $inmate->when($request->date_filter && $request->date_filter != '', function ($query) use ($request, $today) {
            return $request->date_filter == 'today' ? $query->whereDate('created_at', $today) : $query->whereDate('created_at', '<', $today);
        });

        //// Search by record filter, search for release_discharge or transfer
        $inmate = $inmate->when($request->record_filter && $request->record_filter != '', function ($query) use ($request, $today) {
            $request->record_filter == 'transferred' ? $query = $query->whereNotNull('who_authorised_transfer') :
            $query->whereNotNull('inmate_status')->whereNotNull('who_authorised_release_or_discharge');

            //$request->record_filter=='release_discharge' && $query = $query->whereNotNull('inmate_status')->whereNotNull('who_authorised_release_or_discharge');

            return $query;
        });

        //// Search by inmate filter, search for awaiting trial or convict
        $inmate = $inmate->when($request->inmate_status_filter && $request->inmate_status_filter != '0', function ($query) use ($request, $today) {
            return $query->where('inmate_status', $request->inmate_status_filter)->whereNull('who_authorised_release_or_discharge')->whereNull('who_authorised_transfer');
        });

        //// Search by range of dates to filter out date of arrival into the custodial center
        $inmate = $inmate->when($request->start_date && $request->start_date != '', function ($query) use ($request, $today) {
            return $request->end_date && $request->end_date != '' ? $query->whereBetween('date_of_arrival_in_current_prison', [$request->start_date, $request->end_date]) : $query->where('date_of_arrival_in_current_prison', $request->start_date);
            //return $query->where('date_of_arrival_in_current_prison',$request->start_date);
        });

        /////// No include query string for custom query
        $noIncludeArray = ['name_or_prisoner_no_filter', 'date_filter', 'record_filter', 'page', 'include', 'paginate', 'inmate_status_filter', 'start_date', 'end_date'];
        $query          = $request->query();
        //// This method will do a custom query based on query string to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

        $inmate        = $inmate->where($filteredQuery)->orderBy('id', 'desc')->paginate($paginate);
        $relations     = ['awaiting', 'sittings', 'convicted', 'warrants', 'legal_rep'];
        $includeString = explode(',', $request->include);

        if ($request->include) {
            foreach ($includeString as $string) {
                $checkRelationshipArray = $this->shouldIncludeRelation($relations, $string);
                if ($checkRelationshipArray) {
                    $inmate->load($string);
                }
            }
        }
        if ($request->count) {
            $inmate = $inmate->total();
        }

        return response()->json(['data' => $inmate]);
    }

    /**
     * The function performs a custom query on the Inmate model in PHP, filtering out certain query
     * parameters and returning the result.
     *
     * @param Request request The `` parameter is an instance of the `Illuminate\Http\Request`
     * class. It represents the current HTTP request made to the server and contains information such
     * as the request method, headers, query parameters, and request body.
     *
     * @return the result of the query on the Inmate model.
     */

    // Inmates who have never had any court sittings, whether they came in on remand or not.
    // public function onRemand(Request $request)
    // {
    //     /*
    //     /// Do a custom query on Inmate model,
    //     */
    //     $noIncludeArray = ['count', 'page', 'include', 'paginate'];

    //     $year       = $request->year;
    //     $periodType = $request->period_type;

    //     unset($request->year);
    //     unset($request->period_type);

    //     $query = $request->query();
    //     // This method will do a custom query to filter out the count value
    //     $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);
    //     //isset($query['count']) ? $inmate = Inmate::doesntHave("awaiting")->doesntHave("convicted")->where($filteredQuery)->count() : $inmate = Inmate::doesntHave("awaiting")->doesntHave("convicted")->where($filteredQuery)->paginate();

    //     $inmate = Inmate::doesntHave("awaiting")->doesntHave("convicted")->whereNull('who_authorised_release_or_discharge')->whereNull('who_authorised_transfer')->orderBy('id', 'desc')->where($filteredQuery)->when($request->paginate, function ($query) use ($request) {
    //         return $query->paginate($request->paginate);
    //     })->when($request->count, function ($query) use ($request) {
    //         return $query->whereYear('created_at', $year)->count();
    //     });

    //     return response()->json(["data" => $inmate, 201]);
    // }

    public function onRemand(Request $request)
    {
        $noIncludeArray = ['count', 'page', 'include', 'paginate'];

        // Store year and period_type before unsetting
        $year       = $request->year;
        $periodType = $request->period_type;

        // Remove from request to prevent interference with filteredQuery
        $request->query->remove('year');
        $request->query->remove('period_type');

        $query         = $request->query();
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

        $inmate = Inmate::doesntHave("awaiting")
            ->doesntHave("convicted")
            ->whereNull('who_authorised_release_or_discharge')
            ->whereNull('who_authorised_transfer')
            ->orderBy('id', 'desc')
            ->where($filteredQuery)
            ->when($request->paginate, function ($query) use ($request) {
                return $query->paginate($request->paginate);
            })
            ->when($request->count, function ($query) use ($year, $periodType) {
                // $query = $query->when($year, function ($q) use ($year) {
                //     return $q->whereYear('created_at', $year);
                // });
                return $query->count();
            });

        return response()->json(["data" => $inmate], 200);
    }

    ///////////Verify or unverify selected inmates
    // public function verifySelectedInmates(Request $request)
    // {
    //     $selectedInmates = explode(",", $request->selectedInmates);
    //     $action          = $request->action; // 'verify' or 'unverify'

    //     foreach ($selectedInmates as $value) {
    //         $inmate = Inmate::where('prisoner_no', $value)->first();

    //         if ($action === 'unverify') {
    //             $inmate->vetted   = false;
    //             $inmate->vet_date = null; // Clear verification date
    //         } else {
    //             $inmate->vetted   = true;
    //             $inmate->vet_date = Carbon::now();
    //         }

    //         $inmate->save();
    //     }

    //     return response()->json([
    //         "data" => $action === 'unverify'
    //         ? "Inmate records unverified"
    //         : "Inmate records verified",
    //     ], 201);
    // }

    public function verifySelectedInmates(Request $request)
    {
        $selectedInmates = explode(",", $request->selectedInmates);
        $action          = $request->action; // 'verify' or 'unverify'

        // Clean up prisoner numbers
        $prisonerNumbers = array_map('trim', $selectedInmates);

        // Define tables that need to be updated (those with vetted and vet_date columns)
        $tables = [
            'inmates_all_class_register',
            'inmates_atp_register',
            'inmates_convict_register',
            'inmates_sittings',
            'warrants',
            'inmates_sittings',
            'legal_aid_firms',
            'inmate_legal_representative',
            'legal_representatives',
            'legal_rep_defaulters',
        ];

        try {
            DB::beginTransaction();

            // Prepare update data based on action
            if ($action === 'unverify') {
                $updateData = [
                    'vetted'     => false,
                    'vet_date'   => null,
                    'updated_at' => Carbon::now(),
                ];
            } else {
                $updateData = [
                    'vetted'     => true,
                    'vet_date'   => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ];
            }

            $totalUpdated = 0;

            // Update each table
            foreach ($tables as $table) {
                // Check if table exists and has the required columns
                if ($this->tableHasVettedColumns($table)) {
                    $updated = DB::table($table)
                        ->whereIn('prisoner_no', $prisonerNumbers)
                        ->update($updateData);

                    $totalUpdated += $updated;
                }
            }

            DB::commit();

            return response()->json([
                "data"          => $action === 'unverify'
                ? "Successfully unverified {$totalUpdated} records across all tables"
                : "Successfully verified {$totalUpdated} records across all tables",
                "total_updated" => $totalUpdated,
            ], 200);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                "error"   => "Failed to " . $action . " inmate records",
                "message" => $e->getMessage(),
            ], 500);
        }
    }

/**
 * Check if table exists and has the required vetted columns
 */
    protected function tableHasVettedColumns(string $table): bool
    {
        try {
            // Check if table exists
            if (! Schema::hasTable($table)) {
                return false;
            }

            // Check if table has both vetted and vet_date columns
            return Schema::hasColumns($table, ['vetted', 'vet_date', 'prisoner_no']);

        } catch (\Exception $e) {
            return false;
        }
    }

    /////// Inmates who came in on remand
    public function cameInOnRemand(Request $request)
    {

        $year       = $request->year;
        $periodType = $request->period_type;

// Remove from request to prevent interference with filteredQuery
        $request->query->remove('year');
        $request->query->remove('period_type');

        /*
        /// Do a custom query on Inmate model,
        */
        $noIncludeArray = ['count', 'page', 'include', 'paginate'];
        $query          = $request->query();
        //// This method will do a custom query to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);
        $inmate        = Inmate::where('is_inmate_on_remand_on_arrival', '1')->whereNull('who_authorised_release_or_discharge')->whereNull('who_authorised_transfer')->orderBy('id', 'desc')->where($filteredQuery)->when($request->paginate, function ($query) use ($request) {
            return $query->paginate($request->paginate);
        })->when($request->count, function ($query) use ($request) {
            return $query->count();
        });

        return response()->json(["data" => $inmate, 201]);
    }

    /////// Current Inmate who came in on remand and have not yet been taking to court
    public function cameInOnRemandButNoCourtSchedule(Request $request)
    {

        $year       = $request->year;
        $periodType = $request->period_type;

        // Remove from request to prevent interference with filteredQuery
        $request->query->remove('year');
        $request->query->remove('period_type');

        /*
        /// Do a custom query on Inmate model,
        */
        $noIncludeArray = ['count', 'page', 'include', 'paginate'];
        $query          = $request->query();
        //// This method will do a custom query to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);
        $inmate        = Inmate::where('is_inmate_on_remand_on_arrival', '1')->doesntHave("awaiting")->doesntHave("convicted")->whereNull('who_authorised_release_or_discharge')->whereNull('who_authorised_transfer')->orderBy('id', 'desc')->where($filteredQuery)->when($request->paginate, function ($query) use ($request) {
            return $query->paginate($request->paginate);
        })->when($request->count, function ($query) use ($request) {
            return $query->count();
        });

        return response()->json(["data" => $inmate, 201]);
    }

    /**
     * The function "transferred" in PHP performs a custom query on the Inmate model to filter and
     * retrieve transferred inmates based on the request parameters, and returns the result.
     *
     * @param Request request The  parameter is an instance of the Request class, which
     * represents an HTTP request. It contains information about the request, such as the request
     * method, headers, and query parameters. In this code, the  parameter is used to access
     * the query parameters of the request.
     *
     * @return the result of the query on the Inmate model.
     */
    public function transferred(Request $request)
    {
        /*
        /// Do a custom query on Inmate model,
        */

        $year       = $request->year;
        $periodType = $request->period_type;

// Remove from request to prevent interference with filteredQuery
        $request->query->remove('year');
        $request->query->remove('period_type');

        $noIncludeArray = ['count', 'page', 'include', 'paginate'];
        $query          = $request->query();
        //// This method will do a custom query to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);
        //isset($query['count']) ? $inmate = Inmate::whereNotNull('transferred')->where($filteredQuery)->count() : $inmate = Inmate::whereNotNull('transferred')->whereNotNull('transfer_to')->where($filteredQuery)->paginate();
        $inmate = Inmate::whereNull('who_authorised_release_or_discharge')->whereNotNull('who_authorised_transfer')->whereNotNull('inmate_status')->orderBy('id', 'desc')->where($filteredQuery)->when($request->paginate, function ($query) use ($request) {
            return $query->paginate($request->paginate);
        })->when($request->count, function ($query) use ($request) {
            return $query->count();
        });
        return response()->json(["data" => $inmate, 201]);
    }

    /**
     * The function "releasedOrDischarged" performs a custom query on the Inmate model in PHP,
     * filtering out certain query parameters and returning the filtered results.
     *
     * @param Request request The  parameter is an instance of the Request class, which
     * contains the data and information about the current HTTP request. It is used to retrieve query
     * parameters and perform filtering on the Inmate model.
     *
     * @return the query result of the Inmate model.
     */
    public function releasedOrDischarged(Request $request)
    {
        /*
        /// Do a custom query on Inmate model,
        */

        $year       = $request->year;
        $periodType = $request->period_type;

// Remove from request to prevent interference with filteredQuery
        $request->query->remove('year');
        $request->query->remove('period_type');

        $noIncludeArray = ['count', 'page', 'include', 'paginate'];
        $query          = $request->query();
        //// This method will do a custom query to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);
        $inmate        = Inmate::whereNotNull('who_authorised_release_or_discharge')->whereNull('who_authorised_transfer')->whereNotNull('inmate_status')->orderBy('id', 'desc')->where($filteredQuery)->when($request->paginate, function ($query) use ($request) {
            return $query->paginate($request->paginate);
        })->when($request->count, function ($query) use ($request) {
            return $query->count();
        });
        return response()->json(["data" => $inmate, 201]);
    }

    public function unHealthyInmates(Request $request)
    {
        /*
        /// Do a custom query on Inmate model,
        */
        $noIncludeArray = ['count', 'page', 'include', 'paginate'];
        $query          = $request->query();
        //// This method will do a custom query to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);
        $inmate        = Inmate::whereNot('health_status', 'healthy')->whereNotNull('inmate_status')->orderBy('id', 'desc')->where($filteredQuery)->when($request->paginate, function ($query) use ($request) {
            return $query->paginate($request->paginate);
        })->when($request->count, function ($query) use ($request) {
            return $query->count();
        });
        return response()->json(["data" => $inmate, 201]);
    }

    /**
     * The function `lifers` in PHP filters and retrieves inmates who have been sentenced to life
     * imprisonment and have not been release or transfer.
     *
     * @param Request request The  parameter is an instance of the Request class, which
     * represents an HTTP request. It contains information about the request, such as the request
     * method, headers, and query parameters. In this case, it is used to retrieve the query parameters
     * passed in the request.
     *
     * @return the query result of the Inmate model.
     */
    public function lifers(Request $request)
    {
        /*
        /// Do a custom query on Inmate model,
        */
        $noIncludeArray = ['count', 'page', 'include', 'paginate'];
        $year           = $request->year;
        $periodType     = $request->period_type;

        // Remove from request to prevent interference with filteredQuery
        $request->query->remove('year');
        $request->query->remove('period_type');
        $query = $request->query();

        //// This method will do a custom query to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);
        $inmate        = Inmate::whereHas('convicted', function ($join) {
            $join->where('sentence_passed', 'like', '%life%')->orWhere('other_sentence_passed', 'like', '%life%');
        })->whereNull('who_authorised_release_or_discharge')->whereNull('who_authorised_transfer')->whereNotNull('inmate_status')->orderBy('id', 'desc')->where($filteredQuery)->when($request->paginate, function ($query) use ($request) {
            return $query->paginate($request->paginate);
        })->when($request->count, function ($query) use ($request) {
            return $query->count();
        });

        return response()->json(["data" => $inmate]);
    }

    /**
     * The `probono` function in PHP performs a custom query on the Inmate model based on request
     * parameters, filters the results, includes specified relationships, and returns the data in JSON
     * format.
     *
     * @param Request request The `probono` function is a PHP function that takes a `Request` object as
     * a parameter. The function performs a custom query on the `Inmate` model based on the request
     * parameters.
     *
     * @return The `probono` function returns a JSON response containing the data of Inmate records
     * that do not have a legal representative. The function performs a custom query on the Inmate
     * model based on the request parameters. It filters the query based on the query string
     * parameters, includes specified relationships if requested, and can return the total count of
     * records if requested. The final response includes the paginated list of
     */
    public function probono(Request $request)
    {
        $noIncludeArray = ['count', 'page', 'include', 'paginate', 'search'];
        $relations      = ['awaiting', 'sittings', 'convicted', 'legal_rep'];
        $includeString  = explode(',', $request->include);
        $query          = $request->query();
        $paginate       = $request->paginate ?: 20;

        // Get filtered query excluding special parameters
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

        $probono_list = Inmate::whereDoesntHave('legal_rep')
            ->when($request->has('search') && $request->search, function ($query) use ($request) {
                $searchTerm = $request->search;
                $query->where(function ($q) use ($searchTerm) {
                    // Search by prisoner number (exact match)
                    $q->where('prisoner_no', 'LIKE', '%' . $searchTerm . '%')
                    // Search by first name + last name in any order
                        ->orWhereRaw("CONCAT(first_name, ' ', surname) LIKE ?", ['%' . $searchTerm . '%'])
                        ->orWhereRaw("CONCAT(surname, ' ', first_name) LIKE ?", ['%' . $searchTerm . '%'])
                    // Individual name searches
                        ->orWhere('first_name', 'LIKE', '%' . $searchTerm . '%')
                        ->orWhere('surname', 'LIKE', '%' . $searchTerm . '%');
                });
            })
            ->where($filteredQuery)
            ->orderBy('created_at', 'desc')
            ->paginate($paginate);

        // Load relationships if requested
        if ($request->include) {
            foreach ($includeString as $string) {
                $checkRelationshipArray = $this->shouldIncludeRelation($relations, $string);
                if ($checkRelationshipArray) {
                    $probono_list->load($string);
                }
            }
        }

        // Prepare response
        $response = ['data' => $probono_list];

        // Add count if requested
        if ($request->count) {
            $response['count'] = $probono_list->total();
        }

        return response()->json($response);
    }

    /**
     * The function `non_probono` in PHP performs a custom query on the Inmate model based on request
     * parameters and includes specified relationships if requested.
     *
     * @param Request request The `non_probono` function is a controller method that handles a custom
     * query on the `Inmate` model based on the request parameters. Let's break down the parameters
     * used in this function:
     *
     * @return The function `non_probono` is returning a JSON response with the data of the filtered
     * and paginated list of inmates based on the custom query parameters provided in the request. The
     * response includes the list of inmates that have a legal representative, filtered based on the
     * query parameters, and optionally includes related data based on the `include` query parameter.
     * If the `count` query parameter is present,
     */
    public function non_probono(Request $request)
    {
        $noIncludeArray = ['count', 'page', 'include', 'paginate', 'search', 'legal_rep'];
        $relations      = ['awaiting', 'sittings', 'convicted', 'legal_rep'];
        $includeString  = explode(',', $request->include);
        $paginate       = $request->paginate ?: 20;

        // Build base query
        $query = Inmate::whereHas('legal_rep', function ($q) {
            $q->where('inmate_legal_representative.status', 1); // Only active assignments
        });

        // Add search functionality
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                // Search by prisoner number (exact match)
                $q->where('prisoner_no', 'LIKE', '%' . $searchTerm . '%')
                // Search by first name + last name in any order
                    ->orWhereRaw("CONCAT(first_name, ' ', surname) LIKE ?", ['%' . $searchTerm . '%'])
                    ->orWhereRaw("CONCAT(surname, ' ', first_name) LIKE ?", ['%' . $searchTerm . '%'])
                // Individual name searches
                    ->orWhere('first_name', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('surname', 'LIKE', '%' . $searchTerm . '%');
            });
        }

        // Apply other filters (excluding special parameters)
        $filterable = $request->except($noIncludeArray);
        foreach ($filterable as $field => $value) {
            $query->where($field, $value);
        }

        // Execute query
        $results = $query->with($relations)
            ->orderBy('created_at', 'desc')
            ->paginate($paginate);

        return response()->json([
            'data'  => $results,
            'count' => $request->count ? $results->total() : null,
        ]);
    }

    /**
     * The deathRoll function performs a custom query on the Inmate model to filter and retrieve
     * inmates who have a sentence passed as "condemned" and are not released or
     * transfer, with optional pagination and count functionality.
     *
     * @param Request request The  parameter is an instance of the Request class, which
     * represents an HTTP request made to the server. It contains information about the request, such
     * as the request method, URL, headers, and query parameters. In this case, the  object is
     * used to retrieve the query parameters passed in
     *
     * @return the query result of the Inmate model.
     */
    public function deathRoll(Request $request)
    {
        /*
        /// Do a custom query on Inmate model,
        */
        $noIncludeArray = ['count', 'page', 'include', 'paginate'];
        $year           = $request->year;
        $periodType     = $request->period_type;

// Remove from request to prevent interference with filteredQuery
        $request->query->remove('year');
        $request->query->remove('period_type');

        $query = $request->query();

        //// This method will do a custom query to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);
        $inmate        = Inmate::whereHas('convicted', function ($join) {
            $join->where('sentence_passed', 'like', '%death%')->orWhere('other_sentence_passed', 'like', '%death%');
        })->whereNull('who_authorised_release_or_discharge')->whereNull('who_authorised_transfer')->whereNotNull('inmate_status')->orderBy('id', 'desc')->where($filteredQuery)->when($request->paginate, function ($query) use ($request) {
            return $query->paginate($request->paginate);
        })->when($request->count, function ($query) use ($request) {
            return $query->count();
        });

        return response()->json(["data" => $inmate]);
    }

    /**
     * The function retrieves the most recent admissions of inmates within the past 30 days, based on
     * the provided query parameters.
     *
     * @param Request request The  parameter is an instance of the Request class, which
     * represents an HTTP request made to the server. It contains information about the request, such
     * as the request method, headers, and query parameters. In this case, the method is used to
     * retrieve the query parameters from the request.
     *
     * @return the query result for the "inmate" table, filtered by the date of arrival in the current
     * prison within the last 30 days. The query is also filtered based on the request parameters,
     * excluding the "count", "page", "include", and "paginate" parameters. If the "paginate" parameter
     * is present in the request, the query will be paginated based on
     */
    public function mostRecentAdmission(Request $request)
    {
        $noIncludeArray = ['count', 'page', 'include', 'paginate'];
        $query          = $request->query();
        //// This method will do a custom query to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);
        $today         = Carbon::now();
        $_30_days_ago  = Carbon::now()->subDays(30); ////// Get the records from 30 days ago
        $inmate        = Inmate::whereBetween('date_of_arrival_in_current_prison', [$_30_days_ago, $today])->orderBy('id', 'desc')->where($filteredQuery)->when($request->paginate, function ($query) use ($request) {
            return $query->paginate($request->paginate);
        })->when($request->count, function ($query) use ($request) {
            return $query->count();
        });

        return response()->json(["data" => $inmate]);
    }

    /**
     * The function retrieves the most recent release, discharge, or transfer records of inmates within
     * the past 365 days, based on the provided query parameters.
     *
     * @param Request request The  parameter is an instance of the Request class, which
     * represents an HTTP request. It contains information about the request, such as the query
     * parameters, headers, and request body. In this code, the  object is used to retrieve the
     * query parameters using the `query()` method.
     *
     * @return a query builder object.
     */
    public function mostRecentReleaseOrDischargeOrTransfer(Request $request)
    {
        $noIncludeArray = ['count', 'page', 'include', 'paginate'];
        $query          = $request->query();
        //// This method will do a custom query to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);
        $today         = Carbon::now();
        $days_ago      = Carbon::now()->subDays(365); ////// Get the records from 30 days ago
        $inmate        = Inmate::whereBetween('date_released_or_discharged', [$days_ago, $today])->orWhereBetween('date_transferred', [$days_ago, $today])->orderBy('id', 'desc')->where($filteredQuery)->when($request->paginate, function ($query) use ($request) {
            return $query->paginate($request->paginate);
        })->when($request->count, function ($query) use ($request) {
            return $query->count();
        });
        return response()->json(["data" => $inmate]);
    }

    /**
     * The function `durationClassification` retrieves the count of inmates based on their date of
     * arrival in the current prison within different time ranges.
     *
     * @param Request request The  parameter is an instance of the Request class, which
     * represents an HTTP request. It contains information about the request, such as the query
     * parameters, headers, and request body. In this code, the ->query() method is used to
     * retrieve the query parameters from the request.
     *
     * @return the result of the query, which includes the count of records that fall within the
     * specified date ranges. The result includes the count of records within the last 90 days, the
     * count of records within the last 180 days, and the count of records that are older than 270
     * days.
     */
    public function durationClassification(Request $request)
    {
        $noIncludeArray = ['count', 'page', 'include', 'paginate'];
        $query          = $request->query();
        //// This method will do a custom query to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);
        $today         = Carbon::today()->format('Y-m-d');
        $_90_days_ago  = Carbon::today()->subDays(90)->format('Y-m-d'); ////// Get the records from 90 days ago
        $_180_days_ago = Carbon::now()->subDays(180)->format('Y-m-d');  ////// Get the records from 180 days ago
        $_270_days_ago = Carbon::now()->subDays(270)->format('Y-m-d');  ////// Get the records from 270 days ago

        $inmate = $results = DB::table('inmates_all_class_register')
            ->select(
                DB::raw('COUNT(CASE WHEN date_of_arrival_in_current_prison BETWEEN ? AND ? THEN 1 END) AS _90_days'),
                DB::raw('COUNT(CASE WHEN date_of_arrival_in_current_prison BETWEEN ? AND ? THEN 1 END) AS _180_days'),
                DB::raw('COUNT(CASE WHEN date_of_arrival_in_current_prison < ? THEN 1 END) AS _270_days')
            )
            ->whereNull('who_authorised_release_or_discharge')
            ->whereNull('who_authorised_transfer')
            ->setBindings([$_90_days_ago, $today, $_180_days_ago, $_90_days_ago, $_270_days_ago]) // Bind the date values
            ->first();                                                                            // Retrieve the first result

        return response()->json(['data' => $inmate], 201);
    }

    public function acja(Request $request)
    {
        $noIncludeArray = ['count', 'page', 'include', 'paginate', 'duration', 'search'];
        $relations      = ['awaiting', 'sittings', 'convicted'];
        $includeString  = explode(',', $request->include);
        $query          = $request->query();
        $paginate       = $request->paginate ? $request->paginate : 20;

        // Filter custom query
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

        // Base query
        $inmateQuery = Inmate::query();

        // Handle search parameter (name or prisoner_no)
        if ($request->has('search') && ! empty($request->search)) {
            $searchTerm = $request->search;

            $inmateQuery->where(function ($q) use ($searchTerm) {
                $q->where('prisoner_no', 'like', "%{$searchTerm}%")
                    ->orWhere(function ($q) use ($searchTerm) {
                        // Split search term into parts for name search
                        $nameParts = preg_split('/\s+/', $searchTerm);

                        // Handle different name combinations
                        if (count($nameParts) === 1) {
                            // Single term - search in both first and last name
                            $q->where('first_name', 'like', "%{$nameParts[0]}%")
                                ->orWhere('surname', 'like', "%{$nameParts[0]}%");
                        } else {
                            // Multiple terms - search for combinations
                            $q->where(function ($q) use ($nameParts) {
                                $q->where('first_name', 'like', "%{$nameParts[0]}%")
                                    ->where('surname', 'like', "%{$nameParts[1]}%");
                            })->orWhere(function ($q) use ($nameParts) {
                                $q->where('first_name', 'like', "%{$nameParts[1]}%")
                                    ->where('surname', 'like', "%{$nameParts[0]}%");
                            });
                        }
                    });
            });
        }

        // Handle duration parameter
        if ($request->has('duration') && is_numeric($request->duration)) {
            $duration  = (int) $request->duration;
            $startDate = Carbon::now()->subDays($duration)->format('Y-m-d');
            $endDate   = Carbon::today()->format('Y-m-d');

            // Get inmates whose arrival date is NOT within the duration window
            $inmateQuery->whereNotBetween('date_of_arrival_in_current_prison', [$startDate, $endDate]);
        } else {
            // Default to 90 days if no duration specified
            $startDate = Carbon::now()->subDays(90)->format('Y-m-d');
            $endDate   = Carbon::today()->format('Y-m-d');
            $inmateQuery->whereNotBetween('date_of_arrival_in_current_prison', [$startDate, $endDate]);
        }

        // Apply filtered query and ordering
        $inmateQuery->where($filteredQuery)
            ->orderBy('id', 'desc');

        if ($request->include) {
            $inmateQuery->with(array_filter($includeString, function ($relation) use ($relations) {
                return $this->shouldIncludeRelation($relations, $relation);
            }));
        }

        $inmate = $inmateQuery->paginate($paginate);

        if ($request->count) {
            $inmate = $inmate->total();
        }

        return response()->json(['data' => $inmate], 200);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    /**
     * The store function in this PHP code handles the storage of a file and the creation of a new
     * inmate record.
     *
     * @param Request request The  parameter is an instance of the Request class, which
     * represents an HTTP request. It contains all the data and information about the request, such as
     * the request method, headers, and input data.
     *
     * @return a JSON response with a message indicating that an inmate record has been created and the
     * ID of the last saved inmate record.
     */
    public function store(Request $request)
    {
        Gate::authorize('create', Inmate::class);

        if ($request->hasFile('file')) {
            //return $request->file('file');
            $inmate    = Inmate::find($request->inmate)->load('warrants');
            $warrant   = new Warrant;
            $rand      = rand(1000, 100000);
            $file_name = 'warrant/' . str_replace('/', '!', $inmate->prisoner_no) . "_" . $rand . '.pdf';
            Storage::disk('public')->put($file_name, file_get_contents($request->file));
            //$inmate->warrant = $file_name;
            //$inmate->save();
            $warrant->path        = $file_name;
            $warrant->prisoner_no = $inmate->prisoner_no;
            $warrant->save();
            return $inmate;
        }

        $request->validate([
            'prisoner_no'                       => 'required||max:20',
            'surname'                           => 'required',
            'first_name'                        => 'required',
            'age_on_admission'                  => 'required||integer',
            'date_of_birth'                     => 'required',
            'date_of_arrival_in_current_prison' => 'required',
            'description'                       => 'required',
            'nationality'                       => 'required',

        ]);
        $newInmate = new Inmate($request->all());
        $newInmate->save();
        //return $request->all();
        //Inmate::create($request->all());
        return response()->json(['message' => 'Inmate record created', 'last_saved_id' => $newInmate->id]);
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    /**
     * The show function loads the "awaiting", "sittings", and "convicted" relationships for a given
     * Inmate object.
     *
     * @param Inmate inmate The "Inmate" parameter is a reference to an instance of the "Inmate" class.
     * It is used to retrieve information about a specific inmate.
     *
     * @return the loaded relationships of the given Inmate model. The relationships being loaded are
     * 'awaiting', 'sittings', and 'convicted'.
     */
    public function show(Inmate $inmate)
    {
        Gate::authorize('view', $inmate);

        return $inmate->load('awaiting', 'sittings', 'convicted', 'warrants');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    /**
     * The function updates an inmate's record based on the provided request data.
     *
     * @param Request request The  parameter is an instance of the Request class, which
     * contains all the data sent in the HTTP request.
     * @param Inmate inmate The  parameter is an instance of the Inmate model. It represents the
     * inmate record that is being updated.
     *
     * @return a JSON response with a message indicating that the inmate record has been updated, along
     * with the last saved ID of the inmate.
     */
    public function update(Request $request, Inmate $inmate)
    {
        Gate::authorize('update', $inmate);

        if ($request->detention_status) {
            ////////// IF THE INMATE IS AN AWAITING TRIAL
            if ($request->detention_status == 'awaiting') {

                $validateData = $request->validate([
                    'prosecuting_agency.*'   => 'required',
                    'charge_no_suit_no.*'    => 'required',
                    'court_of_arraignment.*' => 'required',
                    'arraignment_date.*'     => 'required',
                    'offence.*'              => 'required',
                    'section.*'              => 'required',

                ]);
                //return response()->json(['message' => $request->all()]);

                foreach ($request->charge_no_suit_no as $index => $date) {
                    $atp_id = $request->atp_id ? $request->atp_id[$index] : null;
                    ///////////CHECK
                    // If this inmate have an already exisiting awaiting trial record with same offence and section
                    ///////////
                    $awaiting = ATP::where('id', $atp_id)->first();
                    if (! $awaiting) {
                        $awaiting              = new ATP;
                        $awaiting->prisoner_no = $inmate->prisoner_no;
                        $awaiting->state_id    = $inmate->state_id;
                        $awaiting->prison_id   = $inmate->prison_id;
                        //$awaiting->status = '1';
                    }
                    $awaiting->prosecuting_agency                       = $request->prosecuting_agency[$index];
                    $awaiting->charge_no_suit_no                        = $request->charge_no_suit_no[$index];
                    $awaiting->court_of_arraignment                     = $request->court_of_arraignment[$index];
                    $awaiting->arraignment_date                         = $request->arraignment_date[$index];
                    $awaiting->offence                                  = $request->offence[$index];
                    $awaiting->section                                  = $request->section[$index];
                    $inmate->inmate_status                              = $request->inmate_status;
                    $request->register_stage && $inmate->register_stage = $request->register_stage;
                    $inmate->vetted                                     = 0;
                    $inmate->save();
                    $awaiting->save();

                    //return $awaiting;
                }
                return response()->json(['message' => 'Inmate record updated', 'last_saved_id' => $inmate->id]);
            }

            ///// IF THE INMATE IS A CONVICT
            if ($request->detention_status == 'convicted') {

                $request->validate([
                    'prosecuting_agency.*'   => 'required',
                    'charge_no_suit_no.*'    => 'required',
                    'court_of_arraignment.*' => 'required',
                    'arraignment_date.*'     => 'required',
                    'offence.*'              => 'required',
                    'section.*'              => 'required',
                    'sentence_type.*'        => 'required',
                    'sentence_passed.*'      => 'required',
                    'date_convicted.*'       => 'required',
                ]);

                //return response()->json(['message' => $request]);
                foreach ($request->charge_no_suit_no as $index => $date) {
                    $convict_id = $request->convict_id ? $request->convict_id[$index] : null;
                    $convict    = Convict::where('id', $convict_id)->first();
                    if (! $convict) {
                        $convict              = new Convict;
                        $convict->prisoner_no = $inmate->prisoner_no;
                        $convict->state_id    = $inmate->state_id;
                        $convict->prison_id   = $inmate->prison_id;
                    }

                    $convict->charge_no_suit_no        = $request->charge_no_suit_no[$index];
                    $convict->prosecuting_agency       = $request->prosecuting_agency[$index];
                    $convict->court_of_arraignment     = $request->court_of_arraignment[$index];
                    $convict->arraignment_date         = $request->arraignment_date[$index];
                    $convict->date_convicted           = $request->date_convicted[$index];
                    $convict->sentence_type            = $request->sentence_type[$index];
                    $convict->sentence_passed          = $request->sentence_passed[$index];
                    $convict->earliest_date_of_release = $request->earliest_date_of_release[$index];
                    $convict->latest_date_of_release   = $request->latest_date_of_release[$index];
                    $convict->actual_date_of_release   = $request->actual_date_of_release[$index];
                    $convict->appeal                   = $request->appeal[$index];
                    $convict->court_of_appeal          = $request->court_of_appeal[$index];
                    $convict->offence                  = $request->offence[$index];
                    $convict->section                  = $request->section[$index];
                    $convict->duration_in_days         = $request->duration_in_days[$index];
                    $inmate->inmate_status             = $request->inmate_status;
                    $inmate->vetted                    = 0;
                    //$convict->status = '1';
                    $request->register_stage && $inmate->register_stage = $request->register_stage;
                    //$inmate->inmate_status='2';////  This set the inmate_status to Convict in all_class_register
                    $inmate->save();
                    $convict->save();
                }
                return response()->json(['message' => 'Inmate record updated', 'last_saved_id' => $inmate->id]);
                //return $convict;
            }
            ///// IF THE INMATE IS A OTHER DETENTION STATUS
            if ($request->detention_status == 'others') {
                $inmate->inmate_status                              = $request->inmate_status;
                $request->register_stage && $inmate->register_stage = $request->register_stage;
                $inmate->vetted                                     = 0;
                $inmate->save();
                return response()->json(['message' => 'Inmate record updated', 'last_saved_id' => $inmate->id]);
            }
        } else if ($request->capture_image) {
            $save_base64_image                                               = $this->save_base64_image($request, 'capture/', str_replace('/', '!', $inmate->prisoner_no) . "_" . $request->capture_image);
            $request->capture_image == 'front_image' && $inmate->front_image = $save_base64_image;
            $request->capture_image == 'left_image' && $inmate->left_image   = $save_base64_image;
            $request->capture_image == 'right_image' && $inmate->right_image = $save_base64_image;
            $request->register_stage && $inmate->register_stage              = $request->register_stage;
            $inmate->vetted                                                  = 0;
            $inmate->save();
            return response()->json(['message' => 'Inmate record updated', 'last_saved_id' => $inmate->id]);
        } else if ($request->surname) {
            $request->validate([
                'surname'                           => 'required',
                'first_name'                        => 'required',
                'age_on_admission'                  => 'required||integer',
                'date_of_birth'                     => 'required',
                'date_of_arrival_in_current_prison' => 'required',
                'description'                       => 'required',
                'nationality'                       => 'required',
                'register_stage'                    => 'required',
            ]);
            //$request->vetted=null;
            $updateInmate = $inmate->update($request->all());
            return response()->json(['message' => 'Inmate record updated', 'last_saved_id' => $inmate->id]);
            //return $request->all();
        } else {

            $inmate->update(
                $request->validate([
                    'date_of_arrival_in_current_prison' => [],
                    'previous_conviction'               => [],
                    'risk'                              => [],
                    'register_stage'                    => [],
                ])
            );

            return response()->json(['message' => 'Inmate record updated', 'last_saved_id' => $inmate->id]);
            //return $request->all();
        }
    }

    /**
     * Update biometric status for an inmate by ID
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateBiometrics(Request $request, $id)
    {
        // Find inmate by ID
        $inmate = Inmate::find($id);

        if (! $inmate) {
            return response()->json([
                'success' => false,
                'message' => 'Inmate not found',
            ], 404);
        }

        // Validate request
        $validated = $request->validate([
            'biometric' => 'required|in:0,1',
        ]);

        // Update the inmate record
        $inmate->update([
            'biometric'      => $validated['biometric'],
            'register_stage' => 5,
            'vetted'         => 0,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Biometric status updated successfully',
            'data'    => [
                'id'             => $inmate->id,
                'prisoner_no'    => $inmate->prisoner_no,
                'biometric'      => $inmate->biometric,
                'register_stage' => $inmate->register_stage,
            ],
        ]);
    }

    public function doBatchRelease(Request $request)
    {
        //return $request->all();
        for ($a = 0; $a < count($request->date_released_or_discharged); $a++) {
            $inmate = Inmate::where('prisoner_no', $request->prisoner_no[$a])->first();
            if ($inmate) {
                $date_released_or_discharged_initiated = Carbon::now();
                $request->validate([
                    'date_released_or_discharged.' . $a         => 'required',
                    'reason_for_release_or_discharge.' . $a     => 'required',
                    'release_or_discharge_remark.' . $a         => 'required',
                    'who_authorised_release_or_discharge.' . $a => 'required',
                    'release_type.' . $a                        => 'required',
                ]);
                $inmate->date_released_or_discharged           = $request->date_released_or_discharged[$a];
                $inmate->reason_for_release_or_discharge       = $request->reason_for_release_or_discharge[$a];
                $inmate->release_or_discharge_remark           = $request->release_or_discharge_remark[$a];
                $inmate->who_authorised_release_or_discharge   = $request->who_authorised_release_or_discharge[$a];
                $inmate->date_released_or_discharged_initiated = $date_released_or_discharged_initiated;
                $inmate->release_type                          = $request->release_type;
                $inmate->save();
            }
        }
        return response()->json(['message' => 'Inmate release record updated. Please refresh page']);
    }
    public function doBatchTransfer(Request $request)
    {
        for ($a = 0; $a < count($request->date_transferred); $a++) {
            $inmate = Inmate::where('prisoner_no', $request->prisoner_no[$a])->first();
            if ($inmate) {
                $request->validate([
                    'date_transferred.' . $a => 'required',
                    'transfer_to.' . $a      => 'required',
                    'departure_date.' . $a   => 'required',
                ]);

                $inmate->transferred             = 1;
                $inmate->date_transferred        = $request->date_transferred[$a];
                $inmate->transfer_to             = $request->transfer_to[$a];
                $inmate->departure_date          = $request->departure_date[$a];
                $inmate->other_transfer_location = $request->other_transfer_location[$a] ?? null;
                $inmate->estimated_return_date   = $request->estimated_return_date[$a] ?? null;
                $inmate->who_authorised_transfer = $request->who_authorised_transfer;

                $inmate->save();
            }
        }
        return response()->json(['message' => 'Inmate transfer record updated. Please refresh page']);
    }
    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    /**
     * The destroy function is used to delete a specific resource identified by its ID.
     *
     * @param id The "id" parameter is the unique identifier of the resource that you want to destroy
     * or delete. It is typically used to identify the specific record in a database table that you
     * want to remove.
     */
    public function destroy($id)
    {
        //
    }
}
