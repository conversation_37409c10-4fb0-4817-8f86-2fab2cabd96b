<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use App\Models\Staff;
use App\Observers\StaffObserver;
use App\Services\GateHelperService;
use Modules\AdminManagement\Entities\HqAdmin;
use App\Policies\TrainingProgramPolicy;
use App\Policies\InmatePolicy;
use App\Policies\ATPPolicy;
use App\Policies\ConvictPolicy;
use App\Policies\WarrantPolicy;
use App\Policies\BelongingPolicy;
use App\Policies\MedicalHistoryPolicy;
use App\Policies\MedicalAppointmentPolicy;
use Modules\TrainingManagement\Entities\TrainingProgram;
use Modules\IdentityManagement\Entities\Inmate;
use Modules\IdentityManagement\Entities\ATP;
use Modules\IdentityManagement\Entities\Convict;
use Modules\IdentityManagement\Entities\Warrant;
use Modules\BelongingManagement\Entities\Belonging;
use Modules\HealthManagement\Entities\MedicalHistory;
use Modules\HealthManagement\Entities\MedicalAppointments;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        TrainingProgram::class => TrainingProgramPolicy::class,
        Inmate::class => InmatePolicy::class,
        ATP::class => ATPPolicy::class,
        Convict::class => ConvictPolicy::class,
        Warrant::class => WarrantPolicy::class,
        Belonging::class => BelongingPolicy::class,
        MedicalHistory::class => MedicalHistoryPolicy::class,
        MedicalAppointments::class => MedicalAppointmentPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Register observers for cache invalidation
        Staff::observe(StaffObserver::class);

        // Register all gates using the simplified helper service
        GateHelperService::registerAllGates();
    }
}
                    'view-cloud-statistics',
                    'manage-all'
                ]) || $user->accountType?->hasDefaultPermission('view-cloud-statistics');
            }

            return false;
        });

        Gate::define('retry-cloud-uploads', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'retry-cloud-uploads',
                    'manage-all'
                ]) || $user->accountType?->hasDefaultPermission('retry-cloud-uploads');
            }

            return false;
        });

        Gate::define('verify-cloud-structure', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'verify-cloud-structure',
                    'manage-all'
                ]) || $user->accountType?->hasDefaultPermission('verify-cloud-structure');
            }

            return false;
        });

        Gate::define('edit-upload', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'edit-upload',
                    'manage-all'
                ]) || $user->accountType?->hasDefaultPermission('edit-upload');
            }

            return false;
        });

        Gate::define('sync-data', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'sync-data',
                    'manage-all'
                ]) || $user->accountType?->hasDefaultPermission('sync-data');
            }

            return false;
        });

        // Training Management Gates
        Gate::define('view-training-programs', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-training-programs',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('view-training-programs');
            }

            return false;
        });

        Gate::define('create-training-programs', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'create-training-programs',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('create-training-programs');
            }

            return false;
        });

        Gate::define('update-training-programs', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'update-training-programs',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('update-training-programs');
            }

            return false;
        });

        Gate::define('delete-training-programs', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'delete-training-programs',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('delete-training-programs');
            }

            return false;
        });

        Gate::define('manage-training-enrollments', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-training-enrollments',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('manage-training-enrollments');
            }

            return false;
        });

        Gate::define('view-training-reports', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-training-reports',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('view-training-reports');
            }

            return false;
        });

        Gate::define('manage-all-training', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('manage-all-training') ||
                       $user->hasRole('super-admin') ||
                       $user->accountType?->hasDefaultPermission('manage-all-training');
            }

            return false;
        });

        // Training Enrollments Gates
        Gate::define('view-training-enrollments', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-training-enrollments',
                    'manage-training-enrollments',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('view-training-enrollments');
            }

            return false;
        });

        // Training Productivity Gates
        Gate::define('view-training-productivity', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-training-productivity',
                    'manage-training-productivity',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('view-training-productivity');
            }

            return false;
        });

        Gate::define('manage-training-productivity', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-training-productivity',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('manage-training-productivity');
            }

            return false;
        });

        // Training Production Gates
        Gate::define('view-training-production', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-training-production',
                    'manage-training-production',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('view-training-production');
            }

            return false;
        });

        Gate::define('manage-training-production', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-training-production',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('manage-training-production');
            }

            return false;
        });

        // Training Skills Gates
        Gate::define('view-training-skills', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-training-skills',
                    'manage-training-skills',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('view-training-skills');
            }

            return false;
        });

        Gate::define('manage-training-skills', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-training-skills',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('manage-training-skills');
            }

            return false;
        });

        // Training Jobs Gates
        Gate::define('view-training-jobs', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-training-jobs',
                    'manage-training-jobs',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('view-training-jobs');
            }

            return false;
        });

        Gate::define('manage-training-jobs', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-training-jobs',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('manage-training-jobs');
            }

            return false;
        });

        // Training Recidivism Gates
        Gate::define('view-training-recidivism', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-training-recidivism',
                    'manage-training-recidivism',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('view-training-recidivism');
            }

            return false;
        });

        Gate::define('manage-training-recidivism', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-training-recidivism',
                    'manage-training-programs',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('manage-training-recidivism');
            }

            return false;
        });

        // Belonging Management Gates
        Gate::define('view-belongings', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-belongings',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('view-belongings');
            }

            return false;
        });

        Gate::define('create-belongings', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'create-belongings',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('create-belongings');
            }

            return false;
        });

        Gate::define('update-belongings', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'update-belongings',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('update-belongings');
            }

            return false;
        });

        Gate::define('delete-belongings', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'delete-belongings',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('delete-belongings');
            }

            return false;
        });

        Gate::define('manage-cash-transactions', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-cash-transactions',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('manage-cash-transactions');
            }

            return false;
        });

        Gate::define('view-cash-statements', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-cash-statements',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('view-cash-statements');
            }

            return false;
        });

        Gate::define('retrieve-belongings', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'retrieve-belongings',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('retrieve-belongings');
            }

            return false;
        });

        Gate::define('dispose-belongings', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'dispose-belongings',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('dispose-belongings');
            }

            return false;
        });

        Gate::define('view-belonging-reports', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-belonging-reports',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('view-belonging-reports');
            }

            return false;
        });

        Gate::define('restore-belongings', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'restore-belongings',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('restore-belongings');
            }

            return false;
        });

        Gate::define('view-belongings-dashboard', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-belongings-dashboard',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('view-belongings-dashboard');
            }

            return false;
        });

        Gate::define('view-cash-balances', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-cash-balances',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('view-cash-balances');
            }

            return false;
        });

        Gate::define('force-delete-belongings', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'force-delete-belongings',
                    'manage-all-training'
                ]) || $user->accountType?->hasDefaultPermission('force-delete-belongings');
            }

            return false;
        });

        // Account Type Based Gates
        Gate::define('account-type-admin', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->accountType?->account_type === 'admin' ||
                       $user->accountType?->hierarchy_level >= 8;
            }

            return false;
        });

        Gate::define('account-type-supervisor', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->accountType?->account_type === 'supervisor' ||
                       $user->accountType?->hierarchy_level >= 6;
            }

            return false;
        });

        Gate::define('account-type-officer', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->accountType?->account_type === 'officer' ||
                       $user->accountType?->hierarchy_level >= 4;
            }

            return false;
        });

        // State and Prison Access Gates
        Gate::define('access-state', function ($user, int $stateId) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasStateAccess($stateId);
            }

            return false;
        });

        Gate::define('access-prison', function ($user, int $prisonId) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPrisonAccess($prisonId);
            }

            return false;
        });

        // General System Gates
        Gate::define('system-admin', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasRole('super-admin') ||
                       $user->hasRole('system-admin') ||
                       $user->accountType?->hierarchy_level >= 10;
            }

            return false;
        });

        Gate::define('state-admin', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyRole(['super-admin', 'system-admin', 'state-admin']) ||
                       $user->accountType?->hierarchy_level >= 8;
            }

            return false;
        });

        // RBAC Management Gates
        Gate::define('view-roles', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission(['system-admin', 'state-admin', 'prison-admin']) ||
                       $user->accountType?->hierarchy_level >= 6;
            }

            return false;
        });

        Gate::define('view-permissions', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission(['system-admin', 'state-admin', 'prison-admin']) ||
                       $user->accountType?->hierarchy_level >= 6;
            }

            return false;
        });

        Gate::define('view-account-types', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission(['system-admin', 'state-admin']) ||
                       $user->accountType?->hierarchy_level >= 8;
            }

            return false;
        });

        Gate::define('view-staff-permissions', function ($user, $targetStaff) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff && $targetStaff instanceof Staff) {
                return $user->hasPermission('system-admin') ||
                       ($user->hasPermission('state-admin') && $user->assigned_state === $targetStaff->assigned_state) ||
                       ($user->hasPermission('prison-admin') && $user->prison === $targetStaff->prison) ||
                       $user->id === $targetStaff->id;
            }

            return false;
        });

        Gate::define('assign-roles', function ($user, $targetStaff) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff && $targetStaff instanceof Staff) {
                return $user->hasPermission('system-admin') ||
                       ($user->hasPermission('state-admin') && $user->assigned_state === $targetStaff->assigned_state) ||
                       ($user->hasPermission('prison-admin') && $user->prison === $targetStaff->prison);
            }

            return false;
        });

        Gate::define('remove-roles', function ($user, $targetStaff) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff && $targetStaff instanceof Staff) {
                return $user->hasPermission('system-admin') ||
                       ($user->hasPermission('state-admin') && $user->assigned_state === $targetStaff->assigned_state) ||
                       ($user->hasPermission('prison-admin') && $user->prison === $targetStaff->prison);
            }

            return false;
        });

        Gate::define('manage-staff-roles', function ($user, $targetStaff) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff && $targetStaff instanceof Staff) {
                return $user->hasPermission('system-admin') ||
                       ($user->hasPermission('state-admin') && $user->assigned_state === $targetStaff->assigned_state);
            }

            return false;
        });

        Gate::define('view-available-roles', function ($user, $targetStaff) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff && $targetStaff instanceof Staff) {
                return $user->hasAnyPermission(['system-admin', 'state-admin', 'prison-admin']) ||
                       $user->id === $targetStaff->id;
            }

            return false;
        });

        Gate::define('check-staff-permissions', function ($user, $targetStaff) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff && $targetStaff instanceof Staff) {
                return $user->hasAnyPermission(['system-admin', 'state-admin', 'prison-admin']) ||
                       $user->id === $targetStaff->id;
            }

            return false;
        });

        Gate::define('manage-staff-permissions', function ($user, $targetStaff) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff && $targetStaff instanceof Staff) {
                return $user->hasPermission('system-admin') ||
                       ($user->hasPermission('state-admin') && $user->assigned_state === $targetStaff->assigned_state);
            }

            return false;
        });

        Gate::define('manage-account-types', function ($user, $targetStaff) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff && $targetStaff instanceof Staff) {
                return $user->hasPermission('system-admin') ||
                       ($user->hasPermission('state-admin') && $user->assigned_state === $targetStaff->assigned_state);
            }

            return false;
        });

        Gate::define('prison-admin', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyRole(['super-admin', 'system-admin', 'state-admin', 'prison-admin']) ||
                       $user->accountType?->hierarchy_level >= 6;
            }

            return false;
        });

        // Health Management Gates
        Gate::define('view-medical-history', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-medical-history',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('view-medical-history');
            }

            return false;
        });

        Gate::define('create-medical-history', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'create-medical-history',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('create-medical-history');
            }

            return false;
        });

        Gate::define('update-medical-history', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'update-medical-history',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('update-medical-history');
            }

            return false;
        });

        Gate::define('delete-medical-history', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'delete-medical-history',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('delete-medical-history');
            }

            return false;
        });

        Gate::define('view-medical-appointments', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-medical-appointments',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('view-medical-appointments');
            }

            return false;
        });

        Gate::define('create-medical-appointments', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'create-medical-appointments',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('create-medical-appointments');
            }

            return false;
        });

        Gate::define('update-medical-appointments', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'update-medical-appointments',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('update-medical-appointments');
            }

            return false;
        });

        Gate::define('delete-medical-appointments', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'delete-medical-appointments',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('delete-medical-appointments');
            }

            return false;
        });

        Gate::define('view-medical-prescriptions', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-medical-prescriptions',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('view-medical-prescriptions');
            }

            return false;
        });

        Gate::define('create-medical-prescriptions', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'create-medical-prescriptions',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('create-medical-prescriptions');
            }

            return false;
        });

        Gate::define('update-medical-prescriptions', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'update-medical-prescriptions',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('update-medical-prescriptions');
            }

            return false;
        });

        Gate::define('delete-medical-prescriptions', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'delete-medical-prescriptions',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('delete-medical-prescriptions');
            }

            return false;
        });

        Gate::define('view-medical-diagnostics', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-medical-diagnostics',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('view-medical-diagnostics');
            }

            return false;
        });

        Gate::define('create-medical-diagnostics', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'create-medical-diagnostics',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('create-medical-diagnostics');
            }

            return false;
        });

        Gate::define('update-medical-diagnostics', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'update-medical-diagnostics',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('update-medical-diagnostics');
            }

            return false;
        });

        Gate::define('delete-medical-diagnostics', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'delete-medical-diagnostics',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('delete-medical-diagnostics');
            }

            return false;
        });

        Gate::define('view-medical-lab-tests', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-medical-lab-tests',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('view-medical-lab-tests');
            }

            return false;
        });

        Gate::define('create-medical-lab-tests', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'create-medical-lab-tests',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('create-medical-lab-tests');
            }

            return false;
        });

        Gate::define('update-medical-lab-tests', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'update-medical-lab-tests',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('update-medical-lab-tests');
            }

            return false;
        });

        Gate::define('delete-medical-lab-tests', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'delete-medical-lab-tests',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('delete-medical-lab-tests');
            }

            return false;
        });

        Gate::define('view-health-reports', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-health-reports',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('view-health-reports');
            }

            return false;
        });

        Gate::define('view-health-dashboard', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-health-dashboard',
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('view-health-dashboard');
            }

            return false;
        });

        Gate::define('manage-all-health', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-all-health'
                ]) || $user->accountType?->hasDefaultPermission('manage-all-health');
            }

            return false;
        });

        // Legal Aid Management Gates
        Gate::define('view-legal-aid', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-legal-aid',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('view-legal-aid');
            }

            return false;
        });

        Gate::define('create-legal-aid', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'create-legal-aid',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('create-legal-aid');
            }

            return false;
        });

        Gate::define('update-legal-aid', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'update-legal-aid',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('update-legal-aid');
            }

            return false;
        });

        Gate::define('delete-legal-aid', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'delete-legal-aid',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('delete-legal-aid');
            }

            return false;
        });

        Gate::define('view-legal-representatives', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-legal-representatives',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('view-legal-representatives');
            }

            return false;
        });

        Gate::define('create-legal-representatives', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'create-legal-representatives',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('create-legal-representatives');
            }

            return false;
        });

        Gate::define('update-legal-representatives', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'update-legal-representatives',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('update-legal-representatives');
            }

            return false;
        });

        Gate::define('delete-legal-representatives', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'delete-legal-representatives',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('delete-legal-representatives');
            }

            return false;
        });

        Gate::define('view-legal-firms', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-legal-firms',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('view-legal-firms');
            }

            return false;
        });

        Gate::define('create-legal-firms', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'create-legal-firms',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('create-legal-firms');
            }

            return false;
        });

        Gate::define('update-legal-firms', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'update-legal-firms',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('update-legal-firms');
            }

            return false;
        });

        Gate::define('delete-legal-firms', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'delete-legal-firms',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('delete-legal-firms');
            }

            return false;
        });

        Gate::define('manage-legal-assignments', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-legal-assignments',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('manage-legal-assignments');
            }

            return false;
        });

        Gate::define('view-legal-defaulters', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-legal-defaulters',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('view-legal-defaulters');
            }

            return false;
        });

        Gate::define('manage-legal-defaulters', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-legal-defaulters',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('manage-legal-defaulters');
            }

            return false;
        });

        Gate::define('view-deprived-legal-rep', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-deprived-legal-rep',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('view-deprived-legal-rep');
            }

            return false;
        });

        Gate::define('manage-deprived-legal-rep', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-deprived-legal-rep',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('manage-deprived-legal-rep');
            }

            return false;
        });

        Gate::define('view-legal-aid-dashboard', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-legal-aid-dashboard',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('view-legal-aid-dashboard');
            }

            return false;
        });

        Gate::define('view-legal-aid-reports', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-legal-aid-reports',
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('view-legal-aid-reports');
            }

            return false;
        });

        Gate::define('manage-all-legal-aid', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-all-legal-aid'
                ]) || $user->accountType?->hasDefaultPermission('manage-all-legal-aid');
            }

            return false;
        });

        // Staff Management Gates
        Gate::define('view-staff', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-staff',
                    'manage-all-staff'
                ]) || $user->accountType?->hasDefaultPermission('view-staff');
            }

            return false;
        });

        Gate::define('create-staff', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'create-staff',
                    'manage-all-staff'
                ]) || $user->accountType?->hasDefaultPermission('create-staff');
            }

            return false;
        });

        Gate::define('update-staff', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'update-staff',
                    'manage-all-staff'
                ]) || $user->accountType?->hasDefaultPermission('update-staff');
            }

            return false;
        });

        Gate::define('delete-staff', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'delete-staff',
                    'manage-all-staff'
                ]) || $user->accountType?->hasDefaultPermission('delete-staff');
            }

            return false;
        });

        Gate::define('activate-staff', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'activate-staff',
                    'manage-all-staff'
                ]) || $user->accountType?->hasDefaultPermission('activate-staff');
            }

            return false;
        });

        Gate::define('deactivate-staff', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'deactivate-staff',
                    'manage-all-staff'
                ]) || $user->accountType?->hasDefaultPermission('deactivate-staff');
            }

            return false;
        });

        Gate::define('view-staff-reports', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-staff-reports',
                    'manage-all-staff'
                ]) || $user->accountType?->hasDefaultPermission('view-staff-reports');
            }

            return false;
        });

        Gate::define('manage-staff-assignments', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-staff-assignments',
                    'manage-all-staff'
                ]) || $user->accountType?->hasDefaultPermission('manage-staff-assignments');
            }

            return false;
        });

        Gate::define('manage-all-staff', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-all-staff'
                ]) || $user->accountType?->hasDefaultPermission('manage-all-staff');
            }

            return false;
        });

        // Visitation Management Gates
        Gate::define('view-visits', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-visits',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('view-visits');
            }

            return false;
        });

        Gate::define('create-visits', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'create-visits',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('create-visits');
            }

            return false;
        });

        Gate::define('update-visits', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'update-visits',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('update-visits');
            }

            return false;
        });

        Gate::define('delete-visits', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'delete-visits',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('delete-visits');
            }

            return false;
        });

        Gate::define('checkin-visits', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'checkin-visits',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('checkin-visits');
            }

            return false;
        });

        Gate::define('complete-visits', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'complete-visits',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('complete-visits');
            }

            return false;
        });

        Gate::define('cancel-visits', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'cancel-visits',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('cancel-visits');
            }

            return false;
        });

        Gate::define('view-visitors', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-visitors',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('view-visitors');
            }

            return false;
        });

        Gate::define('create-visitors', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'create-visitors',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('create-visitors');
            }

            return false;
        });

        Gate::define('update-visitors', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'update-visitors',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('update-visitors');
            }

            return false;
        });

        Gate::define('delete-visitors', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'delete-visitors',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('delete-visitors');
            }

            return false;
        });

        Gate::define('blacklist-visitors', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'blacklist-visitors',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('blacklist-visitors');
            }

            return false;
        });

        Gate::define('remove-visitor-blacklist', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'remove-visitor-blacklist',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('remove-visitor-blacklist');
            }

            return false;
        });

        Gate::define('view-visit-statistics', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-visit-statistics',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('view-visit-statistics');
            }

            return false;
        });

        Gate::define('view-visit-reports', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'view-visit-reports',
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('view-visit-reports');
            }

            return false;
        });

        Gate::define('manage-all-visitation', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission([
                    'manage-all-visitation'
                ]) || $user->accountType?->hasDefaultPermission('manage-all-visitation');
            }

            return false;
        });

        // Statistics Management Gates
        Gate::define('view-statistics-dashboard', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('view-statistics-dashboard') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['view-statistics-dashboard'] ?? false;
            }

            return false;
        });
        Gate::define('view-statistics-summary', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('view-statistics-summary') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['view-statistics-summary'] ?? false;
            }

            return false;
        });
        Gate::define('view-statistics-demographic', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('view-statistics-demographic') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['view-statistics-demographic'] ?? false;
            }

            return false;
        });
        Gate::define('view-statistics-security', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('view-statistics-security') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['view-statistics-security'] ?? false;
            }

            return false;
        });
        Gate::define('view-statistics-health', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('view-statistics-health') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['view-statistics-health'] ?? false;
            }

            return false;
        });
        Gate::define('manage-statistics', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('manage-statistics') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['manage-statistics'] ?? false;
            }

            return false;
        });

        // Identity Management Gates
        Gate::define('view-inmates', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('view-inmates') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['view-inmates'] ?? false;
            }

            return false;
        });
        Gate::define('create-inmates', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('create-inmates') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['create-inmates'] ?? false;
            }

            return false;
        });
        Gate::define('update-inmates', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('update-inmates') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['update-inmates'] ?? false;
            }

            return false;
        });
        Gate::define('delete-inmates', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('delete-inmates') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['delete-inmates'] ?? false;
            }

            return false;
        });
        Gate::define('view-warrants', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('view-warrants') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['view-warrants'] ?? false;
            }

            return false;
        });
        Gate::define('create-warrants', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('create-warrants') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['create-warrants'] ?? false;
            }

            return false;
        });
        Gate::define('update-warrants', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('update-warrants') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['update-warrants'] ?? false;
            }

            return false;
        });
        Gate::define('delete-warrants', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('delete-warrants') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['delete-warrants'] ?? false;
            }

            return false;
        });
        Gate::define('manage-identity', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('manage-identity') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['manage-identity'] ?? false;
            }

            return false;
        });

        // Case Management Gates
        Gate::define('view-cases', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('view-cases') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['view-cases'] ?? false;
            }

            return false;
        });
        Gate::define('create-cases', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('create-cases') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['create-cases'] ?? false;
            }

            return false;
        });
        Gate::define('update-cases', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('update-cases') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['update-cases'] ?? false;
            }

            return false;
        });
        Gate::define('delete-cases', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('delete-cases') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['delete-cases'] ?? false;
            }

            return false;
        });
        Gate::define('view-court-sessions', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('view-court-sessions') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['view-court-sessions'] ?? false;
            }

            return false;
        });
        Gate::define('create-court-sessions', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('create-court-sessions') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['create-court-sessions'] ?? false;
            }

            return false;
        });
        Gate::define('update-court-sessions', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('update-court-sessions') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['update-court-sessions'] ?? false;
            }

            return false;
        });
        Gate::define('delete-court-sessions', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('delete-court-sessions') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['delete-court-sessions'] ?? false;
            }

            return false;
        });
        Gate::define('manage-cases', function ($user) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPermission('manage-cases') || $user->hasPermission('manage-all') || $user->accountType->default_permissions['manage-cases'] ?? false;
            }

            return false;
        });

        // Global super admin bypass for all gates
        // This ensures that any gate not explicitly defined will still allow super admin access
        Gate::before(function ($user, $ability) use ($isHqAdminSuperUser) {
            if ($isHqAdminSuperUser($user)) {
                return true;
            }
        });
    }
}
