<?php

namespace App\Policies;

use App\Models\Staff;
use App\Policies\BasePolicy;
use Modules\AdminManagement\Entities\HqAdmin;
use Modules\CaseManagement\Entities\Sitting;

class SittingPolicy extends BasePolicy
{
    /**
     * Get the base permission name for sittings/cases
     */
    protected function getBasePermission(): string
    {
        return 'cases';
    }

    /**
     * Override view permissions to include manage permissions
     */
    protected function getViewPermissions(): array
    {
        return [
            'view-cases',
            'manage-cases',
            'manage-all'
        ];
    }

    /**
     * Custom authorization for viewing case statistics
     */
    public function viewStats($user): bool
    {
        return $this->hasAnyPermission($user, [
            'view-case-stats',
            'view-cases',
            'manage-cases',
            'manage-all'
        ]);
    }

    /**
     * Custom authorization for searching cases
     */
    public function search($user): bool
    {
        return $this->hasAnyPermission($user, [
            'search-cases',
            'view-cases',
            'manage-cases',
            'manage-all'
        ]);
    }

    /**
     * Custom authorization for viewing remand indicators
     */
    public function viewRemandIndicators($user): bool
    {
        return $this->hasAnyPermission($user, [
            'view-remand-indicators',
            'view-cases',
            'manage-cases',
            'manage-all'
        ]);
    }
}
