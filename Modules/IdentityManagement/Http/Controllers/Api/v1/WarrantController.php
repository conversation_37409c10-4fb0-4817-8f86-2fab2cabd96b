<?php

namespace Modules\IdentityManagement\Http\Controllers\Api\v1;

use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Gate;
use Modules\IdentityManagement\Entities\Warrant;
use Illuminate\Support\Facades\Storage;

class WarrantController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware('authorize:view-warrants')->only(['index', 'show']);
        $this->middleware('authorize:create-warrants')->only(['store']);
        $this->middleware('authorize:update-warrants')->only(['update']);
        $this->middleware('authorize:delete-warrants')->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index()
    {
        // Authorization handled by middleware

        return view('identitymanagement::index');
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        return view('identitymanagement::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        // Authorization handled by middleware

        //
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function show($id)
    {
        // Authorization handled by middleware

        return view('identitymanagement::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        return view('identitymanagement::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        // Authorization handled by middleware

        //
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy(Warrant $warrant)
    {
        if (!Gate::allows('delete-warrants')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $warrant->delete();
        if (Storage::disk('public')->exists($warrant->path)) {
            Storage::disk('public')->delete($warrant->path);
            return response()->json(['message' => 'File deleted successfully']);
        }

    }
}
