<?php

namespace App\Services;

use App\Models\Escapee;
use App\Models\EscapeeBatch;
use Modules\IdentityManagement\Entities\Inmate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Symfony\Component\HttpFoundation\StreamedResponse;
use League\Csv\Writer;
use League\Csv\Reader;

class EscapeeService
{
    /**
     * List all escapee batches with optional filtering
     */
    public function listBatches(array $filters = []): LengthAwarePaginator
    {
        $query = EscapeeBatch::with(['state', 'prison'])
            ->when(isset($filters['state_id']), fn($q) => $q->where('state_id', $filters['state_id']))
            ->when(isset($filters['prison_id']), fn($q) => $q->where('prison_id', $filters['prison_id']))
            ->when(isset($filters['batch_status']), fn($q) => $q->where('batch_status', $filters['batch_status']))
            ->when(isset($filters['search']), function($q) use ($filters) {
                return $q->where('title', 'like', "%{$filters['search']}%")
                    ->orWhere('description', 'like', "%{$filters['search']}%");
            })
            ->when(isset($filters['date_from']), fn($q) => $q->where('created_at', '>=', $filters['date_from']))
            ->when(isset($filters['date_to']), fn($q) => $q->where('created_at', '<=', $filters['date_to']));

        // Add sorting
        $sortField = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        return $query->paginate($filters['per_page'] ?? 15);
    }

    /**
     * Get a specific batch with its escapees
     */
    public function getBatch($id)
    {
        return EscapeeBatch::with(['state', 'prison', 'escapees.prisoner'])->findOrFail($id);
    }

    /**
     * Create a new escapee batch
     */
    public function createBatch(array $data)
    {
        return EscapeeBatch::create($data);
    }

    /**
     * Update an existing escapee batch
     */
    public function updateBatch($id, array $data)
    {
        $batch = EscapeeBatch::findOrFail($id);
        $batch->update($data);
        return $batch;
    }

    /**
     * Delete an escapee batch (soft delete)
     */
    public function deleteBatch($id)
    {
        $batch = EscapeeBatch::findOrFail($id);
        return $batch->delete();
    }

    /**
     * List escapees with optional filtering
     */
    public function listEscapees(array $filters = []): LengthAwarePaginator
    {
        $query = Escapee::with(['batch', 'prisoner'])
            ->when(isset($filters['batch_id']), fn($q) => $q->where('batch_id', $filters['batch_id']))
            ->when(isset($filters['current_status']), fn($q) => $q->where('current_status', $filters['current_status']))
            ->when(isset($filters['escape_date_from']), fn($q) => $q->where('escape_date', '>=', $filters['escape_date_from']))
            ->when(isset($filters['escape_date_to']), fn($q) => $q->where('escape_date', '<=', $filters['escape_date_to']))
            ->when(isset($filters['recapture_date_from']), fn($q) => $q->where('recapture_date', '>=', $filters['recapture_date_from']))
            ->when(isset($filters['recapture_date_to']), fn($q) => $q->where('recapture_date', '<=', $filters['recapture_date_to']))
            ->when(isset($filters['search']), function($q) use ($filters) {
                return $q->whereHas('prisoner', function($query) use ($filters) {
                    $query->where('prisoner_no', 'like', "%{$filters['search']}%")
                        ->orWhere('surname', 'like', "%{$filters['search']}%")
                        ->orWhere('first_name', 'like', "%{$filters['search']}%");
                });
            });

        // Add sorting
        $sortField = $filters['sort_by'] ?? 'escape_date';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        return $query->paginate($filters['per_page'] ?? 15);
    }

    /**
     * Get a specific escapee
     */
    public function getEscapee($id)
    {
        return Escapee::with(['batch', 'prisoner'])->findOrFail($id);
    }

    /**
     * Add an escapee to a batch
     */
    public function addEscapeeToBatch($batchId, array $escapeeData)
    {
        DB::beginTransaction();
        try {
            // Create the escapee record
            $escapeeData['batch_id'] = $batchId;
            $escapee = Escapee::create($escapeeData);

            // Update the inmate record to mark as escapee
            $this->updateInmateEscapeeStatus($escapeeData['prisoner_id'], true,
                $escapeeData['current_status'] === 'recaptured');

            DB::commit();
            return $escapee;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to add escapee to batch: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update an existing escapee
     */
    public function updateEscapee($id, array $data)
    {
        DB::beginTransaction();
        try {
            $escapee = Escapee::findOrFail($id);
            $oldStatus = $escapee->current_status;

            $escapee->update($data);

            // Update inmate record if status changed to/from recaptured
            if (isset($data['current_status']) && $oldStatus !== $data['current_status']) {
                $this->updateInmateEscapeeStatus(
                    $escapee->prisoner_id,
                    true,
                    $data['current_status'] === 'recaptured'
                );
            }

            DB::commit();
            return $escapee;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update escapee: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete an escapee (soft delete)
     */
    public function deleteEscapee($id)
    {
        $escapee = Escapee::findOrFail($id);
        return $escapee->delete();
    }

    /**
     * Update inmate record to mark as escapee and/or recaptured
     */
    public function updateInmateEscapeeStatus(string $prisonerNo, bool $isEscapee, bool $isRecaptured): bool
    {
        try {
            $inmate = Inmate::where('prisoner_no', $prisonerNo)->first();

            if (!$inmate) {
                Log::warning("Inmate not found for prisoner number: {$prisonerNo}");
                return false;
            }

            $inmate->escapee = $isEscapee ? 1 : 0;
            $inmate->recaptured = $isRecaptured ? 1 : 0;
            $inmate->save();

            return true;
        } catch (\Exception $e) {
            Log::error("Failed to update inmate escapee status: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Process CSV file upload for escapees
     */
    public function processCsvUpload(UploadedFile $file, array $batchData, array $options = []): array
    {
        DB::beginTransaction();
        try {
            // Set default options
            $options = array_merge([
                'skip_header' => true,
                'preview_only' => false,
                'delimiter' => ',',
                'encoding' => 'utf-8',
            ], $options);

            // Create the batch
            $batch = null;
            if (!$options['preview_only']) {
                $batch = $this->createBatch($batchData);
            }

            // Process the CSV file
            $reader = Reader::createFromPath($file->getPathname(), 'r');
            $reader->setDelimiter($options['delimiter']);
            $reader->setEncoding($options['encoding']);

            // Skip header row if needed
            $records = $options['skip_header'] ? $reader->getRecords() : $reader->getIterator();

            $processedRecords = [];
            $errors = [];
            $successCount = 0;
            $errorCount = 0;
            $rowNumber = $options['skip_header'] ? 2 : 1; // Start from row 2 if skipping header

            foreach ($records as $index => $record) {
                // Process each record
                $result = $this->processEscapeeCsvRecord($record, $batch?->id, $rowNumber, $options['preview_only']);

                if ($result['success']) {
                    $processedRecords[] = $result['data'];
                    $successCount++;
                } else {
                    $errors[] = [
                        'row' => $rowNumber,
                        'errors' => $result['errors'],
                        'data' => $result['data'],
                    ];
                    $errorCount++;
                }

                $rowNumber++;
            }

            // If preview only or there are errors, rollback
            if ($options['preview_only'] || ($errorCount > 0 && !$options['preview_only'])) {
                DB::rollBack();
                return [
                    'success' => $options['preview_only'] ? true : ($errorCount === 0),
                    'preview_mode' => $options['preview_only'],
                    'processed_records' => $processedRecords,
                    'errors' => $errors,
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'batch' => $options['preview_only'] ? $batchData : null,
                ];
            }

            DB::commit();
            return [
                'success' => true,
                'preview_mode' => false,
                'processed_records' => $processedRecords,
                'errors' => $errors,
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'batch' => $batch,
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('CSV upload processing failed: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Failed to process CSV file: ' . $e->getMessage(),
                'errors' => [['row' => 0, 'errors' => [$e->getMessage()], 'data' => []]],
                'success_count' => 0,
                'error_count' => 1,
            ];
        }
    }

    /**
     * Process a single CSV record for escapee import
     */
    private function processEscapeeCsvRecord(array $record, ?int $batchId, int $rowNumber, bool $previewOnly): array
    {
        // Map CSV columns to database fields
        $mappedData = $this->mapCsvRecordToEscapeeData($record);
        $errors = $this->validateEscapeeData($mappedData, $rowNumber);

        if (!empty($errors)) {
            return [
                'success' => false,
                'errors' => $errors,
                'data' => $mappedData,
            ];
        }

        // Add batch ID
        if ($batchId) {
            $mappedData['batch_id'] = $batchId;
        }

        // If preview only, return the mapped data without saving
        if ($previewOnly) {
            return [
                'success' => true,
                'data' => $mappedData,
            ];
        }

        // Create the escapee record
        try {
            $escapee = Escapee::create($mappedData);

            // Update inmate record
            $this->updateInmateEscapeeStatus(
                $mappedData['prisoner_id'],
                true,
                $mappedData['current_status'] === 'recaptured'
            );

            return [
                'success' => true,
                'data' => $escapee->toArray(),
            ];
        } catch (\Exception $e) {
            Log::error("Failed to create escapee from CSV row {$rowNumber}: " . $e->getMessage());

            return [
                'success' => false,
                'errors' => ["Database error: " . $e->getMessage()],
                'data' => $mappedData,
            ];
        }
    }

    /**
     * Map CSV record to escapee data structure
     */
    private function mapCsvRecordToEscapeeData(array $record): array
    {
        // Default mapping (adjust based on your CSV structure)
        $mapping = [
            0 => 'prisoner_id',
            1 => 'escape_date',
            2 => 'current_status',
            3 => 'escape_location',
            4 => 'escape_circumstances',
            5 => 'recapture_date',
            6 => 'recapture_location',
        ];

        $data = [];
        foreach ($mapping as $index => $field) {
            if (isset($record[$index])) {
                $data[$field] = trim($record[$index]);
            }
        }

        // Format dates
        if (isset($data['escape_date'])) {
            try {
                $data['escape_date'] = Carbon::parse($data['escape_date'])->format('Y-m-d');
            } catch (\Exception $e) {
                // Leave as is for validation to catch
            }
        }

        if (isset($data['recapture_date']) && !empty($data['recapture_date'])) {
            try {
                $data['recapture_date'] = Carbon::parse($data['recapture_date'])->format('Y-m-d');
            } catch (\Exception $e) {
                // Leave as is for validation to catch
            }
        } else {
            $data['recapture_date'] = null;
        }

        return $data;
    }

    /**
     * Validate escapee data from CSV
     */
    private function validateEscapeeData(array $data, int $rowNumber): array
    {
        $errors = [];

        // Required fields
        if (empty($data['prisoner_id'])) {
            $errors[] = "Row {$rowNumber}: Prisoner ID is required";
        } else {
            // Check if prisoner exists
            $prisoner = Inmate::where('prisoner_no', $data['prisoner_id'])->first();
            if (!$prisoner) {
                $errors[] = "Row {$rowNumber}: Prisoner with ID {$data['prisoner_id']} not found";
            }
        }

        if (empty($data['escape_date'])) {
            $errors[] = "Row {$rowNumber}: Escape date is required";
        } else {
            // Validate date format
            try {
                $escapeDate = Carbon::parse($data['escape_date']);
                if ($escapeDate->isFuture()) {
                    $errors[] = "Row {$rowNumber}: Escape date cannot be in the future";
                }
            } catch (\Exception $e) {
                $errors[] = "Row {$rowNumber}: Invalid escape date format";
            }
        }

        if (empty($data['current_status'])) {
            $errors[] = "Row {$rowNumber}: Current status is required";
        } else {
            // Validate status
            $validStatuses = ['escaped', 'recaptured', 'deceased', 'unknown'];
            if (!in_array(strtolower($data['current_status']), $validStatuses)) {
                $errors[] = "Row {$rowNumber}: Invalid status. Must be one of: " . implode(', ', $validStatuses);
            } else {
                // Normalize status
                $data['current_status'] = strtolower($data['current_status']);
            }
        }

        // Conditional validation for recaptured status
        if (isset($data['current_status']) && $data['current_status'] === 'recaptured') {
            if (empty($data['recapture_date'])) {
                $errors[] = "Row {$rowNumber}: Recapture date is required for recaptured escapees";
            } else {
                // Validate recapture date
                try {
                    $recaptureDate = Carbon::parse($data['recapture_date']);
                    $escapeDate = Carbon::parse($data['escape_date']);

                    if ($recaptureDate->isFuture()) {
                        $errors[] = "Row {$rowNumber}: Recapture date cannot be in the future";
                    }

                    if ($recaptureDate->lt($escapeDate)) {
                        $errors[] = "Row {$rowNumber}: Recapture date cannot be before escape date";
                    }
                } catch (\Exception $e) {
                    $errors[] = "Row {$rowNumber}: Invalid recapture date format";
                }
            }

            if (empty($data['recapture_location'])) {
                $errors[] = "Row {$rowNumber}: Recapture location is required for recaptured escapees";
            }
        }

        return $errors;
    }

    /**
     * Generate CSV template for escapee upload
     */
    public function generateCsvTemplate(): StreamedResponse
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="escapee_upload_template.csv"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
        ];

        $callback = function() {
            $handle = fopen('php://output', 'w');

            // Add header row
            fputcsv($handle, [
                'Prisoner Number (required)',
                'Escape Date (required, YYYY-MM-DD)',
                'Current Status (required: escaped, recaptured, deceased, unknown)',
                'Escape Location',
                'Escape Circumstances',
                'Recapture Date (YYYY-MM-DD, required if status is recaptured)',
                'Recapture Location (required if status is recaptured)',
            ]);

            // Add sample data row
            fputcsv($handle, [
                'ABC123456',
                date('Y-m-d'),
                'escaped',
                'Prison yard',
                'Escaped during outdoor exercise',
                '',
                '',
            ]);

            fclose($handle);
        };

        return new StreamedResponse($callback, 200, $headers);
    }

    /**
     * Generate statistics for escapees
     */
    public function generateStatistics(array $filters = []): array
    {
        // Base query for escapees
        $query = Escapee::query()
            ->when(isset($filters['state_id']), function($q) use ($filters) {
                return $q->whereHas('batch', function($query) use ($filters) {
                    $query->where('state_id', $filters['state_id']);
                });
            })
            ->when(isset($filters['prison_id']), function($q) use ($filters) {
                return $q->whereHas('batch', function($query) use ($filters) {
                    $query->where('prison_id', $filters['prison_id']);
                });
            })
            ->when(isset($filters['date_from']), fn($q) => $q->where('escape_date', '>=', $filters['date_from']))
            ->when(isset($filters['date_to']), fn($q) => $q->where('escape_date', '<=', $filters['date_to']));

        // Total escapees
        $totalEscapees = $query->count();

        // Escapees by status
        $byStatus = $query->selectRaw('current_status, count(*) as count')
            ->groupBy('current_status')
            ->pluck('count', 'current_status')
            ->toArray();

        // Active escapees (still at large)
        $activeEscapees = $query->whereIn('current_status', ['escaped', 'unknown'])->count();

        // Recapture rate
        $recaptured = $query->where('current_status', 'recaptured')->count();
        $recaptureRate = $totalEscapees > 0 ? round(($recaptured / $totalEscapees) * 100, 2) : 0;

        // Average time to recapture
        $avgRecaptureTime = $query->where('current_status', 'recaptured')
            ->whereNotNull('escape_date')
            ->whereNotNull('recapture_date')
            ->selectRaw('AVG(DATEDIFF(recapture_date, escape_date)) as avg_days')
            ->first()
            ->avg_days ?? 0;

        // Escapes by month (for trend analysis)
        $escapesByMonth = $query->selectRaw('DATE_FORMAT(escape_date, "%Y-%m") as month, count(*) as count')
            ->groupBy('month')
            ->orderBy('month')
            ->pluck('count', 'month')
            ->toArray();

        // Escapes by prison (if no prison filter)
        $escapesByPrison = [];
        if (!isset($filters['prison_id'])) {
            $escapesByPrison = $query->join('escapee_batches', 'escapees.batch_id', '=', 'escapee_batches.id')
                ->join('prisons', 'escapee_batches.prison_id', '=', 'prisons.id')
                ->selectRaw('prisons.prison_name, count(escapees.id) as count')
                ->groupBy('prisons.id', 'prisons.prison_name')
                ->orderByDesc('count')
                ->limit(10)
                ->pluck('count', 'prison_name')
                ->toArray();
        }

        return [
            'total_escapees' => $totalEscapees,
            'active_escapees' => $activeEscapees,
            'by_status' => $byStatus,
            'recapture_rate' => $recaptureRate,
            'avg_recapture_time' => round($avgRecaptureTime, 1),
            'escapes_by_month' => $escapesByMonth,
            'escapes_by_prison' => $escapesByPrison,
            'filters_applied' => $filters,
        ];
    }
}
