<?php

namespace Modules\AdminManagement\Http\Controllers\Api\V1;

use App\Traits\Common;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Contracts\Support\Renderable;
use Modules\AdminManagement\Entities\StateZoneAdmin;

class StateAdminZoneController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    use Common; //Trait

    public function index(Request $request)
    {
        /*
        /// Do a custom query on Inmate model,
        */
        $noIncludeArray = array('count', 'page', 'include','paginate','has','api_token');
        $relations=array('state','zone');
        $includeString = explode(',',$request->include);
        $query = $request->query();
        $paginate = $request->paginate? $request->paginate:20;
        //// This method will do a custom query based on query string to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

        $admins = StateZoneAdmin::where($filteredQuery)->with(['state', 'zone'])->orderBy('id','desc');

        ////////IF THE RECORD NEEDED HAS A RELATIONSHIP
        $admins = $admins->when($request->has, function ($query) use ($request) {
            return $query->whereHas($request->has);
        });

        ///////// IF THE REQUEST NEEDS PAGINATION
        $admins = $admins->when($request->paginate, function($query) use($request,$paginate){
        return $paginate!='all' ? $query->paginate($paginate) : $query->get();
        });

        if($request->include){
            foreach($includeString as $string){
                $checkRelationshipArray = $this->shouldIncludeRelation($relations, $string);
                if($checkRelationshipArray) $admins->load($string);
            }
        }

        if($request->count){
        $admins = $admins->when($request->count, function($query) use($request,$paginate){
            return $query->count();
        });
    }


        return response()->json(['data' => $admins]);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        return view('adminmanagement::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function show($id)
    {
        return view('adminmanagement::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        return view('adminmanagement::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        //
    }
}
