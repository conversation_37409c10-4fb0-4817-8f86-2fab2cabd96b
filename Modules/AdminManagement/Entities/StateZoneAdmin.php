<?php

namespace Modules\AdminManagement\Entities;

use App\Models\States;
use App\Models\Zone;
use App\Traits\HasRoleBasedSuperAdmin;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Auth\Authenticatable as AuthenticatableTrait;

class StateZoneAdmin extends Model implements Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, AuthenticatableTrait, HasRoleBasedSuperAdmin;

    protected $table = 'state_zone_admins';

    protected $fillable = ['first_name',
        'last_name',
        'password',
        'email',
        'phone',
        'address',
        'assigned_state',
        'zone_id',
        'last_login',
        'status',
    ];
    protected $hidden = [
        'password',
    ];


    protected $casts = [
        'last_login' => 'datetime',
    ];


    public function state()
    {
        return $this->belongsTo(States::class, 'assigned_state');
    }

    public function zone()
    {
        return $this->belongsTo(Zone::class, 'zone_id');
    }

    /**
     * Check if this StateZoneAdmin is a super admin
     * StateZoneAdmins can only be super admins through role assignment
     */
    public function isSuperAdmin(): bool
    {
        // StateZoneAdmins can only be super admins through role-based assignment
        return $this->isRoleBasedSuperAdmin();
    }

    /**
     * Check if StateZoneAdmin has a specific permission
     * Super admins have all permissions, others have limited permissions based on their state/zone assignment
     */
    public function hasPermission(string $permission, ?int $stateId = null, ?int $prisonId = null): bool
    {
        // Super admins have unrestricted access to all permissions
        if ($this->isSuperAdmin()) {
            return true;
        }

        // StateZoneAdmins have basic permissions for their assigned state/zone
        // This can be extended based on business requirements
        $basicPermissions = [
            'view-prisoners',
            'view-staff',
            'view-basic-reports',
            'manage-state-data'
        ];

        return in_array($permission, $basicPermissions);
    }

    /**
     * Check if StateZoneAdmin has any of the given permissions
     * Super admins have all permissions, others are checked individually
     */
    public function hasAnyPermission(array $permissions, ?int $stateId = null, ?int $prisonId = null): bool
    {
        // Super admins have unrestricted access to all permissions
        if ($this->isSuperAdmin()) {
            return true;
        }

        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission, $stateId, $prisonId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if this StateZoneAdmin can access a specific state
     */
    public function canAccessState(int $stateId): bool
    {
        return $this->assigned_state == $stateId;
    }

    /**
     * Check if this StateZoneAdmin can access a specific zone
     */
    public function canAccessZone(int $zoneId): bool
    {
        return $this->zone_id == $zoneId;
    }

    protected static function newFactory()
    {
        return \Modules\AdminManagement\Database\factories\AdminManagementFactory::new();
    }
}
