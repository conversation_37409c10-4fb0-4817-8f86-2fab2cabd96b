<?php

namespace App\Services;

use Illuminate\Support\Facades\Gate;
use App\Models\Staff;
use Modules\AdminManagement\Entities\HqAdmin;

class GateHelperService
{
    /**
     * Register a simple permission gate
     */
    public static function registerPermissionGate(string $gateName, array $permissions): void
    {
        Gate::define($gateName, function ($user) use ($permissions) {
            // Super admin bypass
            if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasAnyPermission($permissions) || 
                       $user->accountType?->hasAnyDefaultPermission($permissions);
            }

            return false;
        });
    }

    /**
     * Register multiple permission gates at once
     */
    public static function registerPermissionGates(array $gates): void
    {
        foreach ($gates as $gateName => $permissions) {
            self::registerPermissionGate($gateName, (array) $permissions);
        }
    }

    /**
     * Register a role-based gate
     */
    public static function registerRoleGate(string $gateName, array $roles, ?int $minHierarchyLevel = null): void
    {
        Gate::define($gateName, function ($user) use ($roles, $minHierarchyLevel) {
            // Super admin bypass
            if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
                return true;
            }

            if ($user instanceof Staff) {
                $hasRole = $user->hasAnyRole($roles);
                $hasHierarchy = $minHierarchyLevel ? 
                    ($user->accountType?->hierarchy_level >= $minHierarchyLevel) : true;
                
                return $hasRole || $hasHierarchy;
            }

            return false;
        });
    }

    /**
     * Register a scoped permission gate (with state/prison access)
     */
    public static function registerScopedGate(string $gateName, string $permission): void
    {
        Gate::define($gateName, function ($user, $targetStaff = null) use ($permission) {
            // Super admin bypass
            if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
                return true;
            }

            if (!$user instanceof Staff) {
                return false;
            }

            // System admin can access everything
            if ($user->hasPermission('system-admin')) {
                return true;
            }

            // If no target staff, just check permission
            if (!$targetStaff) {
                return $user->hasPermission($permission);
            }

            // Check scoped access
            if ($user->hasPermission('state-admin') && 
                $user->assigned_state === $targetStaff->assigned_state) {
                return true;
            }

            if ($user->hasPermission('prison-admin') && 
                $user->prison === $targetStaff->prison) {
                return true;
            }

            // Self-access
            return $user->id === $targetStaff->id;
        });
    }

    /**
     * Register access gates for state and prison
     */
    public static function registerAccessGates(): void
    {
        Gate::define('access-state', function ($user, int $stateId) {
            if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasStateAccess($stateId);
            }

            return false;
        });

        Gate::define('access-prison', function ($user, int $prisonId) {
            if ($user instanceof HqAdmin && $user->isSuperAdmin()) {
                return true;
            }

            if ($user instanceof Staff) {
                return $user->hasPrisonAccess($prisonId);
            }

            return false;
        });
    }

    /**
     * Register all common system gates
     */
    public static function registerSystemGates(): void
    {
        // Role-based gates
        self::registerRoleGate('system-admin', ['super-admin', 'system-admin'], 10);
        self::registerRoleGate('state-admin', ['super-admin', 'system-admin', 'state-admin'], 8);
        self::registerRoleGate('prison-admin', ['super-admin', 'system-admin', 'state-admin', 'prison-admin'], 6);

        // Account type gates
        self::registerRoleGate('account-type-admin', [], 8);
        self::registerRoleGate('account-type-supervisor', [], 6);
        self::registerRoleGate('account-type-officer', [], 4);

        // Access gates
        self::registerAccessGates();

        // RBAC management gates
        self::registerScopedGate('view-staff-permissions', 'view-staff-permissions');
        self::registerScopedGate('assign-roles', 'assign-roles');
        self::registerScopedGate('remove-roles', 'remove-roles');
        self::registerScopedGate('manage-staff-roles', 'manage-staff-roles');
        self::registerScopedGate('manage-staff-permissions', 'manage-staff-permissions');
    }

    /**
     * Register module-specific gates
     */
    public static function registerModuleGates(): void
    {
        // Training Management
        self::registerPermissionGates([
            'view-training-programs' => ['view-training-programs', 'manage-training-programs', 'manage-all-training'],
            'create-training-programs' => ['create-training-programs', 'manage-training-programs', 'manage-all-training'],
            'update-training-programs' => ['update-training-programs', 'manage-training-programs', 'manage-all-training'],
            'delete-training-programs' => ['delete-training-programs', 'manage-training-programs', 'manage-all-training'],
            'manage-training-enrollments' => ['manage-training-enrollments', 'manage-training-programs', 'manage-all-training'],
            'view-training-reports' => ['view-training-reports', 'manage-training-programs', 'manage-all-training'],
            'manage-all-training' => ['manage-all-training'],
        ]);

        // Health Management
        self::registerPermissionGates([
            'view-medical-history' => ['view-medical-history', 'manage-all-health'],
            'create-medical-history' => ['create-medical-history', 'manage-all-health'],
            'update-medical-history' => ['update-medical-history', 'manage-all-health'],
            'delete-medical-history' => ['delete-medical-history', 'manage-all-health'],
            'view-medical-appointments' => ['view-medical-appointments', 'manage-all-health'],
            'create-medical-appointments' => ['create-medical-appointments', 'manage-all-health'],
            'update-medical-appointments' => ['update-medical-appointments', 'manage-all-health'],
            'delete-medical-appointments' => ['delete-medical-appointments', 'manage-all-health'],
            'manage-all-health' => ['manage-all-health'],
        ]);

        // Legal Aid Management
        self::registerPermissionGates([
            'view-legal-aid' => ['view-legal-aid', 'manage-all-legal-aid'],
            'create-legal-aid' => ['create-legal-aid', 'manage-all-legal-aid'],
            'update-legal-aid' => ['update-legal-aid', 'manage-all-legal-aid'],
            'delete-legal-aid' => ['delete-legal-aid', 'manage-all-legal-aid'],
            'view-legal-representatives' => ['view-legal-representatives', 'manage-all-legal-aid'],
            'create-legal-representatives' => ['create-legal-representatives', 'manage-all-legal-aid'],
            'manage-all-legal-aid' => ['manage-all-legal-aid'],
        ]);

        // Belonging Management
        self::registerPermissionGates([
            'view-belongings' => ['view-belongings', 'manage-all-belongings'],
            'create-belongings' => ['create-belongings', 'manage-all-belongings'],
            'update-belongings' => ['update-belongings', 'manage-all-belongings'],
            'delete-belongings' => ['delete-belongings', 'manage-all-belongings'],
            'manage-cash-transactions' => ['manage-cash-transactions', 'manage-all-belongings'],
            'view-cash-statements' => ['view-cash-statements', 'manage-all-belongings'],
            'retrieve-belongings' => ['retrieve-belongings', 'manage-all-belongings'],
            'dispose-belongings' => ['dispose-belongings', 'manage-all-belongings'],
        ]);

        // Cloud Management
        self::registerPermissionGates([
            'upload-to-cloud' => ['upload-to-cloud', 'manage-all'],
            'view-cloud-statistics' => ['view-cloud-statistics', 'manage-all'],
            'retry-cloud-uploads' => ['retry-cloud-uploads', 'manage-all'],
            'verify-cloud-structure' => ['verify-cloud-structure', 'manage-all'],
            'edit-upload' => ['edit-upload', 'manage-all'],
            'sync-data' => ['sync-data', 'manage-all'],
        ]);
    }

    /**
     * Register all gates
     */
    public static function registerAllGates(): void
    {
        self::registerSystemGates();
        self::registerModuleGates();
    }
}
