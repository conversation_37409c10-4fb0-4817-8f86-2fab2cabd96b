<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('escapees', function (Blueprint $table) {
            $table->id();

            $table->string('batch_id');
            $table->unsignedBigInteger('old_batch_id')->nullable()->comment('from old cims v1 database');
            $table->string('prisoner_no');
            $table->boolean('uploaded')->default(false);
            $table->boolean('status')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('escapees');
    }
};
