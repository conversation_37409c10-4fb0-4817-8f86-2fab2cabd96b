<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\BelongingManagement\Http\Controllers\Api\v1\BelongingsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->group(function () {
    Route::prefix('v1')->group(function () {

        // Belonging Management Routes with new RBAC
        Route::middleware('authorize:any:view-belongings,manage-belongings,manage-all-belongings,manage-all')->group(function () {
            Route::get('belongings/trashed', [BelongingsController::class, 'trashed']);
            Route::get('belongings/statement', [BelongingsController::class, 'statement']);
            Route::get('belongings/{id}/statement', [BelongingsController::class, 'statement']);
            Route::get('/belongings', [BelongingsController::class, 'index']);
            Route::get('/belongings/{id}', [BelongingsController::class, 'show']);
            Route::get('/inmates/{prisoner_no}/balance', [BelongingsController::class, 'getBalance']);
            Route::get('/inmates/{prisoner_no}/belongings', [BelongingsController::class, 'getInmateBelongings']);
            Route::get('belongings/dashboard/stats', [BelongingsController::class, 'getDashboardStats']);
        });

        Route::middleware('authorize:any:create-belongings,manage-belongings,manage-all-belongings,manage-all')->group(function () {
            Route::post('/belongings', [BelongingsController::class, 'store']);
        });

        Route::middleware('authorize:any:update-belongings,manage-belongings,manage-all-belongings,manage-all')->group(function () {
            Route::put('/belongings/{id}', [BelongingsController::class, 'update']);
        });

        Route::middleware('authorize:any:retrieve-belongings,manage-belongings,manage-all-belongings,manage-all')->group(function () {
            Route::put('/belongings/{id}/retrieve', [BelongingsController::class, 'retrieve']);
        });

        Route::middleware('authorize:any:dispose-belongings,manage-belongings,manage-all-belongings,manage-all')->group(function () {
            Route::put('/belongings/{id}/dispose', [BelongingsController::class, 'dispose']);
        });

        Route::middleware('authorize:any:delete-belongings,manage-belongings,manage-all-belongings,manage-all')->group(function () {
            Route::delete('/belongings/{id}', [BelongingsController::class, 'destroy']);
        });

        Route::middleware('authorize:any:manage-cash-transactions,manage-belongings,manage-all-belongings,manage-all')->group(function () {
            Route::post('/belongings/{id}/deposit', [BelongingsController::class, 'deposit']);
            Route::post('/belongings/{id}/withdraw', [BelongingsController::class, 'withdraw']);
        });
    });
});
// Route::middleware('auth:api')->get('/belongingmanagement', function (Request $request) {
//     return $request->user();
// });
