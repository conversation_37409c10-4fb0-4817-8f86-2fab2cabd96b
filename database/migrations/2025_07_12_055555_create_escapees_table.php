<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('escapees', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('batch_id');
            $table->unsignedBigInteger('old_batch_id')->nullable()->comment('from old cims v1 database');
            $table->string('prisoner_id');
            $table->date('escape_date');
            $table->string('escape_location')->nullable();
            $table->text('escape_circumstances')->nullable();
            $table->date('recapture_date')->nullable();
            $table->string('recapture_location')->nullable();
            $table->enum('current_status', ['escaped', 'recaptured', 'deceased', 'unknown'])->default('escaped');
            $table->boolean('uploaded')->default(false);
            $table->boolean('status')->default(true);
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('batch_id')->references('id')->on('escapee_batches')->onDelete('cascade');
            $table->foreign('prisoner_id')->references('prisoner_no')->on('inmates_all_class_register')->onDelete('cascade');

            // Indexes for performance
            $table->index('prisoner_id');
            $table->index('escape_date');
            $table->index('recapture_date');
            $table->index('current_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('escapees');
    }
};
