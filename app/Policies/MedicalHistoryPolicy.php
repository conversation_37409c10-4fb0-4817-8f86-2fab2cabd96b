<?php

namespace App\Policies;

use App\Models\Staff;
use App\Policies\BasePolicy;
use Modules\AdminManagement\Entities\HqAdmin;
use Modules\HealthManagement\Entities\MedicalHistory;

class MedicalHistoryPolicy extends BasePolicy
{
    /**
     * Get the base permission name for medical history
     */
    protected function getBasePermission(): string
    {
        return 'medical-history';
    }

    /**
     * Override view permissions to include manage permissions
     */
    protected function getViewPermissions(): array
    {
        return [
            'view-medical-history',
            'manage-medical-history',
            'manage-all-health',
            'manage-all'
        ];
    }
}
