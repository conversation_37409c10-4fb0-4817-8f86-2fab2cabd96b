<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\EscapeeRequest;
use App\Services\EscapeeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Gate;

class EscapeeController extends Controller
{
    protected EscapeeService $escapeeService;

    public function __construct(EscapeeService $escapeeService)
    {
        $this->middleware('auth:sanctum');
        $this->escapeeService = $escapeeService;
    }

    /**
     * Display a listing of escapees
     */
    public function index(Request $request): JsonResponse
    {
        if (!Gate::allows('view-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $filters = $request->only([
            'batch_id', 'current_status', 'escape_date_from', 'escape_date_to',
            'recapture_date_from', 'recapture_date_to', 'search',
            'sort_by', 'sort_direction', 'per_page'
        ]);

        $escapees = $this->escapeeService->listEscapees($filters);

        return response()->json([
            'success' => true,
            'data' => $escapees,
            'filters_applied' => $filters,
        ]);
    }

    /**
     * Store a newly created escapee
     */
    public function store(EscapeeRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();
            $batchId = $validated['batch_id'];
            unset($validated['batch_id']);

            $escapee = $this->escapeeService->addEscapeeToBatch($batchId, $validated);

            return response()->json([
                'success' => true,
                'message' => 'Escapee added successfully',
                'data' => $escapee,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add escapee',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified escapee
     */
    public function show($id): JsonResponse
    {
        if (!Gate::allows('view-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $escapee = $this->escapeeService->getEscapee($id);

            return response()->json([
                'success' => true,
                'data' => $escapee,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Escapee not found',
                'error' => $e->getMessage(),
            ], 404);
        }
    }

    /**
     * Update the specified escapee
     */
    public function update(EscapeeRequest $request, $id): JsonResponse
    {
        try {
            $escapee = $this->escapeeService->updateEscapee($id, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Escapee updated successfully',
                'data' => $escapee,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update escapee',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified escapee
     */
    public function destroy($id): JsonResponse
    {
        if (!Gate::allows('delete-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $this->escapeeService->deleteEscapee($id);

            return response()->json([
                'success' => true,
                'message' => 'Escapee deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete escapee',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get escapees for a specific batch
     */
    public function batchEscapees(Request $request, $batchId): JsonResponse
    {
        if (!Gate::allows('view-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $filters = $request->only([
            'current_status', 'escape_date_from', 'escape_date_to',
            'recapture_date_from', 'recapture_date_to', 'search',
            'sort_by', 'sort_direction', 'per_page'
        ]);

        $filters['batch_id'] = $batchId;

        $escapees = $this->escapeeService->listEscapees($filters);

        return response()->json([
            'success' => true,
            'data' => $escapees,
            'filters_applied' => $filters,
        ]);
    }

    /**
     * Bulk update escapees status
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        if (!Gate::allows('manage-escapees')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'escapee_ids' => 'required|array',
            'escapee_ids.*' => 'exists:escapees,id',
            'current_status' => 'required|in:escaped,recaptured,deceased,unknown',
            'recapture_date' => 'nullable|date|before_or_equal:today',
            'recapture_location' => 'nullable|string|max:255',
        ]);

        try {
            $updatedCount = 0;
            $errors = [];

            foreach ($validated['escapee_ids'] as $escapeeId) {
                try {
                    $updateData = ['current_status' => $validated['current_status']];
                    
                    if ($validated['current_status'] === 'recaptured') {
                        $updateData['recapture_date'] = $validated['recapture_date'] ?? now()->format('Y-m-d');
                        $updateData['recapture_location'] = $validated['recapture_location'] ?? '';
                    }

                    $this->escapeeService->updateEscapee($escapeeId, $updateData);
                    $updatedCount++;
                } catch (\Exception $e) {
                    $errors[] = "Failed to update escapee ID {$escapeeId}: " . $e->getMessage();
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully updated {$updatedCount} escapees",
                'updated_count' => $updatedCount,
                'errors' => $errors,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to bulk update escapees',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
