<?php
namespace Modules\CaseManagement\Entities;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\IdentityManagement\Entities\ATP;
use Modules\IdentityManagement\Entities\Inmate;

class Sittings extends BaseModel
{
    use HasFactory;
    protected $table = "inmates_sittings";
    // protected $primaryKey = 'id';
    protected $guarded = [];
    //protected $fillable = ['prisoner_no,charge_no_suit_no, next_court_appearance_date,court_of_inmates_next_appearance,prosecuting_agency,escort_duty_officer,time_of_court_sittings,legal_rep_name,state_id,prison_id','reason_for_adjournment','other_reason_for_adjournment','stage_of_proceedings','next_time_of_appearance','time_appeared_in_court','time_hearing_started','time_hearing_ended','legal_rep_phone','case_nature','date_of_case_nature_change'];
    public function Inmate(): BelongsTo
    {
        return $this->belongsTo(Inmate::class, 'prisoner_no', 'prisoner_no');
    }

    public function Awaiting(): BelongsTo
    {
        return $this->belongsTo(ATP::class, 'charge_no_suit_no', 'charge_no_suit_no');
    }

    protected static function newFactory()
    {
        return \Modules\CaseManagement\Database\factories\SittingsFactory::new ();
    }
}
