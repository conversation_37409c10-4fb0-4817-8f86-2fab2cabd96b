<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Staff;
use App\Models\Role;
use App\Models\Permission;
use App\Models\AccountType;
use Modules\AdminManagement\Entities\HqAdmin;
use App\Services\RbacService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\ValidationException;

class RbacController extends Controller
{
    protected RbacService $rbacService;

    public function __construct(RbacService $rbacService)
    {
        $this->rbacService = $rbacService;
    }

    /**
     * Get RBAC system statistics.
     */
    public function getStats(): JsonResponse
    {
        Gate::authorize('system-admin');

        $stats = $this->rbacService->getSystemStats();

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get all roles.
     */
    public function getRoles(Request $request): JsonResponse
    {
        Gate::authorize('view-roles');

        $query = Role::with(['permissions', 'state', 'prison']);

        if ($request->has('scope')) {
            $query->where('scope', $request->scope);
        }

        if ($request->has('active')) {
            $query->where('is_active', $request->boolean('active'));
        }

        $roles = $query->get();

        return response()->json([
            'success' => true,
            'data' => $roles
        ]);
    }

    /**
     * Get all permissions.
     */
    public function getPermissions(Request $request): JsonResponse
    {
        Gate::authorize('view-permissions');

        $query = Permission::query();

        if ($request->has('module')) {
            $query->where('module', $request->module);
        }

        if ($request->has('scope')) {
            $query->where('scope', $request->scope);
        }

        $permissions = $query->get()->groupBy('module');

        return response()->json([
            'success' => true,
            'data' => $permissions
        ]);
    }

    /**
     * Get all account types.
     */
    public function getAccountTypes(): JsonResponse
    {
        Gate::authorize('view-account-types');

        $accountTypes = AccountType::orderBy('hierarchy_level', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $accountTypes
        ]);
    }

    /**
     * Get user's permissions and roles.
     */
    public function getStaffPermissions($user): JsonResponse
    {
        Gate::authorize('view-staff-permissions', $user);

        $permissions = $this->rbacService->getStaffPermissions($user);
        $roles = $this->rbacService->getStaffRoles($user);

        $userData = [];
        if ($user instanceof Staff) {
            $userData = [
                'user' => $user->load(['accountType', 'state', 'prison']),
                'account_type_permissions' => $user->accountType?->getAllPermissions() ?? collect()
            ];
        } elseif ($user instanceof HqAdmin) {
            $userData = [
                'user' => $user,
                'admin_type' => $user->getAdminTypeName(),
                'is_super_admin' => $user->isSuperAdmin()
            ];
        }

        return response()->json([
            'success' => true,
            'data' => array_merge($userData, [
                'permissions' => $permissions,
                'roles' => $roles
            ])
        ]);
    }

    /**
     * Assign role to user.
     */
    public function assignRole(Request $request, $user): JsonResponse
    {
        Gate::authorize('assign-roles', $user);

        $request->validate([
            'role_name' => 'required|string|exists:roles,name',
            'state_id' => 'nullable|integer|exists:states,id',
            'prison_id' => 'nullable|integer|exists:prisons,id',
            'expires_at' => 'nullable|date|after:now'
        ]);

        try {
            $this->rbacService->assignRole(
                $user,
                $request->role_name,
                $request->state_id,
                $request->prison_id,
                $request->expires_at
            );

            return response()->json([
                'success' => true,
                'message' => 'Role assigned successfully'
            ]);
        } catch (\InvalidArgumentException $e) {
            throw ValidationException::withMessages([
                'role_name' => [$e->getMessage()]
            ]);
        }
    }

    /**
     * Remove role from user.
     */
    public function removeRole(Request $request, $user): JsonResponse
    {
        Gate::authorize('remove-roles', $user);

        $request->validate([
            'role_name' => 'required|string',
            'state_id' => 'nullable|integer',
            'prison_id' => 'nullable|integer'
        ]);

        $success = $this->rbacService->removeRole(
            $user,
            $request->role_name,
            $request->state_id,
            $request->prison_id
        );

        return response()->json([
            'success' => $success,
            'message' => $success ? 'Role removed successfully' : 'Role not found or not assigned'
        ]);
    }

    /**
     * Sync user roles (replace all roles with new ones).
     */
    public function syncStaffRoles(Request $request, $user): JsonResponse
    {
        Gate::authorize('manage-staff-roles', $user);

        $request->validate([
            'roles' => 'required|array',
            'roles.*.role_name' => 'required|string|exists:roles,name',
            'roles.*.state_id' => 'nullable|integer|exists:states,id',
            'roles.*.prison_id' => 'nullable|integer|exists:prisons,id',
            'roles.*.expires_at' => 'nullable|date|after:now'
        ]);

        try {
            $this->rbacService->syncStaffRoles($user, $request->roles);

            return response()->json([
                'success' => true,
                'message' => 'User roles synchronized successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync roles: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available roles for a user.
     */
    public function getAvailableRoles($user): JsonResponse
    {
        Gate::authorize('view-available-roles', $user);

        $availableRoles = $this->rbacService->getAvailableRoles($user);

        return response()->json([
            'success' => true,
            'data' => $availableRoles
        ]);
    }

    /**
     * Check if user has specific permission.
     */
    public function checkPermission(Request $request, $user): JsonResponse
    {
        Gate::authorize('check-staff-permissions', $user);

        $request->validate([
            'permission' => 'required|string'
        ]);

        $hasPermission = $this->rbacService->staffHasPermission($user, $request->permission);

        return response()->json([
            'success' => true,
            'data' => [
                'user_id' => $user->id,
                'permission' => $request->permission,
                'has_permission' => $hasPermission
            ]
        ]);
    }

    /**
     * Get permissions for a specific module.
     */
    public function getModulePermissions(string $module): JsonResponse
    {
        Gate::authorize('view-permissions');

        $permissions = $this->rbacService->getModulePermissions($module);

        return response()->json([
            'success' => true,
            'data' => [
                'module' => $module,
                'permissions' => $permissions
            ]
        ]);
    }



    /**
     * Update account type for staff member.
     */
    public function updateAccountType(Request $request, $user): JsonResponse
    {
        Gate::authorize('manage-account-types', $user);

        // Only Staff can have account types updated
        if (!$user instanceof Staff) {
            return response()->json([
                'success' => false,
                'message' => 'Only Staff users can have account types updated'
            ], 400);
        }

        $request->validate([
            'account_type' => 'required|string|exists:account_types,account_type'
        ]);

        $user->update([
            'account_type' => $request->account_type
        ]);



        return response()->json([
            'success' => true,
            'message' => 'Account type updated successfully',
            'data' => $user->fresh(['accountType'])
        ]);
    }
}
