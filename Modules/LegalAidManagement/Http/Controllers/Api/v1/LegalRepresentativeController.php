<?php

namespace Modules\LegalAidManagement\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\LegalAidManagement\Entities\LegalRepresentative;
use Modules\LegalAidManagement\Entities\LegalAidFirm;
use Modules\InmateManagement\Entities\Inmate;
use Modules\InmateManagement\Entities\InmateLegalRepresentative;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Gate;

class LegalRepresentativeController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware('authorize:view-legal-representatives')->only(['index', 'show']);
        $this->middleware('authorize:create-legal-representatives')->only(['store']);
        $this->middleware('authorize:update-legal-representatives')->only(['update']);
        $this->middleware('authorize:delete-legal-representatives')->only(['destroy']);
    }
  /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(Request $request)
    {
        // Authorization handled by middleware
    $noIncludeArray = array('count', 'page', 'include', 'paginate', 'search', 'inmates', 'assignments');
    $relations = array('inmates', 'firm', 'assignments');
    $includeString = $request->include ? explode(',', $request->include) : [];
    $query = $request->query();
    $paginate = $request->paginate ?: 20;

    // This method will do a custom query based on query string to filter out the count value
    $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

    $legalRep = LegalRepresentative::orderBy('created_at', 'desc');

    // Add search functionality to index method
    if ($request->has('search') && $request->search) {
      $searchTerm = $request->search;
      $legalRep->where(function ($q) use ($searchTerm) {
        $q->where('full_name', 'LIKE', '%' . $searchTerm . '%')
          ->orWhere('email', 'LIKE', '%' . $searchTerm . '%')
          ->orWhere('phone', 'LIKE', '%' . $searchTerm . '%');
      });
    }

    $legalRep = $legalRep->where($filteredQuery)->paginate($paginate);

    // Load relationships if requested
    if ($request->include) {
      foreach ($includeString as $string) {
        $checkRelationshipArray = $this->shouldIncludeRelation($relations, $string);
        if ($checkRelationshipArray) {
          $legalRep->load($string);
        }
      }
    }

    $response = ['data' => $legalRep];

    if ($request->count) {
      $response['count'] = $legalRep->total();
    }

    return response()->json($response);
  }

  /**
   * Show the form for creating a new resource.
   * @return Renderable
   */
  public function create()
  {
    return view('legalaidmanagement::create');
  }


  public function search(Request $request)
  {
    $legal_rep = LegalRepresentative::query();
    $paginate = $request->paginate ?: 20;

    // Search by name
    if ($request->has('legal_rep_name_filter') && $request->legal_rep_name_filter) {
      $legal_rep->where('full_name', 'LIKE', '%' . $request->legal_rep_name_filter . '%');
    }

    // Search by phone
    if ($request->has('legal_rep_phone_filter') && $request->legal_rep_phone_filter) {
      $legal_rep->where('phone', 'LIKE', '%' . $request->legal_rep_phone_filter . '%');
    }

    // Search by email
    if ($request->has('legal_rep_email_filter') && $request->legal_rep_email_filter) {
      $legal_rep->where('email', 'LIKE', '%' . $request->legal_rep_email_filter . '%');
    }

    // Order by creation date
    $legal_rep = $legal_rep->orderBy('created_at', 'desc')->paginate($paginate);

    // Load relationships if requested
    if ($request->has('include')) {
      $relations = ['inmates', 'firm'];
      $includeString = explode(',', $request->include);

      foreach ($includeString as $relation) {
        if (in_array($relation, $relations)) {
          $legal_rep->load($relation);
        }
      }
    }

    // Prepare response
    $response = ['data' => $legal_rep];

    // Add count if requested
    if ($request->has('count')) {
      $response['count'] = $legal_rep->total();
    }

    return response()->json($response);
  }

  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @return Renderable
   */
  public function store(Request $request)
  {
    if (!Gate::allows('create-legal-representatives')) {
      return response()->json(['error' => 'Unauthorized'], 403);
    }
    $request->validate([
      'full_name' => 'required',
      'phone' => 'required',
      'email' => 'sometimes|email',
      'firm_id' => 'required|exists:legal_aid_firms,id'
    ]);

    // Check if there is a Representative with same phone number
    $check_phone_number = LegalRepresentative::where("phone", $request->phone)->first();
    if ($check_phone_number) {
      return response()->json(['message' => 'This phone number already exists for a Legal Representative'], 422);
    }

    // Check if there is a Representative with same email if email provided
    if ($request->has('email')) {
      $check_email = LegalRepresentative::where("email", $request->email)->first();
      if ($check_email) {
        return response()->json(['message' => 'This email already exists for a Legal Representative'], 422);
      }
    }

    $legalRep = new LegalRepresentative($request->all());
    $legalRep->save();
    return response()->json(['message' => 'New Legal Representative saved']);
  }

  /**
   * Show the specified resource.
   * @param int $id
   * @return Renderable
   */
  public function show($id)
  {
    // Authorization handled by middleware
    return view('legalaidmanagement::show');
  }

  /**
   * Show the form for editing the specified resource.
   * @param int $id
   * @return Renderable
   */
  public function edit($id)
  {
    return view('legalaidmanagement::edit');
  }

  /**
   * Update the specified resource in storage.
   * @param Request $request
   * @param LegalRepresentative $legalrep
   * @return Renderable
   */
  public function update(Request $request, LegalRepresentative $legalrep)
  {
    if (!Gate::allows('update-legal-representatives')) {
      return response()->json(['error' => 'Unauthorized'], 403);
    }
    // Check if at least one field is present
    if (!$request->hasAny(['full_name', 'phone', 'email', 'status', 'firm_id'])) {
      return response()->json(['message' => 'At least one field must be provided for update'], 422);
    }

    // Validate the request data
    $request->validate([
      'full_name' => 'sometimes|string|max:255',
      'phone' => 'sometimes|string|max:20',
      'email' => 'sometimes|email|max:255',
      'status' => 'sometimes|in:0,1',
      'firm_id' => 'sometimes|exists:legal_aid_firms,id'
    ]);

    // Check for unique phone if provided
    if ($request->has('phone')) {
      $existingPhone = LegalRepresentative::where('phone', $request->phone)
        ->where('id', '!=', $legalrep->id)
        ->first();

      if ($existingPhone) {
        return response()->json(['message' => 'This phone number already exists for another Legal Representative'], 422);
      }
    }

    // Check for unique email if provided
    if ($request->has('email')) {
      $existingEmail = LegalRepresentative::where('email', $request->email)
        ->where('id', '!=', $legalrep->id)
        ->first();

      if ($existingEmail) {
        return response()->json(['message' => 'This email already exists for another Legal Representative'], 422);
      }
    }

    // Update all provided fields
    $legalrep->fill($request->only([
      'full_name',
      'phone',
      'email',
      'firm_id',
      'status'
    ]));

    $legalrep->save();

    return response()->json(['message' => "Lawyer's record updated"], 200);
  }

  /**
   * Remove the specified resource from storage.
   * @param int $id
   * @return Renderable
   */
  public function destroy(LegalRepresentative $legalrep)
  {
    if (!Gate::allows('delete-legal-representatives')) {
      return response()->json(['error' => 'Unauthorized'], 403);
    }

    return response()->json(['deleted' => $legalrep->delete()], 201);
  }

  /**
   * Assign or reassign an inmate to a legal representative
   * @param Request $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function assignInmate(Request $request)
  {
    if (!Gate::allows('manage-legal-assignments')) {
      return response()->json(['error' => 'Unauthorized'], 403);
    }
    $request->validate([
      'prisoner_no' => 'required|exists:inmates_all_class_register,prisoner_no',
      'rep_id' => 'required|exists:legal_representatives,id',
      'date_assigned' => 'required|date',
      'offence' => 'nullable|string|max:255',
    ]);

    try {
      // Check if inmate already has an active assignment
      $existingAssignment = DB::table('inmate_legal_representative')
        ->where('prisoner_no', $request->prisoner_no)
        ->where('status', 1)
        ->first();

      // If there's an existing assignment, update it to inactive
      if ($existingAssignment) {
        DB::table('inmate_legal_representative')
          ->where('prisoner_no', $request->prisoner_no)
          ->where('status', 1)
          ->update([
            'status' => 0,
            'updated_at' => now()
          ]);
      }

      // Create new assignment
      DB::table('inmate_legal_representative')->insert([
        'prisoner_no' => $request->prisoner_no,
        'rep_id' => $request->rep_id,
        'date_assigned' => $request->date_assigned,
        'offence' => $request->offence,
        'status' => 1,
        'created_at' => now(),
        'updated_at' => now()
      ]);

      return response()->json([
        'message' => $existingAssignment
          ? 'Inmate successfully reassigned to new legal representative'
          : 'Inmate successfully assigned to legal representative'
      ], 200);

    } catch (\Exception $e) {
      return response()->json([
        'message' => 'Failed to assign inmate',
        'error' => $e->getMessage()
      ], 500);
    }
  }

  /**
   * Batch unassign legal representatives from multiple inmates
   * @param Request $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function batchUnassign(Request $request)
  {
    if (!Gate::allows('manage-legal-assignments')) {
      return response()->json(['error' => 'Unauthorized'], 403);
    }
    $request->validate([
      'assignment_ids' => 'required|array',
      'assignment_ids.*' => 'required|string|exists:inmates_all_class_register,prisoner_no'
    ]);

    try {
      DB::table('inmate_legal_representative')
        ->whereIn('prisoner_no', $request->assignment_ids)
        ->delete();

      return response()->json([
        'message' => 'Successfully unassigned legal representatives'
      ], 200);
    } catch (\Exception $e) {
      return response()->json([
        'message' => 'Failed to unassign legal representatives',
        'error' => $e->getMessage()
      ], 500);
    }
  }

  /**
   * Remove specific legal representative assignments for an inmate
   * @param Request $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function removeSpecificAssignments(Request $request)
  {
    if (!Gate::allows('manage-legal-assignments')) {
      return response()->json(['error' => 'Unauthorized'], 403);
    }
    $request->validate([
      'prisoner_no' => 'required|string|exists:inmates_all_class_register,prisoner_no',
      'rep_ids' => 'required|array',
      'rep_ids.*' => 'required|integer|exists:legal_representatives,id'
    ]);

    try {
      DB::table('inmate_legal_representative')
        ->where('prisoner_no', $request->prisoner_no)
        ->whereIn('rep_id', $request->rep_ids)
        ->delete();

      return response()->json([
        'message' => 'Successfully removed specified legal representative assignments'
      ], 200);
    } catch (\Exception $e) {
      return response()->json([
        'message' => 'Failed to remove legal representative assignments',
        'error' => $e->getMessage()
      ], 500);
    }
  }

  /**
   * Batch unassign legal representatives from multiple inmates in rejected inmates list
   * @param Request $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function batchUnassignRejected(Request $request)
  {
    if (!Gate::allows('manage-legal-assignments')) {
      return response()->json(['error' => 'Unauthorized'], 403);
    }
    $request->validate([
      'assignment_ids' => 'required|array',
      'assignment_ids.*' => 'required|string|exists:legal_rep_deprived,prisoner_no'
    ]);

    try {
      DB::table('legal_rep_deprived')
        ->whereIn('prisoner_no', $request->assignment_ids)
        ->delete();

      return response()->json([
        'message' => 'Successfully unassigned legal representatives'
      ], 200);
    } catch (\Exception $e) {
      return response()->json([
        'message' => 'Failed to unassign legal representatives',
        'error' => $e->getMessage()
      ], 500);
    }
  }
}
