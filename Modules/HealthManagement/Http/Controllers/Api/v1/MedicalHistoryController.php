<?php

namespace Modules\HealthManagement\Http\Controllers\Api\v1;

use App\Traits\Common;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

use Modules\HealthManagement\Entities\MedicalHistory;

class MedicalHistoryController extends Controller
{
    use Common;

    public function __construct()
    {
        $this->middleware('authorize:view-medical-history')->only(['index', 'show', 'getHistoryByPrisoner']);
        $this->middleware('authorize:create-medical-history')->only(['store']);
        $this->middleware('authorize:update-medical-history')->only(['update']);
        $this->middleware('authorize:delete-medical-history')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(Request $request)
    {
        // Authorization handled by middleware

        $noIncludeArray = array('count', 'page', 'include', 'paginate', 'distinct', 'start_date', 'end_date', 'medical_status', 'search');
        $relations=array();
        $includeString = explode(',',$request->include);
        $query = $request->query();
        $paginate = $request->paginate? $request->paginate:20;

        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

        $history = MedicalHistory::orderBy('created_at','desc');

        $history->where($filteredQuery);

        if ($request->has('start_date') && $request->has('end_date')) {
            $history->whereBetween('diagnosis_date', [$request->start_date, $request->end_date]);
        } else if ($request->has('start_date')) {
            $history->where('diagnosis_date', '>=', $request->start_date);
        } else if ($request->has('end_date')) {
            $history->where('diagnosis_date', '<=', $request->end_date);
        }

        if ($request->has('medical_status')) {
            $history->where('medical_status', $request->medical_status); //Resolved or Ongoing
        }

        if ($request->has('search')) {
            $searchTerm = $request->search;
            $history->where(function($query) use ($searchTerm) {
                $query->where('prisoner_no', 'like', "%{$searchTerm}%")
                      ->orWhere('condition', 'like', "%{$searchTerm}%")
                      ->orWhere('notes', 'like', "%{$searchTerm}%");
            });
        }

        $results = $history->paginate($paginate);

        if($request->include){
            foreach($includeString as $string){
                $checkRelationshipArray = $this->shouldIncludeRelation($relations, $string);
                if($checkRelationshipArray) $results->load($string);
            }
        }

        // Append the full name to each model
        $results->getCollection()->each(function ($medicalHistory) {
            if ($medicalHistory->inmate) {
                $medicalHistory->prisoner_fullname = trim("{$medicalHistory->inmate->surname} {$medicalHistory->inmate->first_name} {$medicalHistory->inmate->other_names}");
                unset($medicalHistory->inmate);
            }
        });

        if($request->count){
            $results = $results->total();
        }

        return response()->json(['data' => $results]);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        return view('healthmanagement::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        // Authorization handled by middleware

        $request->validate([
            'prisoner_no' => 'required|exists:inmates_all_class_register,prisoner_no',
            'condition' => 'required|string',
            'diagnosis_date' => 'nullable|date',
            'medical_status' => 'required|string|in:critical,severe,moderate,stable,mild,recovering,resolved',
            'notes' => 'nullable|string',
        ]);

        $medicalHistory = new MedicalHistory($request->all());
        $medicalHistory->save();

        return response()->json([
            'message' => 'Medical history record created successfully',
            'data' => $medicalHistory
        ], 201);
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Authorization handled by middleware

        $medicalHistory = MedicalHistory::findOrFail($id);
        return response()->json([
            'data' => $medicalHistory
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        return view('healthmanagement::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        // Authorization handled by middleware

        $request->validate([
            'prisoner_no' => 'required|exists:inmates_all_class_register,prisoner_no',
            'condition' => 'required|string',
            'diagnosis_date' => 'nullable|date',
            'medical_status' => 'required|string|in:critical,severe,moderate,stable,mild,recovering,resolved',
            'notes' => 'nullable|string',
            'status' => 'nullable|integer|in:0,1',
        ]);

        $medicalHistory = MedicalHistory::findOrFail($id);
        $medicalHistory->update($request->all());

        return response()->json([
            'message' => 'Medical history record updated successfully',
            'data' => $medicalHistory
        ]);
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        // Authorization handled by middleware

        $medicalHistory = MedicalHistory::findOrFail($id);
        $medicalHistory->delete();

        return response()->json([
            'message' => 'Medical history record deleted successfully'
        ]);
    }

    /**
     * Get all medical history records for a specific prisoner
     * @param string $prisoner_no
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHistoryByPrisoner($prisoner_no)
    {
        // Authorization handled by middleware

        $history = MedicalHistory::where('prisoner_no', $prisoner_no)
            ->orderBy('diagnosis_date', 'desc')
            ->get();

        if ($history->isEmpty()) {
            return response()->json([
                'message' => 'No medical history found for this prisoner',
                'data' => []
            ]);
        }

        return response()->json([
            'data' => $history
        ]);
    }
}
