<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Services\PermissionCacheService;



class Staff extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'serviceNo',
        'password',
        'last_login'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'last_login' => 'datetime',
        'status' => 'boolean',
    ];

    /**
     * Get the account type that owns the staff.
     */
    public function accountType(): BelongsTo
    {
        return $this->belongsTo(AccountType::class, 'account_type');
    }

    /**
     * Get the state that owns the staff.
     */
    public function assignedState(): BelongsTo
    {
        return $this->belongsTo(States::class, 'assigned_state');
    }

    /**
     * Get the prison that owns the staff.
     */
    public function assignedPrison(): BelongsTo
    {
        return $this->belongsTo(Prison::class, 'prison');
    }

    /**
     * The roles that belong to the staff.
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'staff_roles')
                    ->withPivot(['state_id', 'prison_id', 'assigned_at', 'expires_at', 'is_active'])
                    ->withTimestamps();
    }

    /**
     * Get active roles for the staff.
     */
    public function activeRoles(): BelongsToMany
    {
        return $this->roles()->wherePivot('is_active', true)
                           ->where(function ($query) {
                               $query->whereNull('staff_roles.expires_at')
                                     ->orWhere('staff_roles.expires_at', '>', now());
                           });
    }

    /**
     * Check if staff has a specific role.
     */
    public function hasRole(string $roleName, ?int $stateId = null, ?int $prisonId = null): bool
    {
        $query = $this->activeRoles()->where('roles.name', $roleName);

        if ($stateId) {
            $query->where(function ($q) use ($stateId) {
                $q->where('staff_roles.state_id', $stateId)
                  ->orWhere('roles.scope', 'system');
            });
        }

        if ($prisonId) {
            $query->where(function ($q) use ($prisonId) {
                $q->where('staff_roles.prison_id', $prisonId)
                  ->orWhere('roles.scope', 'state')
                  ->orWhere('roles.scope', 'system');
            });
        }

        return $query->exists();
    }

    /**
     * Check if staff has any of the given roles.
     */
    public function hasAnyRole(array $roles, ?int $stateId = null, ?int $prisonId = null): bool
    {
        foreach ($roles as $role) {
            if ($this->hasRole($role, $stateId, $prisonId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if staff has a specific permission (cached).
     */
    public function hasPermission(string $permission, ?int $stateId = null, ?int $prisonId = null): bool
    {
        return $this->getPermissionCacheService()->staffHasPermission($this, $permission, $stateId, $prisonId);
    }

    /**
     * Check if staff has any of the given permissions (cached batch operation).
     */
    public function hasAnyPermission(array $permissions, ?int $stateId = null, ?int $prisonId = null): bool
    {
        return $this->getPermissionCacheService()->staffHasAnyPermission($this, $permissions, $stateId, $prisonId);
    }

    /**
     * Check if staff has all of the given permissions (cached batch operation).
     */
    public function hasAllPermissions(array $permissions, ?int $stateId = null, ?int $prisonId = null): bool
    {
        return $this->getPermissionCacheService()->staffHasAllPermissions($this, $permissions, $stateId, $prisonId);
    }

    /**
     * Check if staff has access to a specific state.
     */
    public function hasStateAccess(int $stateId): bool
    {
        return $this->assigned_state == $stateId ||
               $this->hasRole('super-admin') ||
               $this->activeRoles()->where('roles.scope', 'system')->exists();
    }

    /**
     * Check if staff has access to a specific prison.
     */
    public function hasPrisonAccess(int $prisonId): bool
    {
        return $this->prison == $prisonId ||
               $this->hasRole('super-admin') ||
               $this->activeRoles()->whereIn('roles.scope', ['system', 'state'])->exists();
    }

    /**
     * Get all permissions for the staff (cached).
     */
    public function getAllPermissions(?int $stateId = null, ?int $prisonId = null): array
    {
        return $this->getPermissionCacheService()->getStaffPermissions($this, $stateId, $prisonId)->toArray();
    }

    /**
     * Get all roles for the staff (cached).
     */
    public function getAllRoles()
    {
        return $this->getPermissionCacheService()->getStaffRoles($this);
    }

    /**
     * Clear permission cache for this staff member.
     */
    public function clearPermissionCache(): void
    {
        $this->getPermissionCacheService()->clearStaffCache($this);
    }

    /**
     * Warm permission cache for this staff member.
     */
    public function warmPermissionCache(): void
    {
        $this->getPermissionCacheService()->warmCache($this);
    }

    /**
     * Get the permission cache service instance.
     */
    protected function getPermissionCacheService(): PermissionCacheService
    {
        return app(PermissionCacheService::class);
    }



    /**
     * Boot method to register observer and handle role changes.
     */
    protected static function boot()
    {
        parent::boot();

        static::updated(function ($staff) {
            $staff->clearPermissionCache();
        });
    }
}
