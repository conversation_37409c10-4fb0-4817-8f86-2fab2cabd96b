<?php

namespace Modules\HealthManagement\Http\Controllers\Api\v1;

use App\Traits\Common;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

use Modules\HealthManagement\Entities\MedicalAppointments;

class MedicalAppointmentsController extends Controller
{
    use Common;

    public function __construct()
    {
        $this->middleware('authorize:view-medical-appointments')->only(['index', 'show', 'getAppointmentsByPrisoner']);
        $this->middleware('authorize:create-medical-appointments')->only(['store']);
        $this->middleware('authorize:update-medical-appointments')->only(['update']);
        $this->middleware('authorize:delete-medical-appointments')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(Request $request)
    {
        // Authorization handled by middleware

        $noIncludeArray = array('count', 'page', 'include','paginate', 'start_date', 'end_date', 'status', 'search');
        $relations=array();
        $includeString = explode(',',$request->include);
        $query = $request->query();
        $paginate = $request->paginate? $request->paginate:20;

        //// This method will do a custom query based on query string to filter out the count value
        $filteredQuery = $this->filterCustomQuery($query, $noIncludeArray);

        $appointments = MedicalAppointments::orderBy('appointment_date','desc');

        $appointments->where($filteredQuery);

        if ($request->has('start_date') && $request->has('end_date')) {
            $appointments->whereBetween('appointment_date', [$request->start_date, $request->end_date]);
        } else if ($request->has('start_date')) {
            $appointments->where('appointment_date', '>=', $request->start_date);
        } else if ($request->has('end_date')) {
            $appointments->where('appointment_date', '<=', $request->end_date);
        }

        if ($request->has('status')) {
            $appointments->where('appointment_status', $request->status);
        }

        if ($request->has('search')) {
            $searchTerm = $request->search;
            $appointments->where(function($query) use ($searchTerm) {
                $query->where('prisoner_no', 'like', "%{$searchTerm}%");
            });
        }

        $results = $appointments->paginate($paginate);

        if($request->include){
            foreach($includeString as $string){
                $checkRelationshipArray = $this->shouldIncludeRelation($relations, $string);
                if($checkRelationshipArray) $results->load($string);
            }
        }

        // Append the full name to each model
        $results->getCollection()->each(function ($medicalAppointment) {
            if ($medicalAppointment->inmate) {
                $medicalAppointment->prisoner_fullname = trim("{$medicalAppointment->inmate->surname} {$medicalAppointment->inmate->first_name} {$medicalAppointment->inmate->other_names}");
                unset($medicalAppointment->inmate);
            }
        });

        if($request->count){
            $results = $results->total();
        }

        return response()->json(['data' => $results]);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        return view('healthmanagement::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        Gate::authorize('create', MedicalAppointments::class);

        $request->validate([
            'prisoner_no' => 'required',
            'appointment_status' => 'required',
            'appointment_date' => 'required',
        ]);
        $appointment = new MedicalAppointments($request->all());
        $appointment->save();
        return response()->json(['message' => 'New medical appointment saved']);
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Authorization handled by middleware

        $appointment = MedicalAppointments::findOrFail($id);
        return response()->json([
            'data' => $appointment
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        return view('healthmanagement::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        // Authorization handled by middleware

        $request->validate([
            'prisoner_no' => 'required',
            'appointment_status' => 'required',
            'appointment_date' => 'required',
        ]);

        $appointment = MedicalAppointments::findOrFail($id);
        $appointment->update($request->all());

        return response()->json([
            'message' => 'Medical appointment updated successfully',
            'data' => $appointment
        ]);
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        // Authorization handled by middleware

        $appointment = MedicalAppointments::findOrFail($id);
        $appointment->delete();

        return response()->json([
            'message' => 'Medical appointment deleted successfully'
        ]);
    }

     /**
     * Get all medical appointments for a specific prisoner
     * @param string $prisoner_no
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAppointmentsByPrisoner($prisoner_no)
    {
        // Authorization handled by middleware

        $appointments = MedicalAppointments::where('prisoner_no', $prisoner_no)
            ->orderBy('appointment_date', 'desc')
            ->get();

        if ($appointments->isEmpty()) {
            return response()->json([
                'message' => 'No appointments found for this prisoner',
                'data' => []
            ]);
        }

        return response()->json([
            'data' => $appointments
        ]);
    }
}
